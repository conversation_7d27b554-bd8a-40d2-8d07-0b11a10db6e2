from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.orm import Session, joinedload
from app import crud, models, schemas
from app.api import deps
from pydantic import BaseModel
from fastapi.responses import JSONResponse
import logging
import uuid
from datetime import datetime

class BulkDeleteRequest(BaseModel):
    listing_ids: List[int]

class ApprovalRequest(BaseModel):
    action: str  # "approve" or "reject"
    rejection_reason: Optional[str] = None

router = APIRouter()

logger = logging.getLogger(__name__)


@router.get("/", response_model=dict)
def get_listings(
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
    cities: Optional[List[str]] = Query(None),
    neighborhoods: Optional[List[str]] = Query(None),
    property_types: Optional[List[str]] = Query(None),
    property_category: Optional[str] = None,
    transaction_type: Optional[str] = None,
    commercial_type: Optional[str] = None,
    land_type: Optional[str] = None,
    price_min: Optional[float] = None,
    price_max: Optional[float] = None,
    size_min: Optional[float] = None,
    size_max: Optional[float] = None,
    rooms_min: Optional[int] = None,
    rooms_max: Optional[int] = None,
    bathrooms_min: Optional[int] = None,
    bathrooms_max: Optional[int] = None,
    has_elevator: Optional[bool] = None,
    has_parking: Optional[bool] = None,
    parking_spaces_min: Optional[int] = None,
    has_balcony: Optional[bool] = None,
    has_terrace: Optional[bool] = None,
    has_garden: Optional[bool] = None,
    is_furnished: Optional[bool] = None,
    has_storage: Optional[bool] = None,
    is_serviced: Optional[bool] = None,
    is_residential_project: Optional[bool] = None,
    website_source: Optional[str] = None,
    page: int = 1,
    limit: int = 10,
    sort_by: str = "created_at",
    sort_order: str = "desc",
) -> Any:
    """
    Retrieve listings with filters.
    """
    # Create filters object
    filters = schemas.ListingFilters(
        cities=cities,
        neighborhoods=neighborhoods,
        property_types=property_types,
        property_category=property_category,
        transaction_type=transaction_type,
        commercial_type=commercial_type,
        land_type=land_type,
        price_min=price_min,
        price_max=price_max,
        size_min=size_min,
        size_max=size_max,
        rooms_min=rooms_min,
        rooms_max=rooms_max,
        bathrooms_min=bathrooms_min,
        bathrooms_max=bathrooms_max,
        has_elevator=has_elevator,
        has_parking=has_parking,
        parking_spaces_min=parking_spaces_min,
        has_balcony=has_balcony,
        has_terrace=has_terrace,
        has_garden=has_garden,
        is_furnished=is_furnished,
        has_storage=has_storage,
        is_serviced=is_serviced,
        is_residential_project=is_residential_project,
        website_source=website_source,
        page=page,
        limit=limit,
        sort_by=sort_by,
        sort_order=sort_order,
    )
    
    # Get listings with pagination
    listings, total = crud.listing.get_multi_by_filters(db, filters=filters)
    
    # Prepare listings with views and bookmarks using batch operations for optimal performance
    prepared_listings_data = deps.prepare_listings_with_views_and_bookmarks_batch(
        listings=listings,
        db=db,
        current_user=current_user
    )
    
    # Convert to Pydantic models
    prepared_listings = [schemas.Listing.model_validate(listing_dict) for listing_dict in prepared_listings_data]
    
    return {
        "items": prepared_listings,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": (total // limit) + (1 if total % limit > 0 else 0),
    }


@router.get("/best-deals", response_model=List[schemas.Listing])
def get_best_deals(
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
    limit: int = Query(4, ge=1, le=20),
) -> Any:
    """
    Retrieve the most popular listings (most viewed in past 24h).
    Falls back to cheapest listings if no view data is available.
    """
    from app.crud.crud_listing_view import listing_view
    
    # Try to get popular listings based on views first
    popular_listings = listing_view.get_popular_listings(db=db, hours=24, limit=limit)
    
    # If we don't have enough popular listings (less than half requested), 
    # fill the rest with best price deals
    if len(popular_listings) < limit // 2:
        # Get additional listings sorted by price
        additional_needed = limit - len(popular_listings)
        popular_listing_ids = [l.id for l in popular_listings]
        
        price_deals = crud.listing.get_best_deals(db=db, limit=additional_needed * 2)
        # Filter out already included listings
        price_deals = [l for l in price_deals if l.id not in popular_listing_ids][:additional_needed]
        
        popular_listings.extend(price_deals)
    
    # Convert SQLAlchemy models to Pydantic schemas with view counts and bookmark status
    final_listings = popular_listings[:limit]
    
    # Prepare listings with views and bookmarks using batch operations for optimal performance
    prepared_listings_data = deps.prepare_listings_with_views_and_bookmarks_batch(
        listings=final_listings,
        db=db,
        current_user=current_user
    )
    
    # Convert to Pydantic models
    prepared_listings = [schemas.Listing.model_validate(listing_dict) for listing_dict in prepared_listings_data]
    
    return prepared_listings


@router.get("/cities", response_model=List[dict])
def get_cities(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve all cities with listing counts.
    """
    return crud.listing.get_cities(db=db)


@router.get("/top-cities", response_model=List[dict])
def get_top_cities(
    limit: int = Query(8, ge=1, le=20, description="Number of top cities to return"),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve top cities by listing count with random images.
    Optimized endpoint for principales villes section.
    """
    return crud.listing.get_top_cities(db=db, limit=limit)


@router.get("/all-cities-sorted", response_model=List[dict])
def get_all_cities_sorted(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve all cities sorted alphabetically for the cities page.
    This endpoint is specifically for the cities page navigation.
    """
    return crud.listing.get_all_cities_sorted(db=db)


@router.get("/property-types", response_model=List[dict])
def get_property_types(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve all property types with listing counts.
    """
    return crud.listing.get_property_types(db=db)


@router.get("/property-categories", response_model=List[Dict[str, Any]])
def get_property_categories(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve all property categories with listing counts.
    """
    return crud.listing.get_property_categories(db=db)


@router.get("/neighborhoods", response_model=List[Dict[str, Any]])
def get_neighborhoods(
    db: Session = Depends(deps.get_db),
    city: Optional[str] = None,
) -> Any:
    """
    Retrieve all neighborhoods with listing counts, optionally filtered by city.
    """
    if city:
        return crud.listing.get_neighborhoods_by_city(db=db, city=city)
    else:
        return crud.listing.get_neighborhoods(db=db)


@router.get("/property-types-by-city", response_model=List[dict])
def get_property_types_by_city(
    db: Session = Depends(deps.get_db),
    city: Optional[str] = None,
    neighborhoods: Optional[List[str]] = Query(None),
) -> Any:
    """
    Retrieve property types with listing counts, filtered by city and neighborhoods.
    """
    return crud.listing.get_property_types_by_city(db=db, city=city, neighborhoods=neighborhoods)


@router.get("/by-city/{city}", response_model=dict)
def get_listings_by_city(
    *,
    db: Session = Depends(deps.get_db),
    city: str,
    page: int = 1,
    limit: int = 10,
) -> Any:
    """
    Retrieve listings for a specific city with pagination.
    """
    listings, total, total_pages = crud.listing.get_by_city(db=db, city=city, limit=limit, page=page)
    
    # Convert SQLAlchemy models to Pydantic schemas with view counts using batch operations
    prepared_listings_data = deps.prepare_listings_with_views_batch(listings=listings, db=db)
    listing_schemas = [schemas.Listing.model_validate(listing_dict) for listing_dict in prepared_listings_data]
    
    return {
        "items": listing_schemas,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": total_pages,
    }


@router.get("/by-type/{property_type}", response_model=dict)
def get_listings_by_type(
    *,
    db: Session = Depends(deps.get_db),
    property_type: str,
    page: int = 1,
    limit: int = 10,
) -> Any:
    """
    Retrieve listings for a specific property type with pagination.
    """
    listings, total, total_pages = crud.listing.get_by_type(db=db, property_type=property_type, limit=limit, page=page)
    
    # Convert SQLAlchemy models to Pydantic schemas with view counts using batch operations
    prepared_listings_data = deps.prepare_listings_with_views_batch(listings=listings, db=db)
    listing_schemas = [schemas.Listing.model_validate(listing_dict) for listing_dict in prepared_listings_data]
    
    return {
        "items": listing_schemas,
        "total": total,
        "page": page,
        "limit": limit,
        "pages": total_pages,
    }


@router.get("/neighborhoods-by-city/{city}", response_model=List[Dict[str, Any]])
def get_neighborhoods_by_city(
    *,
    db: Session = Depends(deps.get_db),
    city: str,
) -> Any:
    """
    Retrieve neighborhoods for a specific city with listing counts.
    """
    return crud.listing.get_neighborhoods_by_city(db=db, city=city)


@router.get("/property-types-by-category", response_model=Dict[str, List[Dict[str, Any]]])
def get_property_types_by_category(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Retrieve property types grouped by their categories with counts.
    """
    return crud.listing.get_property_types_by_category(db=db)


# User Listing Management Endpoints

@router.post("/user-listing", response_model=schemas.Listing, status_code=status.HTTP_201_CREATED)
def create_user_listing(
    *,
    db: Session = Depends(deps.get_db),
    listing_in: schemas.ListingCreate,
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Create a new user listing.
    """
    try:
        # Generate unique URL if not provided
        if not listing_in.url:
            unique_id = str(uuid.uuid4())[:8]
            listing_in.url = f"user-listing-{unique_id}"
        
        # Calculate price per sqm
        price_per_sqm = listing_in.price / listing_in.size_sqm if listing_in.size_sqm > 0 else None
        
        # Create listing data - frontend now only sends valid fields
        listing_data = {
            **listing_in.model_dump(exclude_none=True),
            "user_id": current_user.id,
            "price_per_sqm": price_per_sqm,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            # User-generated listings require approval
            "approval_status": "pending",
            "is_user_generated": True,
            # Generate placeholder URL if not provided
            "url": listing_in.url or f"/property/user-listing-{uuid.uuid4().hex[:8]}",
            # Set scraper to None for user-generated listings
            "scraper": None,
        }
        
        # Create listing
        listing = crud.listing.create(db=db, obj_in=listing_data)
        
        # Convert to Pydantic schema
        listing_dict = deps.prepare_listing_for_response(listing, current_user)
        return schemas.Listing.model_validate(listing_dict)
        
    except Exception as e:
        logger.error(f"Error creating user listing: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not create listing: {str(e)}"
        )


@router.get("/my-listings", response_model=dict)
def get_my_listings(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
) -> Any:
    """
    Get current user's listings with pagination.
    """
    try:
        # Get user's listings with pagination, load owner relationship
        skip = (page - 1) * limit
        listings = db.query(models.Listing).filter(
            models.Listing.user_id == current_user.id
        ).options(joinedload(models.Listing.owner)).order_by(models.Listing.created_at.desc()).offset(skip).limit(limit).all()
        
        # Get total count
        total = db.query(models.Listing).filter(
            models.Listing.user_id == current_user.id
        ).count()
        
        # Calculate pages
        pages = (total // limit) + (1 if total % limit > 0 else 0)
        
        # Prepare listings with views and bookmarks
        prepared_listings_data = deps.prepare_listings_with_views_and_bookmarks_batch(
            listings=listings,
            db=db,
            current_user=current_user
        )
        
        # Convert to Pydantic models
        listing_schemas = [schemas.Listing.model_validate(listing_dict) for listing_dict in prepared_listings_data]
        
        return {
            "items": listing_schemas,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages,
        }
        
    except Exception as e:
        logger.error(f"Error getting user listings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not retrieve listings: {str(e)}"
        )


@router.get("/my-listings/stats", response_model=dict)
def get_my_listings_stats(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Get statistics for current user's listings.
    """
    try:
        from sqlalchemy import func
        
        # Get total listings count
        total_listings = db.query(models.Listing).filter(
            models.Listing.user_id == current_user.id
        ).count()
        
        # Get total views across all user listings (count unique IP addresses per listing)
        total_views = db.query(func.count(func.distinct(models.ListingView.ip_address))).join(
            models.Listing
        ).filter(models.Listing.user_id == current_user.id).scalar() or 0
        
        # Get total bookmarks across all user listings
        total_bookmarks = db.query(models.Bookmark).join(
            models.Listing
        ).filter(models.Listing.user_id == current_user.id).count()
        
        # Get average price
        avg_price = db.query(func.avg(models.Listing.price)).filter(
            models.Listing.user_id == current_user.id
        ).scalar() or 0.0
        
        # Get listings by type
        listings_by_type = db.query(
            models.Listing.property_type,
            func.count(models.Listing.id).label('count')
        ).filter(
            models.Listing.user_id == current_user.id
        ).group_by(models.Listing.property_type).all()
        
        return {
            "total_listings": total_listings,
            "total_views": int(total_views),
            "total_bookmarks": total_bookmarks,
            "average_price": float(avg_price),
            "listings_by_type": [
                {"property_type": pt, "count": count} 
                for pt, count in listings_by_type
            ],
        }
        
    except Exception as e:
        logger.error(f"Error getting user listing stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not retrieve statistics: {str(e)}"
        )


@router.delete("/my-listings/bulk", response_model=dict)
def bulk_delete_my_listings(
    *,
    db: Session = Depends(deps.get_db),
    request: BulkDeleteRequest,
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Bulk delete current user's listings.
    """
    listing_ids = request.listing_ids
    if not listing_ids:
        raise HTTPException(status_code=400, detail="No listing IDs provided")
    
    try:
        # Get listings to verify ownership
        listings = db.query(models.Listing).filter(
            models.Listing.id.in_(listing_ids),
            models.Listing.user_id == current_user.id
        ).all()
        
        found_ids = [listing.id for listing in listings]
        not_found_ids = [id for id in listing_ids if id not in found_ids]
        
        if not_found_ids:
            raise HTTPException(
                status_code=404, 
                detail=f"Listings not found or not owned by you: {not_found_ids}"
            )
        
        # Delete listings
        deleted_count = 0
        for listing in listings:
            crud.listing.remove(db=db, id=listing.id)
            deleted_count += 1
        
        return {
            "message": f"Successfully deleted {deleted_count} listings",
            "deleted_count": deleted_count,
            "deleted_ids": found_ids,
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error bulk deleting listings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not delete listings: {str(e)}"
        ) 


# This route MUST come last as it has a path parameter that can match anything
@router.get("/{listing_id}", response_model=schemas.Listing)
def get_listing(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
    listing_id: int,
) -> Any:
    """
    Get listing by ID. Only approved listings are accessible to public.
    Users can access their own listings regardless of approval status.
    Admins can access all listings.
    """
    listing = crud.listing.get(db=db, id=listing_id)
    if not listing:
        raise HTTPException(status_code=404, detail="Listing not found")
    
    # Check access permissions based on approval status
    if listing.approval_status != models.listing.ApprovalStatus.approved:
        # Non-approved listings are only accessible to:
        # 1. The owner of the listing
        # 2. Admin users
        if not current_user:
            raise HTTPException(status_code=404, detail="Listing not found")
        
        is_owner = listing.user_id and listing.user_id == current_user.id
        is_admin = current_user.is_superuser
        
        if not (is_owner or is_admin):
            raise HTTPException(status_code=404, detail="Listing not found")
    
    # Convert SQLAlchemy model to Pydantic schema with view counts and bookmark status using batch operations
    prepared_listings_data = deps.prepare_listings_with_views_and_bookmarks_batch(
        listings=[listing],
        db=db,
        current_user=current_user
    )
    
    return schemas.Listing.model_validate(prepared_listings_data[0])


@router.put("/{listing_id}", response_model=schemas.Listing)
def update_listing(
    *,
    db: Session = Depends(deps.get_db),
    listing_id: int,
    listing_in: schemas.ListingUpdate,
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Update a listing. Only owner or admin can update.
    """
    # Get listing
    listing = crud.listing.get(db=db, id=listing_id)
    if not listing:
        raise HTTPException(status_code=404, detail="Listing not found")
    
    # Check permission
    if listing.user_id and listing.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="You can only modify your own listings")
    
    try:
        # Create update data - frontend now only sends valid fields
        update_data = {
            **listing_in.model_dump(exclude_none=True),
            "updated_at": datetime.utcnow(),
        }
        
        # Calculate price per sqm if both price and size are being updated or if one is updated and the other exists
        price = listing_in.price if listing_in.price is not None else listing.price
        size_sqm = listing_in.size_sqm if listing_in.size_sqm is not None else listing.size_sqm
        
        if price is not None and size_sqm is not None and size_sqm > 0:
            update_data["price_per_sqm"] = price / size_sqm
        
        # Update listing
        listing = crud.listing.update(db=db, db_obj=listing, obj_in=update_data)
        
        # Convert to Pydantic schema
        listing_dict = deps.prepare_listing_for_response(listing, current_user)
        return schemas.Listing.model_validate(listing_dict)
        
    except Exception as e:
        logger.error(f"Error updating listing: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not update listing: {str(e)}"
        )


@router.delete("/{listing_id}", response_model=dict)
def delete_listing(
    *,
    db: Session = Depends(deps.get_db),
    listing_id: int,
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Delete a listing. Only owner or admin can delete.
    """
    # Get listing
    listing = crud.listing.get(db=db, id=listing_id)
    if not listing:
        raise HTTPException(status_code=404, detail="Listing not found")
    
    # Check permission
    if listing.user_id and listing.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="You can only delete your own listings")
    
    try:
        # Delete listing
        crud.listing.remove(db=db, id=listing_id)
        
        return {"message": f"Listing {listing_id} has been successfully deleted"}
        
    except Exception as e:
        logger.error(f"Error deleting listing: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not delete listing: {str(e)}"
        ) 


# Admin Endpoints for Approval System
@router.get("/admin/pending", response_model=dict)
def get_pending_listings(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
) -> Any:
    """
    Get pending listings for admin approval. Admin only.
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    try:
        listings, total = crud.listing.get_pending_listings(db=db, page=page, limit=limit)
        
        # Prepare listings with views and bookmarks using batch operations
        prepared_listings_data = deps.prepare_listings_with_views_and_bookmarks_batch(
            listings=listings,
            db=db,
            current_user=current_user
        )
        
        # Convert to Pydantic models
        prepared_listings = [schemas.Listing.model_validate(listing_dict) for listing_dict in prepared_listings_data]
        
        return {
            "items": prepared_listings,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": (total // limit) + (1 if total % limit > 0 else 0),
        }
        
    except Exception as e:
        logger.error(f"Error getting pending listings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not retrieve pending listings: {str(e)}"
        )


@router.post("/admin/approve/{listing_id}", response_model=schemas.Listing)
def approve_listing(
    *,
    db: Session = Depends(deps.get_db),
    listing_id: int,
    request: ApprovalRequest,
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Approve or reject a pending listing. Admin only.
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="Admin access required")
    
    try:
        if request.action == "approve":
            listing = crud.listing.approve_listing(
                db=db, 
                listing_id=listing_id, 
                approved_by=current_user.id
            )
        elif request.action == "reject":
            if not request.rejection_reason:
                raise HTTPException(status_code=400, detail="Rejection reason is required")
            listing = crud.listing.reject_listing(
                db=db, 
                listing_id=listing_id, 
                rejection_reason=request.rejection_reason
            )
        else:
            raise HTTPException(status_code=400, detail="Invalid action. Use 'approve' or 'reject'")
        
        if not listing:
            raise HTTPException(status_code=404, detail="Listing not found")
        
        # Convert to Pydantic schema
        listing_dict = deps.prepare_listing_for_response(listing, current_user)
        return schemas.Listing.model_validate(listing_dict)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing listing approval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not process approval: {str(e)}"
        )