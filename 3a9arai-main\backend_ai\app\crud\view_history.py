from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, func, and_, or_

from app.crud.base import CRUDBase
from app.models.view_history import ViewHistory
from app.models.listing import Listing
from app.schemas.view_history import ViewH<PERSON>oryC<PERSON>, ViewHistoryUpdate
from app.services.view_cache import view_cache


class CRUDViewHistory(CRUDBase[ViewHistory, ViewHistoryCreate, ViewHistoryUpdate]):
    def create_or_update_view(
        self, db: Session, *, user_id: int, listing_id: int
    ) -> ViewHistory:
        """
        Create a new view history entry or update existing one.
        If user has already viewed this listing, update the timestamp.
        Uses Redis cache for performance.
        """
        # Check if user has already viewed this listing
        existing_view = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id,
            ViewHistory.listing_id == listing_id
        ).first()
        
        if existing_view:
            # Update the timestamp
            existing_view.viewed_at = func.now()
            db.commit()
            db.refresh(existing_view)
            
            # Update cache
            view_cache.add_to_user_history(user_id, listing_id)
            
            return existing_view
        else:
            # Create new view history entry
            obj_in = ViewHistoryCreate(user_id=user_id, listing_id=listing_id)
            new_view = self.create(db=db, obj_in=obj_in)
            
            # Maintain max 100 entries per user
            self._maintain_max_entries(db, user_id)
            
            # Update cache
            view_cache.add_to_user_history(user_id, listing_id)
            
            return new_view
    
    def _maintain_max_entries(self, db: Session, user_id: int, max_entries: int = 100):
        """
        Keep only the most recent max_entries for a user.
        """
        # Count total entries for user
        total_count = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).count()
        
        if total_count > max_entries:
            # Get the oldest entries to delete
            entries_to_delete = total_count - max_entries
            oldest_entries = db.query(ViewHistory).filter(
                ViewHistory.user_id == user_id
            ).order_by(ViewHistory.viewed_at.asc()).limit(entries_to_delete).all()
            
            # Delete oldest entries
            for entry in oldest_entries:
                db.delete(entry)
            
            db.commit()
    
    def get_user_history(
        self, 
        db: Session, 
        *, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 20
    ) -> List[ViewHistory]:
        """
        Get user's view history with pagination, ordered by most recent first.
        Uses Redis cache for performance.
        """
        # Try to get from cache first
        cached_listing_ids = view_cache.get_user_history_listing_ids(user_id)
        
        if cached_listing_ids is not None:
            # Use cached data for pagination
            paginated_ids = cached_listing_ids[skip:skip + limit]
            
            if paginated_ids:
                # Get the actual ViewHistory objects for these listing IDs
                view_history_objects = db.query(ViewHistory).filter(
                    ViewHistory.user_id == user_id,
                    ViewHistory.listing_id.in_(paginated_ids)
                ).options(
                    joinedload(ViewHistory.listing)
                ).all()
                
                # Sort to match the order of paginated_ids
                history_dict = {vh.listing_id: vh for vh in view_history_objects}
                ordered_history = [history_dict[listing_id] for listing_id in paginated_ids 
                                 if listing_id in history_dict]
                
                return ordered_history
            else:
                return []
        
        # Cache miss - get from database and update cache
        all_history = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).options(
            joinedload(ViewHistory.listing)
        ).order_by(
            desc(ViewHistory.viewed_at)
        ).all()
        
        # Cache all listing IDs for future requests
        all_listing_ids = [vh.listing_id for vh in all_history]
        view_cache.set_user_history_listing_ids(user_id, all_listing_ids)
        
        # Return paginated results
        return all_history[skip:skip + limit]
    
    def get_user_history_count(self, db: Session, *, user_id: int) -> int:
        """
        Get total count of user's view history entries.
        Uses Redis cache for performance.
        """
        # Check cache first
        cached_count = view_cache.get_user_history_count(user_id)
        if cached_count is not None:
            return cached_count
        
        # Cache miss - get from database
        count = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).count()
        
        # Cache the count (this will be updated when history is cached)
        return count
    
    def delete_user_history(self, db: Session, *, user_id: int) -> int:
        """
        Delete all view history for a user.
        Returns the number of deleted entries.
        Uses Redis cache invalidation.
        """
        deleted_count = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).count()
        
        db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).delete()
        
        db.commit()
        
        # Invalidate user's cache
        view_cache.invalidate_user_history_cache(user_id)
        
        return deleted_count

    def get_suggested_properties(
        self, 
        db: Session, 
        *, 
        user_id: int, 
        limit: int = 6
    ) -> List[Listing]:
        """
        Get suggested properties based on user's viewing history.
        Analyzes user preferences from their view history and suggests similar properties.
        """
        # Get user's recent view history (last 50 views to analyze preferences)
        recent_views = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).options(
            joinedload(ViewHistory.listing)
        ).order_by(
            desc(ViewHistory.viewed_at)
        ).limit(50).all()
        
        if not recent_views:
            # If no history, return best deals
            return db.query(Listing).filter(
                Listing.price > 0
            ).order_by(
                Listing.price.asc()
            ).limit(limit).all()
        
        # Extract user preferences from viewing history
        viewed_listing_ids = [view.listing_id for view in recent_views]
        
        # Analyze preferences
        property_types = []
        property_categories = []
        cities = []
        neighborhoods = []
        price_ranges = []
        
        for view in recent_views:
            listing = view.listing
            if listing:
                if listing.property_type:
                    property_types.append(listing.property_type)
                if listing.property_category:
                    property_categories.append(listing.property_category)
                if listing.city:
                    cities.append(listing.city)
                if listing.neighborhood:
                    neighborhoods.append((listing.city, listing.neighborhood))
                if listing.price and listing.price > 0:
                    price_ranges.append(listing.price)
        
        # Get most common preferences
        from collections import Counter
        
        # Most viewed property types (top 3)
        common_types = [item[0] for item in Counter(property_types).most_common(3)]
        
        # Most viewed categories (top 2)
        common_categories = [item[0] for item in Counter(property_categories).most_common(2)]
        
        # Most viewed city (only 1) and neighborhoods within that city
        most_visited_city = Counter(cities).most_common(1)
        most_visited_city = most_visited_city[0][0] if most_visited_city else None
        
        common_neighborhoods = []
        if most_visited_city:
            city_neighborhoods = [neighborhood for city, neighborhood in neighborhoods if city == most_visited_city]
            common_neighborhoods = [item[0] for item in Counter(city_neighborhoods).most_common(3)]
        
        # Calculate price range (25th to 75th percentile)
        if price_ranges:
            price_ranges.sort()
            q1_index = len(price_ranges) // 4
            q3_index = (3 * len(price_ranges)) // 4
            min_price = price_ranges[q1_index] * 0.8  # 20% below Q1
            max_price = price_ranges[q3_index] * 1.2  # 20% above Q3
        else:
            min_price = max_price = None
        
        # Build query for suggestions
        query = db.query(Listing).filter(
            ~Listing.id.in_(viewed_listing_ids),  # Exclude already viewed
            Listing.price > 0  # Only listings with price
        )
        
        # Create conditions for matching preferences
        conditions = []
        
        # Property type matching
        if common_types:
            conditions.append(Listing.property_type.in_(common_types))
        
        # Category matching
        if common_categories:
            conditions.append(Listing.property_category.in_(common_categories))
        
        # City and neighborhood matching
        if most_visited_city:
            if common_neighborhoods:
                # If we have specific neighborhoods, prioritize those within the city
                conditions.append(and_(
                    Listing.city == most_visited_city,
                    Listing.neighborhood.in_(common_neighborhoods)
                ))
            else:
                # If no specific neighborhoods, just match the city
                conditions.append(Listing.city == most_visited_city)
        
        # Price range matching
        if min_price and max_price:
            conditions.append(and_(
                Listing.price >= min_price,
                Listing.price <= max_price
            ))
        
        # Apply conditions with OR logic (property matches any preference)
        if conditions:
            query = query.filter(or_(*conditions))
        
        # Order by most recent and get suggestions
        suggestions = query.order_by(
            desc(Listing.date_posted)
        ).limit(limit * 2).all()  # Get more to have variety
        
        # If we don't have enough suggestions, add some best deals
        if len(suggestions) < limit:
            additional_needed = limit - len(suggestions)
            additional_listings = db.query(Listing).filter(
                ~Listing.id.in_(viewed_listing_ids + [s.id for s in suggestions]),
                Listing.price > 0
            ).order_by(
                Listing.price.asc()
            ).limit(additional_needed).all()
            
            suggestions.extend(additional_listings)
        
        return suggestions[:limit]

    def get_suggestion_criteria(
        self, 
        db: Session, 
        *, 
        user_id: int
    ) -> dict:
        """
        Get the criteria/filters used to generate property suggestions based on user's viewing history.
        Returns the same analysis used by get_suggested_properties but only the criteria, not the listings.
        """
        # Get user's recent view history (last 50 views to analyze preferences)
        recent_views = db.query(ViewHistory).filter(
            ViewHistory.user_id == user_id
        ).options(
            joinedload(ViewHistory.listing)
        ).order_by(
            desc(ViewHistory.viewed_at)
        ).limit(50).all()
        
        if not recent_views:
            # If no history, return empty criteria
            return {
                "property_types": [],
                "property_categories": [],
                "city": None,
                "neighborhoods": [],
                "price_min": None,
                "price_max": None,
                "has_suggestions": False
            }
        
        # Analyze preferences (same logic as get_suggested_properties)
        property_types = []
        property_categories = []
        cities = []
        neighborhoods = []
        price_ranges = []
        
        for view in recent_views:
            listing = view.listing
            if listing:
                if listing.property_type:
                    property_types.append(listing.property_type)
                if listing.property_category:
                    property_categories.append(listing.property_category)
                if listing.city:
                    cities.append(listing.city)
                if listing.neighborhood:
                    neighborhoods.append((listing.city, listing.neighborhood))
                if listing.price and listing.price > 0:
                    price_ranges.append(listing.price)
        
        # Get most common preferences
        from collections import Counter
        
        # Most viewed property types (top 3)
        common_types = [item[0] for item in Counter(property_types).most_common(3)]
        
        # Most viewed categories (top 2)
        common_categories = [item[0] for item in Counter(property_categories).most_common(2)]
        
        # Most viewed city (only 1)
        most_visited_city = Counter(cities).most_common(1)
        most_visited_city = most_visited_city[0][0] if most_visited_city else None
        
        # Most viewed neighborhoods within the most visited city (top 3)
        common_neighborhoods = []
        if most_visited_city:
            city_neighborhoods = [neighborhood for city, neighborhood in neighborhoods if city == most_visited_city]
            common_neighborhoods = [item[0] for item in Counter(city_neighborhoods).most_common(3)]
        
        # Calculate price range (25th to 75th percentile)
        if price_ranges:
            price_ranges.sort()
            q1_index = len(price_ranges) // 4
            q3_index = (3 * len(price_ranges)) // 4
            min_price = price_ranges[q1_index] * 0.8  # 20% below Q1
            max_price = price_ranges[q3_index] * 1.2  # 20% above Q3
        else:
            min_price = max_price = None
        
        return {
            "property_types": common_types,
            "property_categories": common_categories,
            "city": most_visited_city,
            "neighborhoods": common_neighborhoods,
            "price_min": int(min_price) if min_price else None,
            "price_max": int(max_price) if max_price else None,
            "has_suggestions": True
        }

view_history = CRUDViewHistory(ViewHistory) 