from sqlalchemy import <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Table, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import expression
from sqlalchemy.sql.sqltypes import ARRAY
from datetime import datetime

from app.db.session import Base

# User-Role association table
user_roles = Table(
    "user_roles",
    Base.metadata,
    Column("user_id", Integer, ForeignKey("users.id")),
    Column("role_id", Integer, ForeignKey("roles.id")),
)

class Role(Base):
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    permissions = Column(Text, nullable=False)  # JSON string of permissions
    
    users = relationship("User", secondary=user_roles, back_populates="roles")
    
    def __init__(self, name, description="", permissions=None):
        self.name = name
        self.description = description
        self.permissions = permissions or "[]"  # Default to empty array

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    clerk_user_id = Column(String(255), unique=True, index=True, nullable=True)  # Clerk user ID
    email = Column(String(100), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=True)  # Make nullable for Clerk users
    hashed_password = Column(String(255), nullable=True)  # Make nullable for Clerk users
    full_name = Column(String(100))
    is_active = Column(Boolean(), default=True)
    is_superuser = Column(Boolean(), default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    
    # Add relationship to listings owned by this user
    listings = relationship("Listing", foreign_keys="[Listing.user_id]", back_populates="owner", cascade="all, delete-orphan")
    
    # Add relationship to bookmarks
    bookmarks = relationship("Bookmark", back_populates="user", cascade="all, delete-orphan")
    
    # Add relationship to view history
    view_history = relationship("ViewHistory", back_populates="user", cascade="all, delete-orphan") 