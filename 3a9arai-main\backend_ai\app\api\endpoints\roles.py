from typing import Any, List, Optional
import json

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_admin_user
from app.models.user import Role
from app.schemas.user import (
    Role as RoleSchema,
    RoleCreate,
    RoleUpdate,
    PermissionCheck,
)

router = APIRouter()

@router.get("/", response_model=List[RoleSchema])
async def read_roles(
    skip: int = 0,
    limit: int = 100,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Retrieve roles.
    """
    roles = db.query(Role).offset(skip).limit(limit).all()
    return roles

@router.post("/", response_model=RoleSchema)
async def create_role(
    role_in: RoleCreate,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Create new role.
    """
    # Check if role with this name already exists
    role = db.query(Role).filter(Role.name == role_in.name).first()
    if role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role already exists",
        )
    
    # Create new role
    role = Role(
        name=role_in.name,
        description=role_in.description or "",
        permissions=json.dumps(role_in.permissions),
    )
    
    db.add(role)
    db.commit()
    db.refresh(role)
    
    return role

@router.get("/{role_id}", response_model=RoleSchema)
async def read_role(
    role_id: int,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific role by id.
    """
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found",
        )
    return role

@router.put("/{role_id}", response_model=RoleSchema)
async def update_role(
    role_id: int,
    role_in: RoleUpdate,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Update a role.
    """
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found",
        )
    
    # Update role fields
    if role_in.name is not None:
        # Check if name already exists
        name_exists = db.query(Role).filter(
            Role.name == role_in.name, 
            Role.id != role_id
        ).first()
        if name_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role name already exists",
            )
        role.name = role_in.name
    
    if role_in.description is not None:
        role.description = role_in.description
    
    if role_in.permissions is not None:
        role.permissions = json.dumps(role_in.permissions)
    
    db.add(role)
    db.commit()
    db.refresh(role)
    
    return role

@router.delete("/{role_id}", response_model=RoleSchema)
async def delete_role(
    role_id: int,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Delete a role.
    """
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found",
        )
    
    # Do not allow deleting role if it has users assigned
    if role.users:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete role with assigned users",
        )
    
    db.delete(role)
    db.commit()
    
    return role

@router.post("/check-permission", response_model=bool)
async def check_permission(
    permission_check: PermissionCheck,
    current_user = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Check if a permission is valid.
    """
    # Check if permission is in predefined list
    valid_permissions = [
        "user:read",
        "user:write",
        "admin",
        "listings:read",
        "listings:write",
        "scrapers:read",
        "scrapers:execute",
    ]
    
    return permission_check.permission in valid_permissions 