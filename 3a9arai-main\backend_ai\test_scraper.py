#!/usr/bin/env python3
import logging
import os
from typing import Optional, List, Union
from scrapegraphai.graphs import SmartScraperGraph
from pydantic import BaseModel

class PropertyListing(BaseModel):
    """Schema for property listing data extracted by ScrapegraphAI."""
    title: str
    description: Optional[str] = ""
    price: Union[int, str] = 0  # Can be integer or string to be parsed later
    location: Optional[str] = ""
    city: Optional[str] = ""
    property_type: Optional[str] = ""
    size: Optional[float] = 0
    rooms: Optional[int] = 0
    bathrooms: Optional[int] = 0
    features: Optional[List[str]] = []
    images: Optional[List[str]] = []
    date_posted: Optional[str] = ""
    contact_info: Optional[str] = ""
    url: str

print(f"DISPLAY environment variable: {os.environ.get('DISPLAY', 'Not set')}")
print(f"BaseModel: {BaseModel}")
print(f"PropertyListing: {PropertyListing}")
print(f"PropertyListing.__module__: {PropertyListing.__module__}")

# Config for headless mode
headless_config = {
    "llm": {
        "api_key": "test_key",
        "model": "mistralai/mistral-large-latest",
    },
    "verbose": True,
    "headless": True,
    "playwright_config": {
        "chromium": {
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--disable-gpu",
                "--disable-extensions",
            ]
        }
    }
}

# Config for non-headless mode
non_headless_config = {
    "llm": {
        "api_key": "test_key",
        "model": "mistralai/mistral-large-latest",
    },
    "verbose": True,
    "headless": False,
    "playwright_config": {
        "chromium": {
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--disable-gpu",
                "--disable-extensions",
            ]
        }
    }
}

# Set up the same prompt used in the Agenz scraper
prompt = """
Extract all information about this real estate listing:
1. Extract the property title
2. Extract the full property description
3. Extract the price (just the number, without currency)
4. Extract the location/address
5. Extract the city name
6. Extract the property type (apartment, villa, house, etc.)
7. Extract the property size in square meters
8. Extract the number of rooms
9. Extract the number of bathrooms
10. Extract any listed features or amenities
11. Extract all image URLs
12. Extract the date posted if available
13. Extract any contact information

Format the data according to the specified schema.
"""

# Test with headless mode
print("\n--- Testing with headless mode ---")
try:
    graph = SmartScraperGraph(
        prompt=prompt,
        source="https://example.com",
        config=headless_config,
        schema=PropertyListing,
    )
    print('Success! Headless mode worked.')
except Exception as e:
    print(f'Error with headless mode: {e}')

# Test with non-headless mode
print("\n--- Testing with non-headless mode ---")
try:
    graph = SmartScraperGraph(
        prompt=prompt,
        source="https://example.com",
        config=non_headless_config,
        schema=PropertyListing,
    )
    print('Success! Non-headless mode worked.')
except Exception as e:
    print(f'Error with non-headless mode: {e}') 