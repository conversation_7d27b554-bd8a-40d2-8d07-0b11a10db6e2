"""Celery tasks for scraper pipeline with distributed workers."""
import logging
import json
import time
import os
import psutil
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.worker.celery import celery_app
from app.db.session import SessionLocal
from app.services.scrapers.agenz import AgenzScraper
from app.services.scrapers.sarouty import Sarout<PERSON><PERSON>craper
from app.services.scrapers.avito_rent import Avito<PERSON>entScraper
from app.services.scrapers.avito_sale import AvitoS<PERSON><PERSON>craper
from app.services.scrapers.base_scraper import redis_client
from app.services.scrapers.llm_service import (
    is_rate_limited, set_rate_limited, should_llm_queue_continue, 
    check_llm_queue_control, set_llm_queue_status
)
from app.models.listing import Listing
from app.services.scrapers.sarouty import (
    URL_QUEUE as SAROUTY_URL_QUEUE,
    DETAILS_QUEUE as SAROUTY_DETAILS_QUEUE, 
    PROCESSED_QUEUE as SAROUTY_PROCESSED_QUEUE,
    NEXT_PAGE_QUEUE as SAROUTY_NEXT_PAGE_QUEUE
)
from app.services.utils.s3_image_utils import S3ImageProcessor

logger = logging.getLogger(__name__)

# Configuration constants
class QueueConfig:
    """Configuration for queue names and settings."""
    # Agenz queues
    AGENZ_URL_QUEUE = "agenz_url_queue"
    AGENZ_DETAILS_QUEUE = "agenz_details_queue"
    AGENZ_PROCESSED_QUEUE = "agenz_processed_queue"
    
    # Avito Sale queues
    AVITO_SALE_URL_QUEUE = "avito_sale_url_queue"
    AVITO_SALE_DETAILS_QUEUE = "avito_sale_details_queue"
    AVITO_SALE_PROCESSED_QUEUE = "avito_sale_processed_queue"
    
    # Avito Rent queues
    AVITO_RENT_URL_QUEUE = "avito_rent_url_queue"
    AVITO_RENT_DETAILS_QUEUE = "avito_rent_details_queue"
    AVITO_RENT_PROCESSED_QUEUE = "avito_rent_processed_queue"
    
    # Rate limiting
    LLM_RATE_LIMIT_DEFAULT_PAUSE = 30
    BATCH_SIZE = 10  # Number of URLs to process per task execution
    
    # Memory monitoring thresholds (adjusted for production)
    MEMORY_WARNING_THRESHOLD = 800 * 1024 * 1024  # 800MB  
    MEMORY_CRITICAL_THRESHOLD = 1024 * 1024 * 1024  # 1GB

class MemoryMonitor:
    """Monitor memory usage and prevent memory-related crashes."""
    
    @staticmethod
    def get_memory_usage() -> Dict[str, int]:
        """Get current memory usage statistics."""
        process = psutil.Process()
        memory_info = process.memory_info()
        return {
            'rss': memory_info.rss,  # Resident Set Size
            'vms': memory_info.vms,  # Virtual Memory Size
            'percent': process.memory_percent()
        }
    
    @staticmethod
    def check_memory_status() -> str:
        """Check memory status and return warning level."""
        memory = MemoryMonitor.get_memory_usage()
        rss = memory['rss']
        
        if rss > QueueConfig.MEMORY_CRITICAL_THRESHOLD:
            return 'critical'
        elif rss > QueueConfig.MEMORY_WARNING_THRESHOLD:
            return 'warning'
        else:
            return 'normal'
    
    @staticmethod
    def log_memory_status():
        """Log current memory usage."""
        memory = MemoryMonitor.get_memory_usage()
        status = MemoryMonitor.check_memory_status()
        
        logger.info(f"Memory Status: {status} - RSS: {memory['rss'] / 1024 / 1024:.1f}MB, "
                   f"VMS: {memory['vms'] / 1024 / 1024:.1f}MB, "
                   f"Percent: {memory['percent']:.1f}%")
        
        if status == 'critical':
            logger.error("CRITICAL: Memory usage is very high! Risk of OOM kill.")
        elif status == 'warning':
            logger.warning("WARNING: Memory usage is elevated.")
    
    @staticmethod
    def force_cleanup():
        """Force garbage collection and cleanup with enhanced monitoring."""
        import gc
        
        # Get memory before cleanup
        before_memory = MemoryMonitor.get_memory_usage()
        
        # Force garbage collection multiple times for thorough cleanup
        collected = gc.collect()
        collected += gc.collect()  # Second pass often collects more
        collected += gc.collect()  # Third pass for circular references
        
        # Get memory after cleanup
        after_memory = MemoryMonitor.get_memory_usage()
        
        # Calculate memory freed
        memory_freed = before_memory['rss'] - after_memory['rss']
        memory_freed_mb = memory_freed / 1024 / 1024
        
        if memory_freed_mb > 1:  # Only log if significant memory was freed
            logger.info(f"Garbage collection freed {memory_freed_mb:.1f}MB, collected {collected} objects")
        else:
            logger.info(f"Garbage collection completed, collected {collected} objects")
        
        # Log final memory status
        MemoryMonitor.log_memory_status()

class RedisHealthMonitor:
    """Monitor Redis health and queue states."""
    
    @staticmethod
    def check_redis_memory():
        """Check Redis memory usage and warn if approaching limits."""
        try:
            info = redis_client.info('memory')
            used_memory = info.get('used_memory', 0)
            max_memory = info.get('maxmemory', 0)
            
            if max_memory > 0:
                usage_percent = (used_memory / max_memory) * 100
                if usage_percent > 90:
                    logger.error(f"CRITICAL: Redis memory usage at {usage_percent:.1f}%")
                elif usage_percent > 75:
                    logger.warning(f"WARNING: Redis memory usage at {usage_percent:.1f}%")
                else:
                    logger.info(f"Redis memory usage: {usage_percent:.1f}%")
            
            return used_memory, max_memory
            
        except Exception as e:
            logger.error(f"Failed to check Redis memory: {str(e)}")
            return 0, 0
    
    @staticmethod
    def monitor_queue_sizes():
        """Monitor queue sizes and log warnings for large queues."""
        queue_names = [
            QueueConfig.AGENZ_DETAILS_QUEUE,
            QueueConfig.AGENZ_PROCESSED_QUEUE,
            SAROUTY_PROCESSED_QUEUE,
            QueueConfig.AVITO_SALE_DETAILS_QUEUE,
            QueueConfig.AVITO_RENT_DETAILS_QUEUE
        ]
        
        large_queues = []
        total_items = 0
        
        for queue_name in queue_names:
            try:
                size = redis_client.llen(queue_name)
                total_items += size
                if size > 100:  # Threshold for large queue
                    large_queues.append((queue_name, size))
                    logger.warning(f"Large queue detected: {queue_name} has {size} items")
            except Exception as e:
                logger.error(f"Failed to check queue size for {queue_name}: {str(e)}")
        
        if total_items > 500:
            logger.warning(f"High total queue load: {total_items} items across all queues")
        
        return large_queues, total_items

class ScraperStatus:
    """Utility class for checking scraper status."""
    
    @staticmethod
    def is_running(scraper_name: str) -> bool:
        """Check if a scraper should continue running based on its status in Redis.
        
        Args:
            scraper_name: Name of the scraper to check
            
        Returns:
            bool: True if scraper should continue, False otherwise
        """
        status_key = f"{scraper_name}_status"
        status = redis_client.get(status_key)
        return status and status.decode('utf-8') == "running"
    
    @staticmethod
    def is_url_collection_completed(scraper_name: str) -> bool:
        """Check if URL collection is completed for a scraper."""
        return bool(redis_client.get(f"{scraper_name}_url_collection_completed"))

class QueueManager:
    """Utility class for managing Redis queues."""
    
    @staticmethod
    def is_queue_empty(queue_name: str) -> bool:
        """Check if a queue is effectively empty (accounting for markers)."""
        queue_length = redis_client.llen(queue_name)
        
        if queue_length == 0:
            return True
            
        if queue_length == 1:
            # Check if it only contains markers
            queue_items = redis_client.lrange(queue_name, 0, 1)
            if (not queue_items or 
                queue_items[0] == b'' or 
                queue_items[0] == b'__QUEUE_MARKER__'):
                return True
        
        return False
    
    @staticmethod
    def get_next_item(queue_name: str) -> Optional[str]:
        """Get the next valid item from a queue, skipping markers."""
        while True:
            item_data = redis_client.lpop(queue_name)
            if not item_data:
                return None
                
            # Skip queue markers
            if item_data == b'' or item_data == b'__QUEUE_MARKER__':
                continue
                
            return item_data.decode('utf-8')
    
    @staticmethod
    def requeue_item(queue_name: str, item_data: str):
        """Put an item back in the queue."""
        redis_client.rpush(queue_name, item_data)

class ImageProcessor:
    """Utility class for processing images."""
    
    @staticmethod
    def process_images_with_imgproxy(image_urls: List[str]) -> List[dict]:
        """Process images by downloading them and generating filesystem URLs with thumbnails.
        
        Args:
            image_urls: List of original image URLs
            
        Returns:
            List of dictionaries with original and thumbnail URLs
        """
        return S3ImageProcessor.process_images_for_s3(image_urls)
    
    @staticmethod
    def extract_original_urls(image_data: List[dict]) -> List[str]:
        """Extract original URLs from image data dictionaries for database storage.
        
        Args:
            image_data: List of dictionaries with image URLs
            
        Returns:
            List of original image URLs for database storage
        """
        if not image_data:
            return []
        
        original_urls = []
        for img in image_data:
            if isinstance(img, dict) and 'original' in img:
                original_urls.append(img['original'])
            elif isinstance(img, str):
                # Backward compatibility: if it's already a string URL
                original_urls.append(img)
        
        return original_urls

class DatabaseManager:
    """Utility class for database operations."""
    
    @staticmethod
    def save_listing_to_database(db, listing_data, images=None) -> bool:
        """Save a listing to the database.
        
        Args:
            db: Database session
            listing_data: Listing data from LLM processing
            images: List of image URLs or dictionaries with image data
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            logger.info(f"Saving listing to database: {listing_data.url}")
            
            url = listing_data.url
            existing_listing = db.query(Listing).filter(Listing.url == url).first()
            
            # Process images to get thumbnail data
            processed_images = None
            if images and any(not ("digitaloceanspaces.com" in str(img) or "/media/property_images/" in str(img)) for img in images):
                # Process images and get full thumbnail data with S3
                image_data = S3ImageProcessor.process_images_for_s3(images)
                # Extract just the original URLs for database storage
                processed_images = ImageProcessor.extract_original_urls(image_data)
            elif images:
                # Images are already processed, extract original URLs if needed
                # This handles both filesystem and S3 dictionary formats
                if images and isinstance(images[0], dict):
                    processed_images = ImageProcessor.extract_original_urls(images)
                else:
                    processed_images = images
            
            if existing_listing:
                logger.info(f"Listing {url} already exists. Updating.")
                DatabaseManager._update_existing_listing(existing_listing, listing_data, processed_images)
                db.commit()
                logger.info(f"Updated listing {url} in database")
                return True
            else:
                logger.info(f"Creating new listing for {url}")
                DatabaseManager._create_new_listing(db, listing_data, processed_images)
                logger.info(f"Saved new listing {url} to database")
                return True
                
        except Exception as e:
            logger.error(f"Error saving listing to database: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            db.rollback()
            return False
    
    @staticmethod
    def _update_existing_listing(existing_listing, listing_data, images):
        """Update an existing listing."""
        for key, value in listing_data.__dict__.items():
            if key.startswith('_') or key in ['id', 'scrape_date']:
                continue
            if hasattr(existing_listing, key):
                # Special handling for additional_info field to serialize AdditionalInfo objects
                if key == 'additional_info' and value is not None:
                    # Convert AdditionalInfo objects to dictionaries
                    if isinstance(value, list):
                        serialized_additional_info = []
                        for item in value:
                            if hasattr(item, 'model_dump'):  # It's a Pydantic model
                                serialized_additional_info.append(item.model_dump())
                            elif isinstance(item, dict):  # It's already a dict
                                serialized_additional_info.append(item)
                            else:
                                # Convert to dict if it has dict-like attributes
                                try:
                                    serialized_additional_info.append({
                                        'name': getattr(item, 'name', str(item)),
                                        'value': getattr(item, 'value', ''),
                                        'notes_for_user': getattr(item, 'notes_for_user', None)
                                    })
                                except:
                                    # Last resort: skip malformed items
                                    logger.warning(f"Skipping malformed additional_info item: {item}")
                                    continue
                        setattr(existing_listing, key, serialized_additional_info)
                    else:
                        # If it's not a list, set it to an empty list
                        setattr(existing_listing, key, [])
                else:
                    setattr(existing_listing, key, value)
        
        if images:
            existing_listing.images = [img for img in images if img is not None]
    
    @staticmethod
    def _create_new_listing(db, listing_data, images):
        """Create a new listing."""
        serializable_data = listing_data.model_dump(mode='json')
        
        if images:
            serializable_data['images'] = [img for img in images if img is not None]
        
        new_db_listing = Listing(**serializable_data)
        db.add(new_db_listing)
        db.commit()
        db.refresh(new_db_listing)

class URLCollectionTask:
    """Base class for URL collection tasks."""
    
    def __init__(self, scraper_class, scraper_name: str):
        self.scraper_class = scraper_class
        self.scraper_name = scraper_name
    
    def execute(self) -> int:
        """Execute the URL collection task."""
        if ScraperStatus.is_url_collection_completed(self.scraper_name):
            logger.info(f"URL collection for {self.scraper_name} is completed. Skipping.")
            return 0
        
        if not ScraperStatus.is_running(self.scraper_name):
            logger.info(f"{self.scraper_name} scraper is stopped. Skipping URL extraction.")
            return 0
        
        db = SessionLocal()
        try:
            scraper = self.scraper_class(db)
            
            if not scraper.should_continue():
                logger.info(f"{self.scraper_name} scraper should not continue, stopping URL extraction")
                return 0
            
            urls = scraper.collect_listing_urls()
            
            if not scraper.should_continue():
                logger.info(f"Stopping {self.scraper_name} URL extraction as requested")
                return len(urls)
            
            logger.info(f"{self.scraper_name} URL extraction worker collected {len(urls)} URLs")
            return len(urls)
            
        except Exception as e:
            logger.error(f"Error in {self.scraper_name} URL extraction worker: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return 0
        finally:
            db.close()

class DetailsExtractionTask:
    """Base class for details extraction tasks."""
    
    def __init__(self, scraper_class, scraper_name: str, url_queue: str, details_queue: str):
        self.scraper_class = scraper_class
        self.scraper_name = scraper_name
        self.url_queue = url_queue
        self.details_queue = details_queue
    
    def execute(self) -> int:
        """Execute the details extraction task."""
        db = SessionLocal()
        try:
            scraper = self.scraper_class(db)
            
            if not self._should_continue_safely(scraper):
                logger.info(f"{self.scraper_name} scraper is stopped, not processing details")
                return 0
            
            if QueueManager.is_queue_empty(self.url_queue):
                logger.info(f"No URLs in {self.scraper_name} queue.")
                return 0
            
            return self._process_urls_batch(scraper)
            
        except Exception as e:
            logger.error(f"Error in {self.scraper_name} details extraction worker: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return 0
        finally:
            db.close()
    
    def _should_continue_safely(self, scraper) -> bool:
        """Safely check if scraper should continue."""
        try:
            return scraper.should_continue()
        except Exception as e:
            logger.error(f"Error checking if {self.scraper_name} scraper should continue: {str(e)}")
            return True  # Continue anyway to be safe
    
    def _process_urls_batch(self, scraper) -> int:
        """Process a batch of URLs."""
        processed_count = 0
        db = SessionLocal()
        for _ in range(QueueConfig.BATCH_SIZE):
            if not self._should_continue_safely(scraper):
                logger.info(f"{self.scraper_name} scraper stopped during detail processing")
                break
            
            url = QueueManager.get_next_item(self.url_queue)
            if not url:
                logger.info(f"No more URLs in {self.scraper_name} queue.")
                break
            
            try:
                logger.info(f"Processing URL: {url}")
                # check in database if listing already exists
                if db.query(Listing).filter(Listing.url == url).first():
                    logger.info(f"Listing {url} already exists. Skipping.")
                    continue
                details = scraper.extract_listing_details(url)
                
                if details and details.get("property_details"):
                    # Check queue limits before adding to LLM queue
                    from app.services.scrapers.queue_manager import queue_limit_manager
                    
                    if queue_limit_manager.should_allow_queue_addition(self.scraper_name):
                        redis_client.rpush(self.details_queue, json.dumps(details))
                        processed_count += 1
                        logger.info(f"Added details for {url} to {self.scraper_name} details queue")
                    else:
                        logger.warning(f"Queue limit reached - not adding {url} to {self.scraper_name} details queue. Current total queue size: {queue_limit_manager.get_total_llm_queue_size()}")
                        # Trigger queue management check
                        queue_limit_manager.check_and_manage_queue_limits()
                
            except Exception as e:
                logger.error(f"Error processing {self.scraper_name} URL {url}: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                continue
        
        logger.info(f"{self.scraper_name} details extraction worker processed {processed_count} URLs")
        return processed_count

class LLMProcessor:
    """Handles LLM processing of listings."""
    
    def __init__(self):
        self.queue_configs = [
            {
                "name": QueueConfig.AGENZ_DETAILS_QUEUE,
                "scraper_type": "agenz", 
                "scraper_class": AgenzScraper
            },
            {
                "name": SAROUTY_PROCESSED_QUEUE,
                "scraper_type": "sarouty",
                "scraper_class": SaroutyScraper
            },
            {
                "name": QueueConfig.AVITO_SALE_DETAILS_QUEUE,
                "scraper_type": "avito_sale",
                "scraper_class": AvitoSaleScraper
            },
            {
                "name": QueueConfig.AVITO_RENT_DETAILS_QUEUE,
                "scraper_type": "avito_rent",
                "scraper_class": AvitoRentScraper
            }
        ]
        self.empty_queue_count = 0  # Track consecutive empty queue executions
    
    def execute(self) -> int:
        """Execute LLM processing for all queues with enhanced monitoring and memory optimization."""
        # Monitor memory before processing
        MemoryMonitor.log_memory_status()
        memory_status = MemoryMonitor.check_memory_status()
        
        if memory_status == 'critical':
            logger.error("CRITICAL memory usage detected! Forcing cleanup and skipping processing")
            MemoryMonitor.force_cleanup()
            return 0
        
        # Check LLM queue control commands first (without DB session)
        self._handle_llm_queue_control()
        
        # Check if LLM queue should continue processing
        if not should_llm_queue_continue():
            logger.info("LLM queue is stopped, not processing")
            return 0

        # Handle rate limiting (without DB session)
        self._handle_rate_limiting()
        
        # Quick check for work availability before creating DB session
        has_work = self._quick_queue_check()
        if not has_work:
            self.empty_queue_count += 1
            
            # Perform cleanup every 10 empty executions to prevent memory accumulation
            if self.empty_queue_count % 10 == 0:
                logger.info(f"Performing cleanup after {self.empty_queue_count} empty executions")
                MemoryMonitor.force_cleanup()
                # Monitor Redis health periodically
                RedisHealthMonitor.check_redis_memory()
                large_queues, total_items = RedisHealthMonitor.monitor_queue_sizes()
                if total_items > 1000:
                    logger.warning(f"Large queue load detected: {total_items} items.")
                
                # Check and manage queue limits for automatic scraper pause/resume
                from app.services.scrapers.queue_manager import queue_limit_manager
                queue_limit_manager.check_and_manage_queue_limits()
            
            logger.info("No listings found in any queue")
            return 0
        
        # Reset empty count when work is found
        self.empty_queue_count = 0
        
        # Monitor Redis health when there's actual work
        RedisHealthMonitor.check_redis_memory()
        large_queues, total_items = RedisHealthMonitor.monitor_queue_sizes()
        
        if total_items > 1000:
            logger.warning(f"Very large queue load detected: {total_items} items. Processing cautiously.")
        
        # Check and manage queue limits for automatic scraper pause/resume
        from app.services.scrapers.queue_manager import queue_limit_manager
        queue_status = queue_limit_manager.check_and_manage_queue_limits()
        if queue_status.get("actions_taken"):
            logger.info(f"Queue management actions taken: {queue_status['actions_taken']}")
        
        # If memory pressure detected, perform cleanup
        if queue_status.get("memory_pressure"):
            logger.warning("Memory pressure detected in LLM processor, performing cleanup")
            queue_limit_manager.force_memory_cleanup()
        
        # Only create DB session when there's actual work to do
        db = SessionLocal()
        try:
            for queue_config in self.queue_configs:
                # Check memory before each queue
                if MemoryMonitor.check_memory_status() == 'critical':
                    logger.warning("Memory critical during processing, stopping")
                    break
                    
                processed = self._process_queue(db, queue_config)
                if processed > 0:
                    return processed  # Process one listing at a time
            
            logger.info("No listings found in any queue (after DB session created)")
            return 0
            
        except Exception as e:
            logger.error(f"Error in unified LLM processing worker: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Force cleanup on error
            MemoryMonitor.force_cleanup()
            return 0
        finally:
            db.close()
            
            # Regular cleanup for successful executions
            if memory_status in ['warning', 'critical']:
                MemoryMonitor.force_cleanup()
    
    def _quick_queue_check(self) -> bool:
        """Quickly check if any queue has work without creating database sessions."""
        try:
            for queue_config in self.queue_configs:
                queue_name = queue_config["name"]
                if not QueueManager.is_queue_empty(queue_name):
                    return True
            return False
        except Exception as e:
            logger.error(f"Error in quick queue check: {str(e)}")
            return True  # Default to checking with DB session if Redis fails
    
    def _handle_llm_queue_control(self):
        """Handle LLM queue control commands."""
        try:
            command = check_llm_queue_control()
            if command and "command" in command:
                cmd = command["command"]
                if cmd == "start":
                    set_llm_queue_status("running")
                    logger.info("LLM queue started via control command")
                elif cmd == "stop":
                    set_llm_queue_status("stopped")
                    logger.info("LLM queue stopped via control command")
                elif cmd == "pause":
                    set_llm_queue_status("paused")
                    logger.info("LLM queue paused via control command")
                elif cmd == "resume":
                    set_llm_queue_status("running")
                    logger.info("LLM queue resumed via control command")
        except Exception as e:
            logger.error(f"Error handling LLM queue control: {str(e)}")
    
    def _handle_rate_limiting(self):
        """Handle LLM rate limiting."""
        if is_rate_limited():
            rate_limited_until = redis_client.get("llm_rate_limited_until")
            if rate_limited_until:
                try:
                    until_timestamp = float(rate_limited_until.decode('utf-8'))
                    remaining_seconds = max(0, int(until_timestamp - time.time()))
                    if remaining_seconds > 0:
                        logger.warning(f"LLM API rate limited. Sleeping for {remaining_seconds} seconds")
                        time.sleep(remaining_seconds)
                        logger.info("Rate limit period expired, resuming processing")
                except (ValueError, TypeError):
                    logger.warning("LLM rate limited but couldn't parse expiration time")
                    time.sleep(30)
            else:
                time.sleep(30)
    
    def _process_queue(self, db, queue_config: Dict[str, Any]) -> int:
        """Process a single queue."""
        queue_name = queue_config["name"]
        scraper_type = queue_config["scraper_type"]
        scraper_class = queue_config["scraper_class"]
        
        details_data = redis_client.lpop(queue_name)
        if not details_data or details_data in [b'', b'__QUEUE_MARKER__']:
            return 0
        
        scraper = scraper_class(db)
        
        # LLM queue operates independently of individual scraper status
        # Only check if LLM queue itself should continue
        if not should_llm_queue_continue():
            logger.info("LLM queue is stopped, requeuing item")
            QueueManager.requeue_item(queue_name, details_data.decode('utf-8'))
            return 0
        
        try:
            return self._process_listing(db, scraper, scraper_type, queue_name, details_data)
        except Exception as e:
            logger.error(f"Error processing {scraper_type} listing: {str(e)}")
            QueueManager.requeue_item(queue_name, details_data.decode('utf-8'))
            return 0
    
    def _process_listing(self, db, scraper, scraper_type: str, queue_name: str, details_data: bytes) -> int:
        """Process a single listing."""
        try:
            details = json.loads(details_data.decode('utf-8'))
            url = details.get("url")
            property_details = details.get("property_details")
            original_images = details.get("images", [])
            
            if len(original_images) == 0:
                logger.warning(f"No images found for {scraper_type} listing {url}")
                return 0
            
            if not url or not property_details:
                logger.warning(f"Missing URL or property details for {scraper_type} listing")
                return 0
            
            # Check if listing already exists
            if db.query(Listing).filter(Listing.url == url).first():
                logger.info(f"Listing {url} already exists. Skipping LLM processing.")
                return 1
            
            logger.info(f"Processing {scraper_type} listing with LLM: {url}")
            
            # Process with LLM first (without downloading images)
            listing_data = self._call_llm_with_retry(scraper, url, property_details, scraper_type, original_images, queue_name, details_data)
            
            if not listing_data:
                logger.warning(f"LLM processing returned no data for {scraper_type} listing {url}")
                return 0
            
            # Only download images after successful LLM processing
            logger.info(f"LLM processing successful for {url}, now downloading images...")
            processed_images = None
            if original_images and any(not ("/media/property_images/" in img) for img in original_images):
                try:
                    processed_images = ImageProcessor.process_images_with_imgproxy(original_images)
                    logger.info(f"Successfully downloaded {len(processed_images)} images for {url}")
                except Exception as e:
                    logger.error(f"Failed to download images for {url}: {str(e)}")
                    # Continue with listing save even if image download fails
                    processed_images = []
            elif original_images:
                # Images are already processed
                processed_images = original_images
            
            # Save to database with processed images
            if DatabaseManager.save_listing_to_database(db, listing_data, processed_images):
                logger.info(f"Successfully processed and saved {scraper_type} listing: {url}")
                self._record_processing_event()
                time.sleep(1)  # Rate limiting
                return 1
            else:
                logger.error(f"Failed to save {scraper_type} listing to database: {url}")
                return 0
                
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from {scraper_type} details queue: {str(e)}")
            return 0
    
    def _call_llm_with_retry(self, scraper, url: str, property_details: str, scraper_type: str, 
                           images: List[str], queue_name: str, details_data: bytes):
        """Call LLM with rate limit handling."""
        try:
            return scraper.process_listing_with_llm(url, property_details, scraper_type, images)
        except Exception as e:
            error_str = str(e).lower()
            if any(term in error_str for term in ["rate limit", "429", "too many requests", "quota exceeded"]):
                logger.warning(f"LLM API rate limited. Setting rate limit flag and requeuing {scraper_type} listing: {url}")
                set_rate_limited(180)
                QueueManager.requeue_item(queue_name, details_data.decode('utf-8'))
                return None
            else:
                raise
    
    def _record_processing_event(self):
        """Record the processing event for rate tracking."""
        try:
            from app.api.endpoints.scrapers import record_llm_processing
            record_llm_processing()
        except Exception as track_error:
            logger.warning(f"Failed to record processing event: {track_error}")

# Celery task definitions
@celery_app.task(name="monitor_queue_limits", bind=True)
def monitor_queue_limits(self):
    """Monitor queue limits and pause/resume scrapers as needed."""
    try:
        from app.services.scrapers.queue_manager import queue_limit_manager
        logger.info("Running queue limit monitoring check")
        
        # Check memory usage before doing anything
        memory_info = queue_limit_manager.get_redis_memory_usage()
        
        # If memory usage is very high, force cleanup first
        if memory_info['usage_percent'] >= 85:
            logger.warning(f"High memory usage detected: {memory_info['usage_percent']}%. "
                         f"Performing cleanup before queue management.")
            queue_limit_manager.force_memory_cleanup()
        
        status = queue_limit_manager.check_and_manage_queue_limits()
        
        # Log important actions
        if status.get("actions_taken"):
            logger.info(f"Queue management actions taken: {status['actions_taken']}")
        
        # Log current status periodically
        total_size = status.get("total_queue_size", 0)
        paused_scrapers = status.get("paused_scrapers", [])
        memory_info = status.get("memory_info", {})
        
        if total_size >= 1200:  # Log when approaching limit
            logger.warning(f"Queue size approaching limit: {total_size}/1500 items, "
                         f"paused scrapers: {paused_scrapers}, "
                         f"memory: {memory_info.get('used_memory_mb', 0)}MB "
                         f"({memory_info.get('usage_percent', 0)}%)")
        elif total_size >= 400:  # Log when queue is building up
            logger.info(f"Queue monitoring: {total_size} items in LLM queue, "
                      f"paused scrapers: {paused_scrapers}, "
                      f"memory: {memory_info.get('used_memory_mb', 0)}MB "
                      f"({memory_info.get('usage_percent', 0)}%)")
        
        return {
            "status": "success",
            "total_queue_size": total_size,
            "paused_scrapers": paused_scrapers,
            "actions_taken": status.get("actions_taken", []),
            "memory_info": memory_info,
            "memory_pressure": status.get("memory_pressure", False)
        }
        
    except Exception as e:
        logger.error(f"Error in queue limit monitoring task: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "status": "error",
            "error": str(e)
        }

@celery_app.task(name="scrape_agenz_urls", bind=True)
def scrape_agenz_urls(self):
    """Extract Agenz listing URLs and add them to the URL queue."""
    task = URLCollectionTask(AgenzScraper, "agenz")
    return task.execute()

@celery_app.task(name="scrape_agenz_details", bind=True)
def scrape_agenz_details(self):
    """Extract Agenz listing details and add them to the details queue."""
    task = DetailsExtractionTask(
        AgenzScraper, 
        "agenz", 
        QueueConfig.AGENZ_URL_QUEUE, 
        QueueConfig.AGENZ_DETAILS_QUEUE
    )
    return task.execute()

@celery_app.task(name="scrape_sarouty_urls", bind=True)
def scrape_sarouty_urls(self):
    """Extract Sarouty listing URLs and add them to the URL queue."""
    task = URLCollectionTask(SaroutyScraper, "sarouty")
    return task.execute()

@celery_app.task(name="scrape_sarouty_details", bind=True)
def scrape_sarouty_details(self):
    """Extract Sarouty listing details and add them to the details queue."""
    task = DetailsExtractionTask(
        SaroutyScraper, 
        "sarouty", 
        SAROUTY_URL_QUEUE, 
        SAROUTY_PROCESSED_QUEUE
    )
    return task.execute()

# Avito Sale tasks
@celery_app.task(name="scrape_avito_sale_urls", bind=True)
def scrape_avito_sale_urls(self):
    """Extract Avito Sale listing URLs and add them to the URL queue."""
    task = URLCollectionTask(AvitoSaleScraper, "avito_sale")
    return task.execute()

@celery_app.task(name="scrape_avito_sale_details", bind=True)
def scrape_avito_sale_details(self):
    """Extract Avito Sale listing details and add them to the details queue."""
    task = DetailsExtractionTask(
        AvitoSaleScraper, 
        "avito_sale", 
        QueueConfig.AVITO_SALE_URL_QUEUE, 
        QueueConfig.AVITO_SALE_DETAILS_QUEUE
    )
    return task.execute()

# Avito Rent tasks
@celery_app.task(name="scrape_avito_rent_urls", bind=True)
def scrape_avito_rent_urls(self):
    """Extract Avito Rent listing URLs and add them to the URL queue."""
    task = URLCollectionTask(AvitoRentScraper, "avito_rent")
    return task.execute()

@celery_app.task(name="scrape_avito_rent_details", bind=True)
def scrape_avito_rent_details(self):
    """Extract Avito Rent listing details and add them to the details queue."""
    task = DetailsExtractionTask(
        AvitoRentScraper, 
        "avito_rent", 
        QueueConfig.AVITO_RENT_URL_QUEUE, 
        QueueConfig.AVITO_RENT_DETAILS_QUEUE
    )
    return task.execute()

# Legacy Avito tasks (for backward compatibility)
@celery_app.task(name="scrape_avito_urls", bind=True)
def scrape_avito_urls(self):
    """Extract Avito listing URLs and add them to the URL queue (legacy - points to sale)."""
    task = URLCollectionTask(AvitoSaleScraper, "avito_sale")
    return task.execute()

@celery_app.task(name="scrape_avito_details", bind=True)
def scrape_avito_details(self):
    """Extract Avito listing details and add them to the details queue (legacy - points to sale)."""
    task = DetailsExtractionTask(
        AvitoSaleScraper, 
        "avito_sale", 
        QueueConfig.AVITO_SALE_URL_QUEUE, 
        QueueConfig.AVITO_SALE_DETAILS_QUEUE
    )
    return task.execute()

@celery_app.task(name="process_listings", bind=True)
def process_listings(self):
    """Process listing details with LLM and save to the database."""
    processor = LLMProcessor()
    return processor.execute()

# Legacy function aliases for backward compatibility
def process_images_with_imgproxy(image_urls: List[str]) -> List[dict]:
    """Legacy alias for ImageProcessor.process_images_with_imgproxy."""
    return ImageProcessor.process_images_with_imgproxy(image_urls)

def save_listing_to_database(db, listing_data, images=None) -> bool:
    """Legacy alias for DatabaseManager.save_listing_to_database."""
    return DatabaseManager.save_listing_to_database(db, listing_data, images)

def check_scraper_status(scraper_name: str) -> bool:
    """Legacy alias for ScraperStatus.is_running."""
    return ScraperStatus.is_running(scraper_name)
