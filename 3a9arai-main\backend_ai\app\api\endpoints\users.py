from typing import Any, List, Optional, Dict

from fastapi import APIRouter, Depends, HTTPException, Query, Security, status
from sqlalchemy.orm import Session
import json
import logging
from sqlalchemy import func, desc
from sqlalchemy.orm import joinedload

from app.api.deps import get_db, get_current_user_from_clerk, prepare_listing_for_response, get_current_admin_user
from app.core.auth import (
    get_password_hash,
)
from app.core.config import settings
from app.models.user import User, Role
from app.schemas.user import (
    User as UserSchema,
    UserCreate,
    UserUpdate,
)
from app.models import Bookmark, ViewHistory, Listing

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=List[UserSchema])
async def read_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Retrieve users.
    """
    users = db.query(User).offset(skip).limit(limit).all()
    return users

@router.post("/", response_model=UserSchema)
async def create_user(
    user_in: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Create new user.
    """
    # Check if user with this email already exists
    user = db.query(User).filter(User.email == user_in.email).first()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered",
        )
    
    # Check if user with this username already exists
    user = db.query(User).filter(User.username == user_in.username).first()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered",
        )
        
    # Create new user
    user = User(
        email=user_in.email,
        username=user_in.username,
        hashed_password=get_password_hash(user_in.password),
        full_name=user_in.full_name or "",
        is_superuser=user_in.is_superuser,
    )
    
    # Get or create default role
    role = db.query(Role).filter(Role.name == settings.DEFAULT_USER_ROLE).first()
    if not role:
        # Create default role if it doesn't exist
        role = Role(
            name=settings.DEFAULT_USER_ROLE,
            description="Default user role with basic permissions",
            permissions=json.dumps(settings.DEFAULT_USER_PERMISSIONS)
        )
        db.add(role)
        db.flush()
    
    # Add role to user
    user.roles.append(role)
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user

@router.get("/{user_id}", response_model=UserSchema)
async def read_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    return user

@router.put("/{user_id}", response_model=UserSchema)
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Update a user.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Update user fields
    if user_in.email is not None:
        # Check if email already exists
        email_exists = db.query(User).filter(
            User.email == user_in.email, 
            User.id != user_id
        ).first()
        if email_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )
        user.email = user_in.email
    
    if user_in.full_name is not None:
        user.full_name = user_in.full_name
    
    if user_in.password is not None:
        user.hashed_password = get_password_hash(user_in.password)
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user

@router.delete("/{user_id}", response_model=UserSchema)
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Delete a user.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Cannot delete yourself
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own user account",
        )
    
    db.delete(user)
    db.commit()
    
    return user

@router.post("/{user_id}/activate", response_model=UserSchema)
async def activate_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Activate a user.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    user.is_active = True
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user

@router.post("/{user_id}/deactivate", response_model=UserSchema)
async def deactivate_user(
    user_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Any:
    """
    Deactivate a user.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    
    # Cannot deactivate yourself
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate your own user account",
        )
    
    user.is_active = False
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user

@router.get("/me/stats", response_model=Dict[str, Any])
def get_user_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user_from_clerk),
) -> Any:
    """
    Get statistics for the current user.
    """
    try:
        # Get bookmark count
        bookmark_count = db.query(func.count(Bookmark.id)).filter(
            Bookmark.user_id == current_user.id
        ).scalar() or 0
        
        # Get view history count
        view_count = db.query(func.count(ViewHistory.id)).filter(
            ViewHistory.user_id == current_user.id
        ).scalar() or 0
        
        # Get recent bookmarks (latest 5)
        recent_bookmarks = db.query(Listing).join(
            Bookmark, Bookmark.listing_id == Listing.id
        ).filter(
            Bookmark.user_id == current_user.id
        ).order_by(
            desc(Bookmark.created_at)
        ).limit(5).all()
        
        # Get recent view history (latest 5)
        recent_views = db.query(ViewHistory).filter(
            ViewHistory.user_id == current_user.id
        ).options(
            joinedload(ViewHistory.listing)
        ).order_by(
            desc(ViewHistory.viewed_at)
        ).limit(5).all()
        
        # Prepare listings for response
        from app.schemas.listing import Listing as ListingSchema
        
        recent_bookmarks_data = [
            ListingSchema.model_validate(listing).model_dump() for listing in recent_bookmarks
        ]
        
        recent_views_data = [
            {
                "viewed_at": view.viewed_at.isoformat(),
                "listing": ListingSchema.model_validate(view.listing).model_dump() if view.listing else None
            }
            for view in recent_views
        ]
        
        return {
            "bookmark_count": bookmark_count,
            "view_count": view_count,
            "recent_bookmarks": recent_bookmarks_data,
            "recent_views": recent_views_data
        }
        
    except Exception as e:
        logger.error(f"Error getting user stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not get user stats: {str(e)}") 