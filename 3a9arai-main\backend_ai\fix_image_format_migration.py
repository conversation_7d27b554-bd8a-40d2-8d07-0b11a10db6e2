#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix image data format in existing listings.

This script will:
1. Find all listings with image data stored as dictionaries
2. Convert image dictionaries to URL strings
3. Update the database with the corrected format
4. Provide statistics about what was fixed

The issue: Some listings have images stored as:
[{'original': 'url', 'small': 'url'}] 

But the API expects them as:
['url', 'url']

Usage:
    python fix_image_format_migration.py [--dry-run] [--confirm]
"""

import sys
import os
import argparse
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.listing import Listing

def normalize_image_data(images: Optional[List[Any]]) -> List[str]:
    """
    Normalize image data to ensure it's a list of URL strings.
    
    Args:
        images: Image data that could be None, list of strings, or list of dicts
        
    Returns:
        List of URL strings
    """
    if not images:
        return []
    
    if not isinstance(images, list):
        return []
    
    normalized_images = []
    for img in images:
        if img is None:
            continue
        elif isinstance(img, str):
            # Already a URL string, keep as-is
            normalized_images.append(img)
        elif isinstance(img, dict):
            # Handle S3 migration format with 'original' key
            if 'original' in img and img['original']:
                normalized_images.append(img['original'])
            # Handle old format with 'paths' key
            elif 'paths' in img and isinstance(img['paths'], dict) and 'standard' in img['paths']:
                normalized_images.append(img['paths']['standard'])
            # Skip if dict doesn't have expected keys
        # Skip other types
    
    return normalized_images

def analyze_image_formats(db: Session) -> Dict[str, Any]:
    """
    Analyze the current state of image formats in the database.
    
    Args:
        db: Database session
        
    Returns:
        Dictionary with analysis results
    """
    analysis = {
        "total_listings": 0,
        "listings_with_images": 0,
        "listings_with_dict_images": 0,
        "listings_with_string_images": 0,
        "listings_with_mixed_images": 0,
        "listings_with_null_images": 0,
        "total_dict_images": 0,
        "total_string_images": 0,
        "problematic_listings": []
    }
    
    all_listings = db.query(Listing).all()
    analysis["total_listings"] = len(all_listings)
    
    for listing in all_listings:
        if listing.images is None:
            analysis["listings_with_null_images"] += 1
            continue
        
        if not isinstance(listing.images, list) or len(listing.images) == 0:
            continue
        
        analysis["listings_with_images"] += 1
        
        dict_count = 0
        string_count = 0
        
        for img in listing.images:
            if isinstance(img, dict):
                dict_count += 1
                analysis["total_dict_images"] += 1
            elif isinstance(img, str):
                string_count += 1
                analysis["total_string_images"] += 1
        
        if dict_count > 0 and string_count > 0:
            analysis["listings_with_mixed_images"] += 1
            analysis["problematic_listings"].append({
                "id": listing.id,
                "title": listing.title[:50] + "..." if len(listing.title) > 50 else listing.title,
                "dict_images": dict_count,
                "string_images": string_count
            })
        elif dict_count > 0:
            analysis["listings_with_dict_images"] += 1
            analysis["problematic_listings"].append({
                "id": listing.id,
                "title": listing.title[:50] + "..." if len(listing.title) > 50 else listing.title,
                "dict_images": dict_count,
                "string_images": string_count
            })
        elif string_count > 0:
            analysis["listings_with_string_images"] += 1
    
    return analysis

def fix_image_formats(db: Session, dry_run: bool = True) -> Dict[str, Any]:
    """
    Fix image formats in all listings.
    
    Args:
        db: Database session
        dry_run: If True, don't actually modify the database
        
    Returns:
        Dictionary with operation results
    """
    results = {
        "processed_listings": 0,
        "fixed_listings": 0,
        "failed_fixes": 0,
        "errors": []
    }
    
    all_listings = db.query(Listing).all()
    
    for listing in all_listings:
        results["processed_listings"] += 1
        
        if not listing.images or not isinstance(listing.images, list):
            continue
        
        # Check if this listing needs fixing
        needs_fixing = False
        for img in listing.images:
            if isinstance(img, dict):
                needs_fixing = True
                break
        
        if not needs_fixing:
            continue
        
        try:
            # Normalize the image data
            original_images = listing.images.copy()
            normalized_images = normalize_image_data(listing.images)
            
            if dry_run:
                print(f"[DRY RUN] Would fix listing {listing.id}: {listing.title[:50]}...")
                print(f"  Original: {len(original_images)} images (some dicts)")
                print(f"  Fixed: {len(normalized_images)} images (all strings)")
                results["fixed_listings"] += 1
            else:
                # Update the listing
                listing.images = normalized_images
                db.commit()
                print(f"✅ Fixed listing {listing.id}: {listing.title[:50]}...")
                print(f"  Converted {len(original_images)} -> {len(normalized_images)} images")
                results["fixed_listings"] += 1
                
        except Exception as e:
            error_msg = f"Failed to fix listing {listing.id}: {str(e)}"
            print(f"❌ {error_msg}")
            results["failed_fixes"] += 1
            results["errors"].append(error_msg)
            if not dry_run:
                db.rollback()
    
    return results

def main():
    """Main function with command line argument handling."""
    parser = argparse.ArgumentParser(description='Fix image data format in listings')
    parser.add_argument('--dry-run', action='store_true', help='Test run without making changes')
    parser.add_argument('--fix', action='store_true', help='Perform actual fixes')
    parser.add_argument('--analyze', action='store_true', help='Analyze current image formats')
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.fix and not args.analyze:
        print("Please specify --dry-run, --fix, or --analyze")
        parser.print_help()
        return
    
    print("🔧 Image Format Migration Tool")
    print("=" * 50)
    
    db = SessionLocal()
    try:
        if args.analyze:
            print("📊 Analyzing current image formats...")
            analysis = analyze_image_formats(db)
            
            print("\n📈 Analysis Results:")
            print(f"Total listings: {analysis['total_listings']}")
            print(f"Listings with images: {analysis['listings_with_images']}")
            print(f"Listings with dict images: {analysis['listings_with_dict_images']}")
            print(f"Listings with string images: {analysis['listings_with_string_images']}")
            print(f"Listings with mixed formats: {analysis['listings_with_mixed_images']}")
            print(f"Listings with null images: {analysis['listings_with_null_images']}")
            print(f"Total dict images: {analysis['total_dict_images']}")
            print(f"Total string images: {analysis['total_string_images']}")
            
            if analysis['problematic_listings']:
                print(f"\n⚠️  {len(analysis['problematic_listings'])} listings need fixing:")
                for listing in analysis['problematic_listings'][:10]:  # Show first 10
                    print(f"  - ID {listing['id']}: {listing['title']} "
                          f"({listing['dict_images']} dicts, {listing['string_images']} strings)")
                if len(analysis['problematic_listings']) > 10:
                    print(f"  ... and {len(analysis['problematic_listings']) - 10} more")
        
        if args.dry_run or args.fix:
            print(f"\n🔄 {'[DRY RUN] ' if args.dry_run else ''}Starting image format fixes...")
            
            results = fix_image_formats(db, dry_run=args.dry_run)
            
            print(f"\n{'🔍 DRY RUN ' if args.dry_run else '✅ '}Results:")
            print(f"Processed listings: {results['processed_listings']}")
            print(f"Fixed listings: {results['fixed_listings']}")
            print(f"Failed fixes: {results['failed_fixes']}")
            
            if results['errors']:
                print(f"\n❌ Errors encountered:")
                for error in results['errors']:
                    print(f"  - {error}")
            
            if args.dry_run and results['fixed_listings'] > 0:
                print(f"\n💡 To apply these fixes, run: python {sys.argv[0]} --fix")
        
    except Exception as e:
        print(f"❌ Script failed: {e}")
        return 1
    finally:
        db.close()
    
    print("\n🎉 Image format migration completed!")
    return 0

if __name__ == "__main__":
    exit(main())