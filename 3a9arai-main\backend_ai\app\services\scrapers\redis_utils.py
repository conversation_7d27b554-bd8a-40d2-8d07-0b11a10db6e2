"""Utility functions for Redis queue management in scrapers."""
import logging
import json
import time
import redis
import os
from typing import Dict, Any, List, Optional, Tuple

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize Redis client
redis_client = redis.Redis.from_url(
    os.environ.get('REDIS_URL', 'redis://redis:6379/0'),
    max_connections=20,  # Limit connections in pool
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True
)

# Queue name constants
URL_QUEUE_SUFFIX = "_url_queue"
DETAILS_QUEUE_SUFFIX = "_details_queue"
PROCESSED_QUEUE_SUFFIX = "_processed_queue"
CONTROL_QUEUE_SUFFIX = "_control_queue"
NEXT_PAGE_QUEUE_SUFFIX = "_next_page_queue"

# Rate limiting constants
LLM_RATE_LIMIT_SUFFIX = "_llm_rate_limit_until"
LLM_RATE_LIMIT_DEFAULT_PAUSE = 30

# Queue marker
QUEUE_MARKER = "__QUEUE_MARKER__"

def get_queue_name(scraper_name: str, queue_type: str) -> str:
    """Get the full queue name for a specific scraper and queue type.
    
    Args:
        scraper_name: Name of the scraper
        queue_type: Type of queue (url, details, processed, control, next_page)
        
    Returns:
        Full queue name
    """
    suffix_map = {
        "url": URL_QUEUE_SUFFIX,
        "details": DETAILS_QUEUE_SUFFIX,
        "processed": PROCESSED_QUEUE_SUFFIX,
        "control": CONTROL_QUEUE_SUFFIX,
        "next_page": NEXT_PAGE_QUEUE_SUFFIX
    }
    
    suffix = suffix_map.get(queue_type)
    if not suffix:
        raise ValueError(f"Invalid queue type: {queue_type}")
        
    return f"{scraper_name}{suffix}"

def init_queue(queue_name: str, is_json: bool = False) -> None:
    """Initialize a Redis queue if it doesn't exist.
    
    Args:
        queue_name: Name of the queue
        is_json: Whether the queue contains JSON data
    """
    # Check if queue exists and is empty
    if redis_client.exists(queue_name) and redis_client.llen(queue_name) == 0:
        if is_json:
            redis_client.lpush(queue_name, json.dumps({"type": "marker"}))
        else:
            redis_client.lpush(queue_name, QUEUE_MARKER)
    # Check if queue doesn't exist at all
    elif not redis_client.exists(queue_name):
        if is_json:
            redis_client.lpush(queue_name, json.dumps({"type": "marker"}))
        else:
            redis_client.lpush(queue_name, QUEUE_MARKER)

def init_scraper_queues(scraper_name: str) -> None:
    """Initialize all queues for a scraper.
    
    Args:
        scraper_name: Name of the scraper
    """
    url_queue = get_queue_name(scraper_name, "url")
    details_queue = get_queue_name(scraper_name, "details")
    processed_queue = get_queue_name(scraper_name, "processed")
    control_queue = get_queue_name(scraper_name, "control")
    
    init_queue(url_queue)
    init_queue(details_queue)
    init_queue(processed_queue)
    init_queue(control_queue, is_json=True)

def purge_queues(scraper_name: str) -> bool:
    """Purge all queues for a scraper.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        True if queues were purged successfully, False otherwise
    """
    try:
        url_queue = get_queue_name(scraper_name, "url")
        details_queue = get_queue_name(scraper_name, "details")
        processed_queue = get_queue_name(scraper_name, "processed")
        
        # Delete all Redis queues
        redis_client.delete(url_queue)
        redis_client.delete(details_queue)
        redis_client.delete(processed_queue)
        
        # Reinitialize queues
        init_scraper_queues(scraper_name)
        
        logger.info(f"All queues for scraper {scraper_name} purged successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error purging queues for scraper {scraper_name}: {str(e)}")
        return False

def purge_scraper_queues_only(scraper_name: str) -> bool:
    """Purge only scraper input queues, preserving LLM processing queues.
    
    This function only purges the URL queue (urls to be scraped) but preserves 
    both details and processed queues which contain items for LLM processing.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        True if queues were purged successfully, False otherwise
    """
    try:
        url_queue = get_queue_name(scraper_name, "url")
        # Only delete the URL queue - preserve details and processed queues for LLM processing
        redis_client.delete(url_queue)
        
        # Reinitialize only the URL queue
        init_queue(url_queue)
        
        logger.info(f"Scraper input queues for {scraper_name} purged successfully (URL queue only)")
        return True
        
    except Exception as e:
        logger.error(f"Error purging scraper queues for {scraper_name}: {str(e)}")
        return False

def get_queue_length(queue_name: str) -> int:
    """Get the length of a queue.
    
    Args:
        queue_name: Name of the queue
        
    Returns:
        Number of items in the queue
    """
    return redis_client.llen(queue_name)

def get_all_queue_lengths(scraper_name: str) -> Dict[str, int]:
    """Get the length of all queues for a scraper.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Dictionary with queue types as keys and their lengths as values
    """
    url_queue = get_queue_name(scraper_name, "url")
    details_queue = get_queue_name(scraper_name, "details")
    processed_queue = get_queue_name(scraper_name, "processed")
    
    return {
        "url_queue": get_queue_length(url_queue),
        "details_queue": get_queue_length(details_queue),
        "processed_queue": get_queue_length(processed_queue)
    }

def add_to_url_queue(scraper_name: str, url: str) -> None:
    """Add a URL to the URL queue.
    
    Args:
        scraper_name: Name of the scraper
        url: URL to add to the queue
    """
    queue_name = get_queue_name(scraper_name, "url")
    redis_client.rpush(queue_name, url)
    logger.debug(f"Added URL to {queue_name}: {url}")

def add_to_details_queue(scraper_name: str, url: str, html: str, images: List[str] = None, property_details: Dict[str, Any] = None) -> None:
    """Add listing details to the details queue.
    
    Args:
        scraper_name: Name of the scraper
        url: URL of the listing
        html: HTML content of the listing
        images: List of image URLs
    """
    queue_name = get_queue_name(scraper_name, "details")
    data = {
        "url": url,
        "html": html,
        "images": images or [],
        "property_details": property_details
    }
    redis_client.rpush(queue_name, json.dumps(data))
    logger.debug(f"Added details to {queue_name} for URL: {url}")

def add_to_processed_queue(scraper_name: str, listing_data: Dict[str, Any]) -> None:
    """Add processed listing data to the processed queue.
    
    Args:
        scraper_name: Name of the scraper
        listing_data: Processed listing data
    """
    try:
        queue_name = get_queue_name(scraper_name, "processed")
        
        # Validate data before adding to queue
        if not isinstance(listing_data, dict):
            logger.error(f"Invalid listing data type: {type(listing_data)}")
            return
            
        if "url" not in listing_data:
            logger.error("Listing data missing required URL field")
            return
            
        # Ensure images are properly filtered
        if "images" in listing_data and listing_data["images"]:
            listing_data["images"] = [img for img in listing_data["images"] if img is not None]
            
        # Check queue limits before adding to LLM queue (for scrapers like Sarouty that use processed queue as LLM queue)
        if scraper_name in ["sarouty"]:  # Add other scrapers that use processed queue as LLM queue
            try:
                from app.services.scrapers.queue_manager import queue_limit_manager
                
                if not queue_limit_manager.should_allow_queue_addition(scraper_name):
                    logger.warning(f"Queue limit reached - not adding {listing_data.get('url')} to {scraper_name} processed queue. Current total queue size: {queue_limit_manager.get_total_llm_queue_size()}")
                    # Trigger queue management check
                    queue_limit_manager.check_and_manage_queue_limits()
                    return
            except ImportError:
                # Fallback if queue manager is not available
                logger.warning("Queue manager not available, adding item anyway")
        
        # Convert to JSON and add to queue
        json_data = json.dumps(listing_data)
        logger.info(f"Adding listing to {queue_name}: {listing_data.get('url')} ({len(json_data)} bytes)")
        result = redis_client.rpush(queue_name, json_data)
        
        # Verify it was added
        logger.debug(f"Added to {queue_name} with result: {result}")
        new_queue_length = redis_client.llen(queue_name)
        logger.debug(f"{queue_name} length is now: {new_queue_length}")
        
    except Exception as e:
        logger.error(f"Error adding to processed queue: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

def get_next_from_url_queue(scraper_name: str) -> Optional[str]:
    """Get the next URL from the URL queue.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        URL or None if queue is empty
    """
    queue_name = get_queue_name(scraper_name, "url")
    url_bytes = redis_client.lpop(queue_name)
    
    if not url_bytes or url_bytes == QUEUE_MARKER.encode('utf-8'):
        return None
        
    return url_bytes.decode('utf-8')

def get_next_from_details_queue(scraper_name: str) -> Optional[Dict[str, Any]]:
    """Get the next details from the details queue.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Details dictionary or None if queue is empty
    """
    queue_name = get_queue_name(scraper_name, "details")
    details_bytes = redis_client.lpop(queue_name)
    
    if not details_bytes or details_bytes == QUEUE_MARKER.encode('utf-8'):
        return None
        
    try:
        return json.loads(details_bytes)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding details from {queue_name}: {str(e)}")
        return None

def get_next_from_processed_queue(scraper_name: str) -> Optional[Dict[str, Any]]:
    """Get the next processed data from the processed queue.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Processed data dictionary or None if queue is empty
    """
    queue_name = get_queue_name(scraper_name, "processed")
    processed_bytes = redis_client.lpop(queue_name)
    
    if not processed_bytes or processed_bytes == QUEUE_MARKER.encode('utf-8'):
        return None
        
    try:
        return json.loads(processed_bytes)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding processed data from {queue_name}: {str(e)}")
        return None

def send_control_command(scraper_name: str, command: str, data: Dict[str, Any] = None) -> None:
    """Send a control command to a scraper.
    
    Args:
        scraper_name: Name of the scraper
        command: Command to send (e.g., "stop", "pause", "resume")
        data: Additional data to include in the command
    """
    queue_name = get_queue_name(scraper_name, "control")
    command_data = {"command": command}
    
    if data:
        command_data.update(data)
        
    redis_client.rpush(queue_name, json.dumps(command_data))
    logger.info(f"Sent control command to {scraper_name}: {command}")

def check_control_queue(scraper_name: str) -> Optional[Dict[str, Any]]:
    """Check the control queue for commands.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Command data dictionary or None if queue is empty
    """
    queue_name = get_queue_name(scraper_name, "control")
    command_bytes = redis_client.lpop(queue_name)
    
    if not command_bytes:
        return None
        
    # Skip queue markers
    if command_bytes == b'' or command_bytes == QUEUE_MARKER.encode('utf-8'):
        return None
        
    try:
        return json.loads(command_bytes)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding command from {queue_name}: {str(e)}")
        return None

def set_scraper_status(scraper_name: str, status: str) -> None:
    """Set the status of a scraper in Redis.
    
    Args:
        scraper_name: Name of the scraper
        status: Status to set (e.g., "running", "stopped", "paused")
    """
    status_key = f"{scraper_name}_status"
    redis_client.set(status_key, status)
    logger.info(f"Set {scraper_name} status to: {status}")

def get_scraper_status(scraper_name: str) -> Optional[str]:
    """Get the status of a scraper from Redis.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Status or None if not found
    """
    status_key = f"{scraper_name}_status"
    status_bytes = redis_client.get(status_key)
    
    if not status_bytes:
        return None
        
    return status_bytes.decode('utf-8')

def acquire_url_collection_lock(scraper_name: str, timeout: int = 300) -> Tuple[bool, str]:
    """Try to acquire the URL collection lock.
    
    Args:
        scraper_name: Name of the scraper
        timeout: Lock timeout in seconds
        
    Returns:
        Tuple of (success, lock_value)
    """
    lock_key = f"{scraper_name}_url_collection_lock"
    lock_value = str(time.time())  # Use timestamp as lock value
    
    # Check if lock exists and its age
    existing_lock = redis_client.get(lock_key)
    if existing_lock:
        try:
            lock_age = time.time() - float(existing_lock.decode('utf-8'))
            if lock_age > timeout:
                # Lock is stale, delete it
                logger.info(f"Found stale lock for {scraper_name} (age: {lock_age}s), removing it")
                redis_client.delete(lock_key)
            else:
                logger.info(f"Another worker is already collecting URLs for {scraper_name}")
                return False, ""
        except (ValueError, TypeError):
            # Invalid lock value, delete it
            redis_client.delete(lock_key)
    
    # Try to acquire lock with NX (only if not exists) and expiration
    if redis_client.set(lock_key, lock_value, ex=timeout, nx=True):
        return True, lock_value
    
    return False, ""

def release_url_collection_lock(scraper_name: str, lock_value: str) -> None:
    """Release the URL collection lock if we own it.
    
    Args:
        scraper_name: Name of the scraper
        lock_value: Lock value to check for ownership
    """
    lock_key = f"{scraper_name}_url_collection_lock"
    current_lock = redis_client.get(lock_key)
    
    if current_lock and current_lock.decode('utf-8') == lock_value:
        redis_client.delete(lock_key)
        logger.info(f"Released URL collection lock for {scraper_name}")

def get_next_page_url(scraper_name: str) -> Optional[str]:
    """Get the next page URL from the next page queue.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Next page URL or None if queue is empty
    """
    queue_name = get_queue_name(scraper_name, "next_page")
    url_bytes = redis_client.lpop(queue_name)
    
    if not url_bytes:
        return None
        
    return url_bytes.decode('utf-8')

def add_next_page_url(scraper_name: str, url: str) -> None:
    """Add a next page URL to the next page queue.
    
    Args:
        scraper_name: Name of the scraper
        url: Next page URL
    """
    queue_name = get_queue_name(scraper_name, "next_page")
    redis_client.rpush(queue_name, url)
    logger.info(f"Added next page URL to {queue_name}: {url}")

def get_last_page(scraper_name: str) -> int:
    """Get the last processed page number for a scraper.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        Last processed page number (defaults to 1 if not found)
    """
    last_page_key = f"{scraper_name}_last_page"
    last_page_bytes = redis_client.get(last_page_key)
    
    if not last_page_bytes:
        return 1
        
    try:
        return int(last_page_bytes.decode('utf-8'))
    except (ValueError, TypeError):
        return 1

def set_last_page(scraper_name: str, page_number: int) -> None:
    """Set the last processed page number for a scraper.
    
    Args:
        scraper_name: Name of the scraper
        page_number: Page number to set
    """
    last_page_key = f"{scraper_name}_last_page"
    redis_client.set(last_page_key, str(page_number))
    logger.debug(f"Set last page for {scraper_name} to: {page_number}")

def set_url_collection_completed(scraper_name: str, completed: bool = True) -> None:
    """Set the URL collection completed flag.
    
    Args:
        scraper_name: Name of the scraper
        completed: Whether URL collection is completed
    """
    completed_key = f"{scraper_name}_url_collection_completed"
    
    if completed:
        redis_client.set(completed_key, "1")
        logger.info(f"Marked URL collection as completed for {scraper_name}")
    else:
        redis_client.delete(completed_key)
        logger.info(f"Marked URL collection as not completed for {scraper_name}")

def is_url_collection_completed(scraper_name: str) -> bool:
    """Check if URL collection is completed.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        True if URL collection is completed, False otherwise
    """
    completed_key = f"{scraper_name}_url_collection_completed"
    completed = redis_client.get(completed_key)
    
    return completed is not None

def get_queue_items(scraper_name: str, queue_type: str, start: int = 0, end: int = 5) -> List[bytes]:
    """Get a range of items from a queue without removing them.
    
    Args:
        scraper_name: Name of the scraper
        queue_type: Type of queue
        start: Start index
        end: End index
        
    Returns:
        List of queue items as bytes
    """
    queue_name = get_queue_name(scraper_name, queue_type)
    return redis_client.lrange(queue_name, start, end)

def set_llm_rate_limited(scraper_name: str, duration: int = LLM_RATE_LIMIT_DEFAULT_PAUSE) -> None:
    """Set the LLM rate limit flag.
    
    Args:
        scraper_name: Name of the scraper
        duration: Duration in seconds to rate limit
    """
    key = f"{scraper_name}{LLM_RATE_LIMIT_SUFFIX}"
    expiry_time = int(time.time()) + duration
    redis_client.set(key, str(expiry_time))
    redis_client.expire(key, duration)
    logger.info(f"Set LLM rate limit for {scraper_name} for {duration} seconds")

def is_llm_rate_limited(scraper_name: str) -> bool:
    """Check if LLM is rate limited for a scraper.
    
    Args:
        scraper_name: Name of the scraper
        
    Returns:
        True if rate limited, False otherwise, and remaining time if available
    """
    key = f"{scraper_name}{LLM_RATE_LIMIT_SUFFIX}"
    rate_limited_until = redis_client.get(key)
    
    if not rate_limited_until:
        return False
        
    try:
        expiry_time = int(rate_limited_until.decode('utf-8'))
        now = int(time.time())
        
        if expiry_time > now:
            remaining = expiry_time - now
            logger.info(f"LLM is rate limited for {scraper_name} for {remaining} more seconds")
            return True
        else:
            # Expired, clean up
            redis_client.delete(key)
            return False
    except (ValueError, TypeError):
        # Invalid value, clean up
        redis_client.delete(key)
        return False

def add_to_sarouty_processed_queue(url: str, property_details: Dict[str, Any], images: List[str] = None) -> None:
    """Add processed Sarouty listing data to the processed queue (special case for Sarouty).
    
    Args:
        url: URL of the listing
        property_details: Property details JSON object
        images: List of image URLs
    """
    queue_name = get_queue_name("sarouty", "processed")
    data = {
        "url": url,
        "property_details": property_details,
        "images": images or []
    }
    redis_client.rpush(queue_name, json.dumps(data))
    logger.debug(f"Added Sarouty processed data to {queue_name} for URL: {url}") 