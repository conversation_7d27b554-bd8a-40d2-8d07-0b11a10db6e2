from pydantic import BaseModel, EmailStr, Field, validator
from typing import List, Optional
import json
from datetime import datetime

class PermissionCheck(BaseModel):
    permission: str

class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None
    permissions: List[str] = []
    
    class Config:
        json_encoders = {
            # Handle custom JSON encoding
            list: lambda v: json.dumps(v)
        }

class RoleCreate(RoleBase):
    pass

class RoleUpdate(RoleBase):
    name: Optional[str] = None
    permissions: Optional[List[str]] = None

class RoleInDBBase(RoleBase):
    id: int
    
    class Config:
        from_attributes = True
        
    @validator('permissions', pre=True)
    def parse_permissions(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v

class Role(RoleInDBBase):
    pass

class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_superuser: bool = False

class UserCreate(UserBase):
    password: str = Field(..., min_length=8)
    password_confirm: str
    
    @validator('password_confirm')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('passwords do not match')
        return v

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None

class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class User(UserInDBBase):
    roles: List[Role] = []

class UserInDB(UserInDBBase):
    hashed_password: str
    roles: List[Role] = []

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    refresh_token: Optional[str] = None

class TokenPayload(BaseModel):
    sub: str
    exp: int
    scopes: List[str] = []
    jti: Optional[str] = None

class TokenData(BaseModel):
    username: Optional[str] = None
    scopes: List[str] = []

class LoginRequest(BaseModel):
    username: str
    password: str 