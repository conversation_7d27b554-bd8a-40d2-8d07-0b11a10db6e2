from typing import List, Any
import os
import uuid
import shutil
from pathlib import Path
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.orm import Session

from app.api import deps
from app.models import User
from app.services.utils.s3_image_utils import save_uploaded_image_to_s3, S3ImageUploader

router = APIRouter()

# Allowed image file types
ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

def validate_image_file(file: UploadFile) -> None:
    """Validate uploaded image file"""
    # Check file extension
    file_extension = os.path.splitext(file.filename or '')[1].lower()
    if file_extension not in ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
        )
    
    # Check file size (approximate check since we haven't read the file yet)
    if hasattr(file, 'size') and file.size and file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
        )

@router.post("/upload", response_model=dict)
async def upload_image(
    file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_user_from_clerk),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Upload an image file and return the filesystem URLs including thumbnails.
    """
    try:
        # Validate the uploaded file
        validate_image_file(file)
        
        # Read file content
        content = await file.read()
        
        # Additional file size check after reading
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Save file and get URLs with thumbnails
        image_urls = save_uploaded_image_to_s3(
            content, 
            file.filename or "image.jpg", 
            current_user.id
        )
        
        # Extract filename from the original URL for response
        filename = image_urls['original'].split('/')[-1]
        
        # Generate S3 key for compatibility
        s3_key = f"property_images/{current_user.id}/{filename}"
        
        # Return success response with all image URLs
        return {
            "success": True,
            "message": "Image uploaded successfully",
            "image_url": image_urls['original'],
            "thumbnail_url": image_urls['small'],  # Use small for backwards compatibility
            "small_url": image_urls['small'],
            "filename": filename,
            "s3_key": s3_key,
            "urls": image_urls  # Include all URLs for future use
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log the error and return a generic error message
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error uploading image: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload image"
        )

@router.post("/upload-multiple", response_model=dict)
async def upload_multiple_images(
    files: List[UploadFile] = File(...),
    current_user: User = Depends(deps.get_current_user_from_clerk),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Upload multiple image files and return filesystem URLs including thumbnails.
    """
    if len(files) > 10:  # Limit to 10 images
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Too many files. Maximum 10 images allowed."
        )
    
    uploaded_images = []
    failed_uploads = []
    
    for file in files:
        try:
            # Validate the uploaded file
            validate_image_file(file)
            
            # Read file content
            content = await file.read()
            
            # Additional file size check after reading
            if len(content) > MAX_FILE_SIZE:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": f"File too large. Maximum size: {MAX_FILE_SIZE // (1024*1024)}MB"
                })
                continue
            
            # Save file and get URLs with thumbnails
            image_urls = save_uploaded_image_to_s3(
                content, 
                file.filename or "image.jpg", 
                current_user.id
            )
            
            # Extract filename from the original URL for response
            filename = image_urls['original'].split('/')[-1]
            
            # Generate S3 key for compatibility
            s3_key = f"property_images/{current_user.id}/{filename}"
            
            uploaded_images.append({
                "success": True,
                "filename": file.filename,
                "image_url": image_urls['original'],
                "thumbnail_url": image_urls['small'],  # Use small for backwards compatibility
                "small_url": image_urls['small'],
                "s3_key": s3_key,
                "urls": image_urls  # Include all URLs for future use
            })
            
        except Exception as e:
            failed_uploads.append({
                "filename": file.filename or "unknown",
                "error": str(e)
            })
    
    return {
        "success": len(uploaded_images) > 0,
        "message": f"Uploaded {len(uploaded_images)} out of {len(files)} images",
        "uploaded_images": uploaded_images,
        "failed_uploads": failed_uploads,
        "total_uploaded": len(uploaded_images),
        "total_failed": len(failed_uploads)
    }

@router.delete("/delete/{filename}")
async def delete_image(
    filename: str,
    current_user: User = Depends(deps.get_current_user_from_clerk),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Delete an uploaded image file from S3.
    """
    try:
        # Initialize S3 uploader
        uploader = S3ImageUploader()
        
        # Construct S3 key for original image
        s3_key = f"property_images/{current_user.id}/{filename}"
        
        # Delete the original image
        success = uploader.delete_image(s3_key)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Image not found or could not be deleted"
            )
        
        # Also try to delete the thumbnail if it exists
        # Generate thumbnail filename
        from pathlib import Path
        stem = Path(filename).stem
        extension = Path(filename).suffix
        thumbnail_filename = f"{stem}_small{extension}"
        thumbnail_key = f"property_images/{current_user.id}/{thumbnail_filename}"
        uploader.delete_image(thumbnail_key)  # Don't fail if thumbnail doesn't exist
        
        return {
            "success": True,
            "message": "Image deleted successfully",
            "filename": filename
        }
        
    except HTTPException:
        raise
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error deleting image: {str(e)}")
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete image"
        ) 