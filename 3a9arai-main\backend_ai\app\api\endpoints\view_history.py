from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.core.config import settings

router = APIRouter()


@router.post("/track", response_model=schemas.ViewHistoryResponse)
def track_listing_view(
    *,
    db: Session = Depends(deps.get_db),
    listing_id: int,
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Track a listing view for the current user.
    """
    # Check if listing exists
    listing = crud.listing.get(db=db, id=listing_id)
    if not listing:
        raise HTTPException(status_code=404, detail="Listing not found")
    
    # Create or update view history
    view_history = crud.view_history.create_or_update_view(
        db=db, user_id=current_user.id, listing_id=listing_id
    )
    
    return view_history


@router.get("/", response_model=List[schemas.ViewHistoryResponse])
def get_user_view_history(
    db: Session = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of items to return"),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Get current user's view history with pagination.
    """
    view_history = crud.view_history.get_user_history(
        db=db, user_id=current_user.id, skip=skip, limit=limit
    )
    
    return view_history


@router.get("/count", response_model=int)
def get_user_view_history_count(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Get total count of current user's view history entries.
    """
    count = crud.view_history.get_user_history_count(
        db=db, user_id=current_user.id
    )
    
    return count


@router.delete("/clear", response_model=dict)
def clear_user_view_history(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Clear all view history for the current user.
    """
    deleted_count = crud.view_history.delete_user_history(
        db=db, user_id=current_user.id
    )
    
    return {"message": f"Deleted {deleted_count} view history entries"}


@router.get("/suggestions", response_model=List[schemas.Listing])
def get_property_suggestions(
    db: Session = Depends(deps.get_db),
    limit: int = Query(6, ge=1, le=20, description="Number of suggestions to return"),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Get property suggestions based on user's viewing history.
    Analyzes user preferences and suggests similar properties they might be interested in.
    """
    suggestions = crud.view_history.get_suggested_properties(
        db=db, user_id=current_user.id, limit=limit
    )
    
    return suggestions


@router.get("/suggestion-criteria")
def get_suggestion_criteria(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user_from_clerk),
) -> Any:
    """
    Get the criteria/filters used to generate property suggestions based on user's viewing history.
    This is useful for displaying filters when navigating to the filtered listings page.
    """
    criteria = crud.view_history.get_suggestion_criteria(
        db=db, user_id=current_user.id
    )
    
    return criteria 