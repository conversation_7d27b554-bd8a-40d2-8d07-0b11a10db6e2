from typing import Any, List, Dict
from fastapi import APIRouter, Depends, HTTPException, Security, Query
from sqlalchemy.orm import Session
from app import crud, models, schemas
from app.api import deps
from app.api.deps import get_current_user_from_clerk
from pydantic import BaseModel
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


class BookmarkCreate(BaseModel):
    listing_id: int


@router.post("/", response_model=dict)
async def create_bookmark(
    *,
    bookmark_in: BookmarkCreate,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_user_from_clerk),
) -> Any:
    """
    Create a new bookmark.
    """
    try:
        bookmark = crud.bookmark.create_bookmark(
            db=db, user_id=current_user.id, listing_id=bookmark_in.listing_id
        )
        return {"success": True, "message": "Listing bookmarked successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error creating bookmark: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not create bookmark: {str(e)}")


@router.delete("/{listing_id}", response_model=dict)
async def delete_bookmark(
    *,
    listing_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_user_from_clerk),
) -> Any:
    """
    Delete a bookmark.
    """
    try:
        crud.bookmark.delete_bookmark(db=db, user_id=current_user.id, listing_id=listing_id)
        return {"success": True, "message": "Bookmark removed successfully"}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error deleting bookmark: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not delete bookmark: {str(e)}")


@router.get("/", response_model=Dict)
async def get_user_bookmarks(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_user_from_clerk),
    skip: int = 0,
    limit: int = 10,
) -> Any:
    """
    Get all bookmarks for the current user.
    """
    try:
        bookmarked_listings, total = crud.bookmark.get_user_bookmarks(
            db=db, user_id=current_user.id, skip=skip, limit=limit
        )
        
        # Convert listings to schemas
        prepared_listings = [schemas.Listing.model_validate(deps.prepare_listing_for_response(listing)) 
                            for listing in bookmarked_listings]
        
        return {
            "items": prepared_listings,
            "total": total,
            "page": skip // limit + 1,
            "limit": limit,
            "pages": (total // limit) + (1 if total % limit > 0 else 0),
        }
        
    except Exception as e:
        logger.error(f"Error getting bookmarks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not get bookmarks: {str(e)}")


@router.get("/is-bookmarked/{listing_id}", response_model=Dict)
async def check_bookmark_status(
    *,
    listing_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(get_current_user_from_clerk),
) -> Any:
    """
    Check if a listing is bookmarked by the current user.
    """
    try:
        is_bookmarked = crud.bookmark.is_bookmarked(
            db=db, user_id=current_user.id, listing_id=listing_id
        )
        return {"is_bookmarked": is_bookmarked}
    except Exception as e:
        logger.error(f"Error checking bookmark status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Could not check bookmark status: {str(e)}") 