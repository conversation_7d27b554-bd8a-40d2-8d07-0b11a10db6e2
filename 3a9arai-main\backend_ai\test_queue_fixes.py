#!/usr/bin/env python3
"""Test script to verify queue management fixes."""

import os
import sys
import time
import logging
from typing import Dict, List

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.scrapers.queue_manager import queue_limit_manager
from app.services.scrapers.redis_utils import redis_client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_memory_monitoring():
    """Test Redis memory monitoring functionality."""
    print("\n=== Testing Memory Monitoring ===")
    
    memory_info = queue_limit_manager.get_redis_memory_usage()
    print(f"Redis Memory Usage: {memory_info['used_memory_mb']}MB / {memory_info['max_memory_mb']}MB ({memory_info['usage_percent']}%)")
    
    is_pressure = queue_limit_manager.is_memory_pressure()
    print(f"Memory Pressure Detected: {is_pressure}")
    
    return memory_info

def test_queue_status():
    """Test queue management status."""
    print("\n=== Testing Queue Status ===")
    
    total_size = queue_limit_manager.get_total_llm_queue_size()
    print(f"Total LLM Queue Size: {total_size}")
    
    paused_scrapers = queue_limit_manager.get_paused_scrapers()
    print(f"Paused Scrapers: {paused_scrapers}")
    
    return total_size, paused_scrapers

def test_queue_management():
    """Test the queue management system."""
    print("\n=== Testing Queue Management ===")
    
    status = queue_limit_manager.check_and_manage_queue_limits()
    print(f"Queue Management Status: {status}")
    
    return status

def test_scraper_status():
    """Test scraper status checking."""
    print("\n=== Testing Scraper Status ===")
    
    scrapers = ["agenz", "sarouty", "avito_sale", "avito_rent"]
    
    for scraper in scrapers:
        is_running = queue_limit_manager.is_scraper_running(scraper)
        print(f"{scraper}: {'running' if is_running else 'not running'}")

def test_queue_cleanup():
    """Test queue cleanup functionality."""
    print("\n=== Testing Queue Cleanup ===")
    
    try:
        cleanup_stats = queue_limit_manager.cleanup_stale_queues()
        print(f"Queue Cleanup Stats: {cleanup_stats}")
        
        memory_cleanup = queue_limit_manager.force_memory_cleanup()
        print(f"Memory Cleanup Performed: {memory_cleanup}")
        
    except Exception as e:
        print(f"Error in queue cleanup: {e}")

def test_redis_connection():
    """Test Redis connection and basic operations."""
    print("\n=== Testing Redis Connection ===")
    
    try:
        # Test basic Redis operations
        redis_client.set("test_key", "test_value", ex=10)
        value = redis_client.get("test_key")
        print(f"Redis Connection Test: {'OK' if value == b'test_value' else 'FAILED'}")
        
        # Test Redis info
        info = redis_client.info()
        print(f"Redis Version: {info.get('redis_version', 'Unknown')}")
        print(f"Redis Uptime: {info.get('uptime_in_seconds', 0)} seconds")
        
    except Exception as e:
        print(f"Redis Connection Error: {e}")
        return False
    
    return True

def test_auto_resume_functionality():
    """Test the auto-resume functionality."""
    print("\n=== Testing Auto-Resume Functionality ===")
    
    try:
        # Test auto-resume list management
        auto_resume_scrapers = queue_limit_manager.get_auto_resume_scrapers()
        print(f"Current auto-resume scrapers: {auto_resume_scrapers}")
        
        # Test adding to auto-resume list
        queue_limit_manager.add_to_auto_resume("test_scraper")
        updated_list = queue_limit_manager.get_auto_resume_scrapers()
        print(f"After adding test_scraper: {updated_list}")
        
        # Test removing from auto-resume list
        queue_limit_manager.remove_from_auto_resume("test_scraper")
        final_list = queue_limit_manager.get_auto_resume_scrapers()
        print(f"After removing test_scraper: {final_list}")
        
        # Test manual trigger
        result = queue_limit_manager.trigger_auto_resume_all()
        print(f"Manual auto-resume result: {result}")
        
    except Exception as e:
        print(f"Error in auto-resume test: {e}")

def test_detailed_status():
    """Test the detailed queue management status."""
    print("\n=== Testing Detailed Status ===")
    
    try:
        from app.services.scrapers.queue_manager import get_queue_management_status
        
        # Get detailed status including auto-resume info
        status = get_queue_management_status()
        
        print(f"Paused scrapers: {status.get('paused_scrapers', [])}")
        print(f"Auto-resume scrapers: {status.get('auto_resume_scrapers', [])}")
        print(f"Memory pressure: {status.get('memory_pressure', False)}")
        print(f"Actions taken: {status.get('actions_taken', [])}")
        
        return status
        
    except Exception as e:
        print(f"Error getting detailed status: {e}")
        return {}

def test_scraper_status_with_queue_info():
    """Test scraper status including queue management information."""
    print("\n=== Testing Scraper Status with Queue Info ===")
    
    scrapers = ["agenz", "sarouty", "avito_sale", "avito_rent"]
    
    for scraper in scrapers:
        is_running = queue_limit_manager.is_scraper_running(scraper)
        
        # Get paused/auto-resume status
        paused_scrapers = queue_limit_manager.get_paused_scrapers()
        auto_resume_scrapers = queue_limit_manager.get_auto_resume_scrapers()
        
        is_paused = scraper in paused_scrapers
        is_auto_resume = scraper in auto_resume_scrapers
        
        print(f"{scraper}:")
        print(f"  - Running: {is_running}")
        print(f"  - Paused for queue: {is_paused}")
        print(f"  - In auto-resume: {is_auto_resume}")
        print(f"  - Should be resumed: {is_paused or is_auto_resume}")

def simulate_queue_pressure_scenario():
    """Simulate a queue pressure scenario to test the fixes."""
    print("\n=== Simulating Queue Pressure Scenario ===")
    
    try:
        # Get current status
        initial_status = queue_limit_manager.check_and_manage_queue_limits()
        print(f"Initial status: {initial_status.get('total_queue_size', 0)} items")
        
        # Add some test scrapers to auto-resume list (simulate they were running before)
        for scraper in ["sarouty", "avito_sale", "avito_rent"]:
            if not queue_limit_manager.is_scraper_running(scraper):
                queue_limit_manager.add_to_auto_resume(scraper)
                print(f"Added {scraper} to auto-resume list (simulating it was running before)")
        
        # Check status after adding to auto-resume
        updated_status = queue_limit_manager.check_and_manage_queue_limits()
        print(f"Actions taken: {updated_status.get('actions_taken', [])}")
        
        # Manually trigger auto-resume to test the functionality
        print("Manually triggering auto-resume...")
        result = queue_limit_manager.trigger_auto_resume_all()
        print(f"Auto-resume result: {result}")
        
    except Exception as e:
        print(f"Error in simulation: {e}")

def main():
    """Main test function."""
    print("Queue Management Fix Test Script")
    print("=" * 50)
    
    # Test Redis connection first
    if not test_redis_connection():
        print("Redis connection failed. Exiting.")
        return False
    
    # Test memory monitoring
    memory_info = test_memory_monitoring()
    
    # Test queue status
    total_size, paused_scrapers = test_queue_status()
    
    # Test scraper status
    test_scraper_status()
    
    # Test queue management
    status = test_queue_management()
    
    # Test queue cleanup
    test_queue_cleanup()
    
    # Test auto-resume functionality
    test_auto_resume_functionality()
    
    # Test detailed status
    detailed_status = test_detailed_status()
    
    # Test scraper status with queue info
    test_scraper_status_with_queue_info()
    
    # Simulate queue pressure scenario
    simulate_queue_pressure_scenario()
    
    # Summary
    print("\n=== Test Summary ===")
    print(f"Redis Memory: {memory_info['used_memory_mb']}MB ({memory_info['usage_percent']}%)")
    print(f"Queue Size: {total_size}")
    print(f"Paused Scrapers: {len(paused_scrapers)}")
    print(f"Auto-Resume Scrapers: {len(detailed_status.get('auto_resume_scrapers', []))}")
    print(f"Memory Pressure: {status.get('memory_pressure', False)}")
    print(f"Actions Taken: {status.get('actions_taken', [])}")
    
    # Check if fixes are working
    print("\n=== Fix Verification ===")
    
    # Check if Redis is using noeviction policy
    try:
        config = redis_client.config_get("maxmemory-policy")
        policy = config.get("maxmemory-policy", "unknown")
        print(f"Redis Eviction Policy: {policy}")
        if policy == "noeviction":
            print("✓ Redis eviction policy fixed - keys won't be evicted")
        else:
            print("✗ Redis eviction policy not set to noeviction")
    except Exception as e:
        print(f"Could not check Redis eviction policy: {e}")
    
    # Check queue limits
    from app.services.scrapers.queue_manager import MAX_QUEUE_SIZE, RESUME_QUEUE_SIZE
    print(f"Max Queue Size: {MAX_QUEUE_SIZE}")
    print(f"Resume Queue Size: {RESUME_QUEUE_SIZE}")
    
    if MAX_QUEUE_SIZE <= 1500 and RESUME_QUEUE_SIZE <= 750:
        print("✓ Queue limits reduced to prevent memory issues")
    else:
        print("✗ Queue limits not properly reduced")
    
    # Check auto-resume functionality
    auto_resume_count = len(detailed_status.get('auto_resume_scrapers', []))
    if auto_resume_count >= 0:  # Just check that we can get the list
        print("✓ Auto-resume functionality is working")
    else:
        print("✗ Auto-resume functionality not working")
    
    # Check frontend status integration
    try:
        # Simulate what the frontend would receive
        for scraper in ["agenz", "sarouty", "avito_sale", "avito_rent"]:
            queue_mgmt_status = detailed_status
            is_paused_for_queue = scraper in queue_mgmt_status.get("paused_scrapers", [])
            
            if is_paused_for_queue:
                print(f"✓ Frontend will see {scraper} as paused for queue limits")
        
        print("✓ Frontend status integration working")
    except Exception as e:
        print(f"✗ Frontend status integration error: {e}")
    
    print("\nTest completed!")
    return True

if __name__ == "__main__":
    main() 