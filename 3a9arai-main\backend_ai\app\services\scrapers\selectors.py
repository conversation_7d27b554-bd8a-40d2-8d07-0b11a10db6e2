"""Configuration file for all XPaths and class names used in scrapers."""

# Agenz selectors
AGENZ_SELECTORS = {
    # Search page selectors
    "scroll_container": '//*[@id="scroll--container"]',
    "listing_card": '//*[contains(@class, "_listingCard")]',
    "listing_card_data_url": "data-url",
    "next_button": '//img[@alt="Next Icon"]',
    
    # Listing details page selectors
    "listing_details": "//*[contains(@class, '_annonceDetailsContainer')]",
    "image_item": "//*[contains(@class, '_imageItem')]",
    "image_src": "data-name",
}

# Common selectors that might be used across different scrapers
COMMON_SELECTORS = {
    "wait_timeout": 10,  # Default wait timeout in seconds
} 