from typing import Optional, List, Dict, Union, Any
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl
from enum import Enum

class ApprovalStatus(str, Enum):
    pending = "pending"
    approved = "approved"
    rejected = "rejected"

class AdditionalInfo(BaseModel):
    """A structured representation of a single piece of extracted information."""
    name: str = Field(..., description="The name of the feature or attribute.")
    value: Any = Field(..., description="The value of the feature or attribute.")
    notes_for_user: Optional[str] = Field(None, description="Optional context, warnings, or clarifications for the end-user.")

# Shared properties
class ListingBase(BaseModel):
    # Core Vitals (URL optional for user-generated listings)
    url: Optional[str] = Field(None, description="The source URL of the listing (optional for user-generated).")
    title: str = Field(..., description="A full, descriptive title for the property page.")
    price: float = Field(..., description="The listing price as a numeric value.")
    size_sqm: float = Field(..., description="The total size of the property in square meters.")
    note_about_size_sqm: Optional[str] = Field("Surface habitable", description="Note about the size measurement.")
    city: str = Field(..., description="The city where the property is located.")
    property_type: str = Field(..., description="Primary type of the property (e.g., Appartement, Villa).")
    property_category: str = Field(..., description="High-level category: 'residential', 'commercial', 'land'.")
    transaction_type: str = Field(default='sell', description="Type of transaction: sell or rent.")
    description_html: str = Field(..., description="A well-structured HTML description.")
    contact_name: str = Field(..., description="The name of the contact person.")
    contact_phone: str = Field(..., description="The phone number of the contact person.")
    contact_email: Optional[str] = Field(None, description="The email of the contact person.")
    images: List[str] = Field(..., description="The images of the property.")
    
    # Flexible, Structured Information
    additional_info: List[AdditionalInfo] = Field(
        default_factory=list,
        description="A list of all other extracted details and amenities, each as a name-value pair."
    )
    
    # Location, Contact & Metadata
    neighborhood: Optional[str] = Field(None, description="The specific neighborhood or district.")
    scraper: Optional[str] = Field(None, description="The name of the scraper used (optional for user-generated).")
    price_per_sqm: Optional[float] = Field(None, description="The price per square meter of the property.")
    
    # Legacy fields for backward compatibility
    card_title: Optional[str] = None
    description: Optional[str] = None  # Will be mapped from description_html
    gps_coordinates: Optional[Dict[str, float]] = None
    image_url: Optional[str] = None
    date_posted: Optional[str] = None
    contact_info: Optional[str] = None
    size: Optional[float] = None  # For backward compatibility, will map to size_sqm
    source_website: Optional[str] = None
    
    # Legacy property detail fields that will be in additional_info
    rooms: Optional[int] = None
    bathrooms: Optional[int] = None
    
    # Additional backward compatibility fields
    location: Optional[str] = None
    is_part_of_project: Optional[bool] = None
    commercial_type: Optional[str] = None
    has_showroom: Optional[bool] = None
    has_storage: Optional[bool] = None
    has_reception: Optional[bool] = None
    has_meeting_rooms: Optional[bool] = None
    is_furnished: Optional[bool] = None
    land_type: Optional[str] = None
    land_zoning: Optional[str] = None
    is_serviced: Optional[bool] = None
    is_residential_project: Optional[bool] = None
    residential_project_name: Optional[str] = None
    floor: Optional[int] = None
    total_floors: Optional[int] = None
    building_age: Optional[str] = None
    elevator: Optional[bool] = None
    parking: Optional[bool] = None
    parking_type: Optional[str] = None
    parking_spaces: Optional[int] = None
    orientation: Optional[str] = None
    has_balcony: Optional[bool] = None
    balcony_size: Optional[float] = None
    has_terrace: Optional[bool] = None
    terrace_size: Optional[float] = None
    has_garden: Optional[bool] = None
    garden_size: Optional[float] = None
    indoor_features: Optional[List[str]] = None
    outdoor_features: Optional[List[str]] = None
    security_features: Optional[List[str]] = None
    nearby_amenities: Optional[List[str]] = None
    features: Optional[List[str]] = None
    monthly_fees: Optional[float] = None
    website_source: Optional[str] = None

# Properties to receive on listing creation
class ListingCreate(ListingBase):
    pass

# Properties to receive on listing update
class ListingUpdate(BaseModel):
    # Core fields (all optional for updates)
    url: Optional[str] = None
    title: Optional[str] = None
    price: Optional[float] = None
    size_sqm: Optional[float] = None
    note_about_size_sqm: Optional[str] = None
    city: Optional[str] = None
    property_type: Optional[str] = None
    property_category: Optional[str] = None
    transaction_type: Optional[str] = None
    description_html: Optional[str] = None
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None
    contact_email: Optional[str] = None
    images: Optional[List[str]] = None
    additional_info: Optional[List[AdditionalInfo]] = None
    neighborhood: Optional[str] = None
    scraper: Optional[str] = None
    price_per_sqm: Optional[float] = None
    
    # Legacy fields
    card_title: Optional[str] = None
    description: Optional[str] = None
    gps_coordinates: Optional[Dict[str, float]] = None
    image_url: Optional[str] = None
    date_posted: Optional[str] = None
    contact_info: Optional[str] = None
    size: Optional[float] = None
    source_website: Optional[str] = None
    rooms: Optional[int] = None
    bathrooms: Optional[int] = None
    location: Optional[str] = None
    is_part_of_project: Optional[bool] = None
    commercial_type: Optional[str] = None
    has_showroom: Optional[bool] = None
    has_storage: Optional[bool] = None
    has_reception: Optional[bool] = None
    has_meeting_rooms: Optional[bool] = None
    is_furnished: Optional[bool] = None
    land_type: Optional[str] = None
    land_zoning: Optional[str] = None
    is_serviced: Optional[bool] = None
    is_residential_project: Optional[bool] = None
    residential_project_name: Optional[str] = None
    floor: Optional[int] = None
    total_floors: Optional[int] = None
    building_age: Optional[str] = None
    elevator: Optional[bool] = None
    parking: Optional[bool] = None
    parking_type: Optional[str] = None
    parking_spaces: Optional[int] = None
    orientation: Optional[str] = None
    has_balcony: Optional[bool] = None
    balcony_size: Optional[float] = None
    has_terrace: Optional[bool] = None
    terrace_size: Optional[float] = None
    has_garden: Optional[bool] = None
    garden_size: Optional[float] = None
    indoor_features: Optional[List[str]] = None
    outdoor_features: Optional[List[str]] = None
    security_features: Optional[List[str]] = None
    nearby_amenities: Optional[List[str]] = None
    features: Optional[List[str]] = None
    monthly_fees: Optional[float] = None
    website_source: Optional[str] = None

# Properties shared by models stored in DB
class ListingInDBBase(ListingBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Properties to return to client
class Listing(ListingInDBBase):
    view_count_24h: Optional[int] = Field(None, description="Number of unique views in the past 24 hours")
    is_bookmarked: Optional[bool] = Field(False, description="Whether the listing is bookmarked by the current user")
    
    # Approval system fields
    approval_status: ApprovalStatus = Field(ApprovalStatus.approved, description="Approval status of the listing")
    is_user_generated: bool = Field(False, description="Whether the listing was created by a user")
    approved_by: Optional[int] = Field(None, description="ID of the user who approved the listing")
    approval_date: Optional[datetime] = Field(None, description="Date when the listing was approved")
    rejection_reason: Optional[str] = Field(None, description="Reason for rejection if applicable")
    
    # User ownership information
    user_id: Optional[int] = Field(None, description="ID of the user who owns this listing")
    
    # User information for user-generated listings
    owner_name: Optional[str] = Field(None, description="Name of the user who created the listing (for user-generated listings only)")

# Properties stored in DB
class ListingInDB(ListingInDBBase):
    pass

# Listing filters
class ListingFilters(BaseModel):
    cities: Optional[List[str]] = None
    neighborhoods: Optional[List[str]] = None
    property_types: Optional[List[str]] = None
    property_category: Optional[str] = None
    transaction_type: Optional[str] = None
    commercial_type: Optional[str] = None
    land_type: Optional[str] = None
    price_min: Optional[float] = None
    price_max: Optional[float] = None
    price_range: Optional[str] = None
    size_min: Optional[float] = None
    size_max: Optional[float] = None
    rooms_min: Optional[int] = None
    rooms_max: Optional[int] = None
    bathrooms_min: Optional[int] = None 
    bathrooms_max: Optional[int] = None
    has_elevator: Optional[bool] = None
    has_parking: Optional[bool] = None
    parking_spaces_min: Optional[int] = None
    has_balcony: Optional[bool] = None
    has_terrace: Optional[bool] = None
    has_garden: Optional[bool] = None
    is_furnished: Optional[bool] = None
    has_storage: Optional[bool] = None
    is_serviced: Optional[bool] = None
    is_residential_project: Optional[bool] = None
    website_source: Optional[str] = None
    page: Optional[int] = 1
    limit: Optional[int] = 10
    sort_by: Optional[str] = "created_at"
    sort_order: Optional[str] = "desc" 