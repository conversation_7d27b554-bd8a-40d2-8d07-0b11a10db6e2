import os
import boto3
import hashlib
import logging
import uuid
import random
from io import Bytes<PERSON>
from pathlib import Path
from typing import Optional, Dict, List, Tuple
from urllib.parse import urlparse

import requests
from PIL import Image
from botocore.exceptions import ClientError, NoCredentialsError
from botocore.client import Config

logger = logging.getLogger(__name__)

# Configuration from environment variables
DO_SPACES_ACCESS_KEY = os.environ.get("DO_SPACES_ACCESS_KEY")
DO_SPACES_SECRET_KEY = os.environ.get("DO_SPACES_SECRET_KEY")
DO_SPACES_BUCKET_NAME = os.environ.get("DO_SPACES_BUCKET_NAME")
DO_SPACES_REGION = os.environ.get("DO_SPACES_REGION", "nyc3")
DO_SPACES_ENDPOINT = os.environ.get("DO_SPACES_ENDPOINT", "https://nyc3.digitaloceanspaces.com")

# User agents for web scraping to avoid bot detection
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
]


class S3ImageUploader:
    """S3-compatible image uploader for Digital Ocean Spaces."""
    
    def __init__(self):
        """Initialize the S3 client for Digital Ocean Spaces."""
        if not all([DO_SPACES_ACCESS_KEY, DO_SPACES_SECRET_KEY, DO_SPACES_BUCKET_NAME]):
            raise ValueError("Missing required Digital Ocean Spaces configuration. Please check environment variables.")
        
        self.session = boto3.Session(
            aws_access_key_id=DO_SPACES_ACCESS_KEY,
            aws_secret_access_key=DO_SPACES_SECRET_KEY,
            region_name=DO_SPACES_REGION
        )
        
        # Configure S3 client for Digital Ocean Spaces
        self.s3_client = self.session.client(
            's3',
            endpoint_url=DO_SPACES_ENDPOINT,
            config=Config(signature_version='s3v4')
        )
        
        self.bucket_name = DO_SPACES_BUCKET_NAME
        self.cdn_url = f"https://{DO_SPACES_BUCKET_NAME}.{DO_SPACES_REGION}.cdn.digitaloceanspaces.com"
        
    def upload_image_bytes(self, image_bytes: bytes, key: str, content_type: str = "image/jpeg") -> Optional[str]:
        """
        Upload image bytes to S3.
        
        Args:
            image_bytes: Raw image bytes
            key: S3 object key (path)
            content_type: MIME type of the image
            
        Returns:
            Public URL of uploaded image or None if failed
        """
        try:
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=key,
                Body=image_bytes,
                ContentType=content_type,
                ACL='public-read'  # Make the image publicly accessible
            )
            
            # Return CDN URL for faster access
            return f"{self.cdn_url}/{key}"
            
        except (ClientError, NoCredentialsError) as e:
            logger.error(f"Error uploading image to S3: {str(e)}")
            return None
            
    def delete_image(self, key: str) -> bool:
        """
        Delete an image from S3.
        
        Args:
            key: S3 object key (path)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=key)
            return True
        except ClientError as e:
            logger.error(f"Error deleting image from S3: {str(e)}")
            return False
            
    def check_bucket_exists(self) -> bool:
        """Check if the configured bucket exists and is accessible."""
        try:
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            return True
        except ClientError:
            return False


def create_thumbnail_from_bytes(image_bytes: bytes, max_size: Tuple[int, int]) -> Optional[bytes]:
    """
    Create a thumbnail from image bytes while preserving aspect ratio.
    
    Args:
        image_bytes: Raw image bytes
        max_size: Tuple of (max_width, max_height)
        
    Returns:
        Thumbnail image bytes or None if creation failed
    """
    try:
        with Image.open(BytesIO(image_bytes)) as img:
            # Convert to RGB if necessary (handles transparency)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create a white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Calculate thumbnail size while preserving aspect ratio
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Save to bytes
            thumbnail_bytes = BytesIO()
            img.save(thumbnail_bytes, format='JPEG', optimize=True, quality=85)
            return thumbnail_bytes.getvalue()
            
    except Exception as e:
        logger.error(f"Error creating thumbnail: {str(e)}")
        return None


def download_image_to_s3(image_url: str, retry_count: int = 0) -> Optional[dict]:
    """
    Download an image from a URL and upload it to S3 with thumbnails.
    
    Args:
        image_url: URL of the image to download
        retry_count: Number of retry attempts made
        
    Returns:
        Dictionary with original and thumbnail URLs or None if download failed
    """
    if retry_count >= 3:
        logger.error(f"Max retries reached for {image_url}")
        return None
        
    try:
        uploader = S3ImageUploader()
        
        # Generate a unique filename based on the URL
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        
        # Extract file extension from URL
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        
        # If no valid extension found, default to .jpg
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
            
        # Create filename with hash and extension
        filename = f"{url_hash}{extension}"
        s3_key = f"property_images/{filename}"
        
        # Check if file already exists in S3 by trying to get its metadata
        try:
            uploader.s3_client.head_object(Bucket=uploader.bucket_name, Key=s3_key)
            # File exists, return existing URLs
            return get_s3_image_urls(filename)
        except ClientError as e:
            if e.response['Error']['Code'] != '404':
                logger.error(f"Error checking if image exists in S3: {str(e)}")
                return None
            # File doesn't exist, continue with download
        
        # Extract domain for referer header
        domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        
        # Set browser-like headers to avoid bot detection
        headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Referer": domain,
            "Origin": domain,
            "sec-ch-ua": '"Not.A/Brand";v="8", "Chromium";v="123", "Google Chrome";v="123"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "image",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-origin",
            "DNT": "1",
            "Connection": "keep-alive",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }
        
        # Download the image
        response = requests.get(image_url, headers=headers, timeout=30, stream=True)
        response.raise_for_status()
        
        # Read image bytes
        image_bytes = response.content
        
        # Validate image size and type
        if len(image_bytes) < 1024:  # Less than 1KB
            logger.warning(f"Image too small: {len(image_bytes)} bytes")
            return None
            
        if len(image_bytes) > 50 * 1024 * 1024:  # More than 50MB
            logger.warning(f"Image too large: {len(image_bytes)} bytes")
            return None
        
        # Validate that it's actually an image
        try:
            with Image.open(BytesIO(image_bytes)) as img:
                img.verify()
        except Exception as e:
            logger.warning(f"Invalid image format: {str(e)}")
            return None
        
        # Upload original image to S3
        content_type = f"image/{extension[1:]}" if extension else "image/jpeg"
        original_url = uploader.upload_image_bytes(image_bytes, s3_key, content_type)
        
        if not original_url:
            logger.error(f"Failed to upload original image to S3: {image_url}")
            return None
        
        # Create and upload thumbnail
        thumbnail_bytes = create_thumbnail_from_bytes(image_bytes, (400, 400))
        if thumbnail_bytes:
            thumbnail_key = f"property_images/{url_hash}_small{extension}"
            thumbnail_url = uploader.upload_image_bytes(thumbnail_bytes, thumbnail_key, "image/jpeg")
        else:
            logger.warning(f"Failed to create thumbnail for {image_url}, using original")
            thumbnail_url = original_url
        
        logger.info(f"Successfully uploaded image to S3: {original_url}")
        
        return {
            'original': original_url,
            'small': thumbnail_url or original_url
        }
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error downloading image: {str(e)}")
        if retry_count < 2:
            logger.info(f"Retrying download for {image_url}")
            return download_image_to_s3(image_url, retry_count + 1)
        return None
    except Exception as e:
        logger.error(f"Unexpected error in download_image_to_s3: {str(e)}")
        return None


def save_uploaded_image_to_s3(file_content: bytes, filename: str, user_id: Optional[int] = None) -> dict:
    """
    Save uploaded image content to S3 with thumbnails and return URLs.
    
    Args:
        file_content: Binary content of the image file
        filename: Original filename
        user_id: Optional user ID for user-specific directory
        
    Returns:
        Dictionary with original and thumbnail URLs
    """
    try:
        uploader = S3ImageUploader()
        
        # Generate unique filename to avoid conflicts
        file_extension = os.path.splitext(filename)[1].lower()
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        
        # Create S3 key with user-specific directory if user_id provided
        if user_id:
            s3_key = f"property_images/{user_id}/{unique_filename}"
        else:
            s3_key = f"property_images/{unique_filename}"
        
        # Determine content type
        content_type_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.webp': 'image/webp',
            '.gif': 'image/gif'
        }
        content_type = content_type_map.get(file_extension, 'image/jpeg')
        
        # Upload original image
        original_url = uploader.upload_image_bytes(file_content, s3_key, content_type)
        
        if not original_url:
            raise Exception("Failed to upload original image to S3")
        
        # Create and upload thumbnail
        thumbnail_bytes = create_thumbnail_from_bytes(file_content, (400, 400))
        if thumbnail_bytes:
            # Create thumbnail key
            stem = Path(unique_filename).stem
            extension = Path(unique_filename).suffix
            if user_id:
                thumbnail_key = f"property_images/{user_id}/{stem}_small{extension}"
            else:
                thumbnail_key = f"property_images/{stem}_small{extension}"
            
            thumbnail_url = uploader.upload_image_bytes(thumbnail_bytes, thumbnail_key, "image/jpeg")
        else:
            logger.warning("Failed to create thumbnail, using original")
            thumbnail_url = original_url
        
        return {
            'original': original_url,
            'small': thumbnail_url or original_url
        }
        
    except Exception as e:
        logger.error(f"Error saving uploaded image to S3: {str(e)}")
        raise


def get_s3_image_urls(filename: str, user_id: Optional[int] = None) -> dict:
    """
    Generate S3 URLs for an image and its thumbnail.
    
    Args:
        filename: Image filename
        user_id: Optional user ID for user-specific directory
        
    Returns:
        Dictionary with original and thumbnail URLs
    """
    try:
        uploader = S3ImageUploader()
        
        # Generate original and thumbnail URLs
        if user_id:
            original_key = f"property_images/{user_id}/{filename}"
            # Generate thumbnail filename
            stem = Path(filename).stem
            extension = Path(filename).suffix
            thumbnail_filename = f"{stem}_small{extension}"
            thumbnail_key = f"property_images/{user_id}/{thumbnail_filename}"
        else:
            original_key = f"property_images/{filename}"
            # Generate thumbnail filename
            stem = Path(filename).stem
            extension = Path(filename).suffix
            thumbnail_filename = f"{stem}_small{extension}"
            thumbnail_key = f"property_images/{thumbnail_filename}"
        
        return {
            'original': f"{uploader.cdn_url}/{original_key}",
            'small': f"{uploader.cdn_url}/{thumbnail_key}"
        }
        
    except Exception as e:
        logger.error(f"Error generating S3 URLs: {str(e)}")
        return {
            'original': '',
            'small': ''
        }


def download_with_referer_chain_s3(image_url: str) -> Optional[dict]:
    """
    Download image with referer chain handling and upload to S3.
    
    Args:
        image_url: URL of the image to download
        
    Returns:
        Dictionary with original and thumbnail URLs or None if download failed
    """
    try:
        uploader = S3ImageUploader()
        
        # Generate unique filename
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        extension = '.jpg'  # Default extension
        
        # Try to extract extension from URL
        parsed_url = urlparse(image_url)
        if parsed_url.path:
            path_extension = os.path.splitext(parsed_url.path)[1]
            if path_extension.lower() in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
                extension = path_extension
        
        filename = f"{url_hash}{extension}"
        s3_key = f"property_images/{filename}"
        
        # Check if already exists
        try:
            uploader.s3_client.head_object(Bucket=uploader.bucket_name, Key=s3_key)
            return get_s3_image_urls(filename)
        except ClientError as e:
            if e.response['Error']['Code'] != '404':
                logger.error(f"Error checking S3 object: {str(e)}")
                return None
        
        # Multiple download attempts with different strategies
        session = requests.Session()
        
        # Strategy 1: Basic request
        try:
            headers = {"User-Agent": random.choice(USER_AGENTS)}
            response = session.get(image_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            if response.content and len(response.content) > 1024:
                return upload_and_create_thumbnails_s3(response.content, filename, extension, uploader)
        except:
            pass
        
        # Strategy 2: With referer
        try:
            parsed_url = urlparse(image_url)
            domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
            headers = {
                "User-Agent": random.choice(USER_AGENTS),
                "Referer": domain
            }
            response = session.get(image_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            if response.content and len(response.content) > 1024:
                return upload_and_create_thumbnails_s3(response.content, filename, extension, uploader)
        except:
            pass
        
        logger.error(f"All download strategies failed for: {image_url}")
        return None
        
    except Exception as e:
        logger.error(f"Referer chain download failed: {str(e)}")
        return None


def upload_and_create_thumbnails_s3(image_bytes: bytes, filename: str, extension: str, uploader: S3ImageUploader) -> Optional[dict]:
    """
    Upload image bytes to S3 and create thumbnails.
    
    Args:
        image_bytes: Raw image bytes
        filename: Base filename
        extension: File extension
        uploader: S3ImageUploader instance
        
    Returns:
        Dictionary with URLs or None if failed
    """
    try:
        # Validate image
        try:
            with Image.open(BytesIO(image_bytes)) as img:
                img.verify()
        except Exception as e:
            logger.warning(f"Invalid image format: {str(e)}")
            return None
        
        # Upload original
        s3_key = f"property_images/{filename}"
        content_type = f"image/{extension[1:]}" if extension else "image/jpeg"
        original_url = uploader.upload_image_bytes(image_bytes, s3_key, content_type)
        
        if not original_url:
            return None
        
        # Create and upload thumbnail
        thumbnail_bytes = create_thumbnail_from_bytes(image_bytes, (400, 400))
        if thumbnail_bytes:
            stem = Path(filename).stem
            thumbnail_filename = f"{stem}_small{extension}"
            thumbnail_key = f"property_images/{thumbnail_filename}"
            thumbnail_url = uploader.upload_image_bytes(thumbnail_bytes, thumbnail_key, "image/jpeg")
        else:
            thumbnail_url = original_url
        
        return {
            'original': original_url,
            'small': thumbnail_url or original_url
        }
        
    except Exception as e:
        logger.error(f"Error uploading to S3: {str(e)}")
        return None


class S3ImageProcessor:
    """Utility class for processing images with S3 storage."""
    
    @staticmethod
    def process_images_for_s3(image_urls: List[str]) -> List[dict]:
        """Process images by downloading them and uploading to S3 with thumbnails.
        
        Args:
            image_urls: List of original image URLs
            
        Returns:
            List of dictionaries with original and thumbnail URLs
        """
        s3_urls = []
        
        for image_url in image_urls:
            if not image_url:
                continue
                
            try:
                # Check if it's already an S3 URL
                if "digitaloceanspaces.com" in image_url:
                    # Already in S3, extract filename and generate URLs
                    # Extract filename from URL
                    url_parts = image_url.split("/")
                    if "property_images" in url_parts:
                        idx = url_parts.index("property_images")
                        if idx + 1 < len(url_parts):
                            filename = url_parts[idx + 1]
                            # Remove thumbnail suffix if present
                            if "_small" in filename:
                                filename = filename.replace("_small", "")
                            s3_urls.append(get_s3_image_urls(filename))
                            continue
                
                # Download the image and upload to S3
                image_data = download_image_to_s3(image_url)
                
                if image_data:
                    s3_urls.append(image_data)
                else:
                    # Fallback: create a dict with the original URL for both sizes
                    s3_urls.append({
                        'original': image_url,
                        'small': image_url
                    })
                    
            except Exception as e:
                logger.error(f"Error processing image: {str(e)}")
                # Fallback: create a dict with the original URL for both sizes
                s3_urls.append({
                    'original': image_url,
                    'small': image_url
                })
        
        return s3_urls 