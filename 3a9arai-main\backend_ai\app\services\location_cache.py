"""Location cache service for city and neighborhood mapping."""
import json
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from fuzzywuzzy import fuzz, process

from app.services.scrapers.redis_utils import redis_client
from app.models.listing import Listing

logger = logging.getLogger(__name__)

class LocationCacheService:
    """Redis cache service for location mappings and suggestions."""
    
    def __init__(self):
        """Initialize the location cache service."""
        self.redis = redis_client
        self.cache_ttl = 86400  # 24 hours TTL for location cache
        self.suggestion_cache_key = "location_suggestions"
        self.city_neighborhoods_cache_key = "city_neighborhoods_mapping"
        
    def update_location_mappings(self, db: Session) -> None:
        """
        Update the city and neighborhood mappings in Redis based on current listings.
        This should be run as a background task periodically.
        """
        try:
            logger.info("Starting location mappings update...")
            
            # Get all cities with their listing counts
            cities_query = db.query(
                Listing.city,
                func.count(Listing.id).label("count")
            ).filter(
                Listing.city.isnot(None),
                Listing.city != ""
            ).group_by(Listing.city).all()
            
            cities_data = [
                {"name": city, "count": count}
                for city, count in cities_query
            ]
            
            # Get neighborhoods for each city
            city_neighborhoods = {}
            for city_data in cities_data:
                city_name = city_data["name"]
                
                neighborhoods_query = db.query(
                    Listing.neighborhood,
                    func.count(Listing.id).label("count")
                ).filter(
                    func.lower(Listing.city) == func.lower(city_name),
                    Listing.neighborhood.isnot(None),
                    Listing.neighborhood != ""
                ).group_by(Listing.neighborhood).all()
                
                neighborhoods = [
                    {"name": neighborhood, "count": count}
                    for neighborhood, count in neighborhoods_query
                ]
                
                city_neighborhoods[city_name] = {
                    "neighborhoods": sorted(neighborhoods, key=lambda x: x["name"]),
                    "total_listings": city_data["count"]
                }
            
            # Create suggestion data for search autocomplete
            suggestions = []
            
            # Add cities as suggestions
            for city_data in cities_data:
                suggestions.append({
                    "type": "city",
                    "name": city_data["name"],
                    "display_name": city_data["name"],
                    "count": city_data["count"],
                    "city": city_data["name"],
                    "neighborhood": None
                })
            
            # Add city + neighborhood combinations
            for city_name, city_info in city_neighborhoods.items():
                for neighborhood in city_info["neighborhoods"]:
                    suggestions.append({
                        "type": "neighborhood",
                        "name": f"{neighborhood['name']}, {city_name}",
                        "display_name": neighborhood["name"],
                        "count": neighborhood["count"],
                        "city": city_name,
                        "neighborhood": neighborhood["name"]
                    })
            
            # Sort suggestions by count (descending)
            suggestions.sort(key=lambda x: x["count"], reverse=True)
            
            # Cache the data in Redis
            self.redis.setex(
                self.suggestion_cache_key,
                self.cache_ttl,
                json.dumps(suggestions)
            )
            
            self.redis.setex(
                self.city_neighborhoods_cache_key,
                self.cache_ttl,
                json.dumps(city_neighborhoods)
            )
            
            logger.info(f"Updated location cache with {len(suggestions)} suggestions and {len(city_neighborhoods)} cities")
            
        except Exception as e:
            logger.error(f"Error updating location mappings: {str(e)}")
            raise
    
    def get_location_suggestions(self, query: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get location suggestions from cache, optionally filtered by query using fuzzy search.
        
        Args:
            query: Optional search query to filter suggestions using fuzzy matching
            limit: Maximum number of suggestions to return
            
        Returns:
            List of location suggestions sorted by relevance
        """
        try:
            cached_data = self.redis.get(self.suggestion_cache_key)
            if not cached_data:
                logger.warning("No location suggestions found in cache")
                return []
            
            suggestions = json.loads(cached_data.decode('utf-8'))
            
            # If no query provided, return all suggestions limited by count
            if not query:
                return suggestions[:limit]
            
            # Use fuzzy search to find matching suggestions
            query_lower = query.lower().strip()
            
            # If query is too short, fall back to simple substring matching
            if len(query_lower) < 2:
                filtered_suggestions = []
                for suggestion in suggestions:
                    if (query_lower in suggestion["name"].lower() or 
                        query_lower in suggestion["display_name"].lower() or
                        query_lower in suggestion["city"].lower()):
                        filtered_suggestions.append(suggestion)
                return filtered_suggestions[:limit]
            
            # Create search strings for fuzzy matching
            suggestion_strings = []
            suggestion_map = {}
            
            for i, suggestion in enumerate(suggestions):
                # Create multiple search targets for each suggestion
                search_targets = [
                    suggestion["display_name"],  # Primary display name
                    suggestion["city"],          # City name
                    suggestion["name"]           # Full name (for neighborhoods: "Neighborhood, City")
                ]
                
                # Add neighborhood name separately if it exists
                if suggestion.get("neighborhood"):
                    search_targets.append(suggestion["neighborhood"])
                
                # Store all targets with reference to original suggestion
                for target in search_targets:
                    if target and target.strip():
                        key = f"{i}_{target}"
                        suggestion_strings.append(target)
                        suggestion_map[target] = suggestion
            
            # Use fuzzy matching to find best matches
            # We'll use different algorithms for different match quality
            matched_suggestions = []
            seen_suggestions = set()  # Avoid duplicates
            
            # 1. First pass: High-quality matches (ratio >= 70)
            high_quality_matches = process.extract(
                query, 
                suggestion_strings, 
                scorer=fuzz.ratio, 
                limit=limit * 2  # Get more to filter
            )
            
            for match, score in high_quality_matches:
                if score >= 70:  # High similarity threshold
                    suggestion = suggestion_map[match]
                    suggestion_key = f"{suggestion['type']}_{suggestion['name']}"
                    if suggestion_key not in seen_suggestions:
                        # Add the score for sorting
                        suggestion_with_score = suggestion.copy()
                        suggestion_with_score['_search_score'] = score
                        matched_suggestions.append(suggestion_with_score)
                        seen_suggestions.add(suggestion_key)
            
            # 2. Second pass: Medium-quality partial matches if we need more results
            if len(matched_suggestions) < limit:
                partial_matches = process.extract(
                    query, 
                    suggestion_strings, 
                    scorer=fuzz.partial_ratio, 
                    limit=limit * 2
                )
                
                for match, score in partial_matches:
                    if score >= 80:  # Higher threshold for partial matches
                        suggestion = suggestion_map[match]
                        suggestion_key = f"{suggestion['type']}_{suggestion['name']}"
                        if suggestion_key not in seen_suggestions:
                            suggestion_with_score = suggestion.copy()
                            suggestion_with_score['_search_score'] = score - 10  # Lower priority
                            matched_suggestions.append(suggestion_with_score)
                            seen_suggestions.add(suggestion_key)
            
            # 3. Third pass: Token-based matching for compound queries
            if len(matched_suggestions) < limit:
                token_matches = process.extract(
                    query, 
                    suggestion_strings, 
                    scorer=fuzz.token_set_ratio, 
                    limit=limit * 2
                )
                
                for match, score in token_matches:
                    if score >= 75:  # Medium threshold for token matching
                        suggestion = suggestion_map[match]
                        suggestion_key = f"{suggestion['type']}_{suggestion['name']}"
                        if suggestion_key not in seen_suggestions:
                            suggestion_with_score = suggestion.copy()
                            suggestion_with_score['_search_score'] = score - 15  # Lower priority
                            matched_suggestions.append(suggestion_with_score)
                            seen_suggestions.add(suggestion_key)
            
            # Sort by search score (descending) and listing count (descending)
            matched_suggestions.sort(
                key=lambda x: (x.get('_search_score', 0), x.get('count', 0)), 
                reverse=True
            )
            
            # Remove the search score before returning
            final_suggestions = []
            for suggestion in matched_suggestions:
                if '_search_score' in suggestion:
                    del suggestion['_search_score']
                final_suggestions.append(suggestion)
            
            logger.info(f"Fuzzy search for '{query}' returned {len(final_suggestions)} matches")
            return final_suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Error getting location suggestions with fuzzy search: {str(e)}")
            # Fallback to simple search if fuzzy search fails
            return self._simple_search_fallback(query, limit)
    
    def _simple_search_fallback(self, query: Optional[str], limit: int) -> List[Dict[str, Any]]:
        """
        Fallback simple search method when fuzzy search fails.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching suggestions using simple string matching
        """
        try:
            cached_data = self.redis.get(self.suggestion_cache_key)
            if not cached_data:
                return []
            
            suggestions = json.loads(cached_data.decode('utf-8'))
            
            if not query:
                return suggestions[:limit]
            
            query_lower = query.lower()
            filtered_suggestions = []
            
            for suggestion in suggestions:
                # Check if query matches city or neighborhood name
                if (query_lower in suggestion["name"].lower() or 
                    query_lower in suggestion["display_name"].lower() or
                    query_lower in suggestion["city"].lower()):
                    filtered_suggestions.append(suggestion)
            
            return filtered_suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Error in simple search fallback: {str(e)}")
            return []
    
    def get_neighborhoods_for_city(self, city_name: str) -> List[Dict[str, Any]]:
        """
        Get neighborhoods for a specific city from cache.
        
        Args:
            city_name: Name of the city
            
        Returns:
            List of neighborhoods with counts
        """
        try:
            cached_data = self.redis.get(self.city_neighborhoods_cache_key)
            if not cached_data:
                logger.warning("No city neighborhoods mapping found in cache")
                return []
            
            city_neighborhoods = json.loads(cached_data.decode('utf-8'))
            city_data = city_neighborhoods.get(city_name)
            
            if not city_data:
                return []
            
            return city_data.get("neighborhoods", [])
            
        except Exception as e:
            logger.error(f"Error getting neighborhoods for city {city_name}: {str(e)}")
            return []
    
    def invalidate_cache(self) -> None:
        """Invalidate all location cache data."""
        try:
            self.redis.delete(self.suggestion_cache_key, self.city_neighborhoods_cache_key)
            logger.info("Location cache invalidated")
        except Exception as e:
            logger.error(f"Error invalidating location cache: {str(e)}")


# Global instance
location_cache_service = LocationCacheService() 