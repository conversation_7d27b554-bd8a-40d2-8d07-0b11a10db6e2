from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_, desc

from app.crud.base import CRUDBase
from app.models.listing import Listing, ApprovalStatus
from app.schemas.listing import ListingCreate, ListingUpdate, ListingFilters
from app.crud.crud_listing_helpers import (
    create_rooms_filter,
    create_bathrooms_filter,
    create_elevator_filter,
    create_garden_filter,
    create_balcony_filter,
    create_terrace_filter,
    create_furnished_filter,
    create_parking_filter
)


class CRUDListing(CRUDBase[Listing, ListingCreate, ListingUpdate]):
    def get(self, db: Session, id: int) -> Optional[Listing]:
        """Override get to include owner relationship"""
        return db.query(Listing).options(joinedload(Listing.owner)).filter(Listing.id == id).first()
    
    def get_by_url(self, db: Session, *, url: str) -> Optional[Listing]:
        return db.query(Listing).filter(Listing.url == url).first()

    def get_multi_by_filters(
        self, db: Session, *, filters: ListingFilters
    ) -> Tuple[List[Listing], int]:
        """
        Get multiple listings by applying filters with pagination.
        
        Args:
            db: Database session
            filters: Filter parameters
            
        Returns:
            Tuple of (listings, total_count)
        """
        query = db.query(Listing)
        
        # Only show approved listings to non-admin users
        query = query.filter(Listing.approval_status == ApprovalStatus.approved)
        
        # Apply basic filters
        if filters.cities:
            query = query.filter(Listing.city.in_(filters.cities))
            
        if filters.neighborhoods:
            query = query.filter(Listing.neighborhood.in_(filters.neighborhoods))
            
        if filters.property_types:
            query = query.filter(Listing.property_type.in_(filters.property_types))
            
        if filters.property_category:
            query = query.filter(Listing.property_category == filters.property_category)
            
        if filters.transaction_type:
            query = query.filter(Listing.transaction_type == filters.transaction_type)
            
        # Price filters
        if filters.price_range:
            price_range = filters.price_range.split('-')
            if len(price_range) == 2:
                min_price, max_price = price_range
                if min_price.isdigit():
                    query = query.filter(Listing.price >= int(min_price))
                if max_price.isdigit():
                    query = query.filter(Listing.price <= int(max_price))
            elif filters.price_range.endswith('+') and filters.price_range[:-1].isdigit():
                min_price = int(filters.price_range[:-1])
                query = query.filter(Listing.price >= min_price)
                
        if filters.price_min is not None:
            query = query.filter(Listing.price >= filters.price_min)
            
        if filters.price_max is not None:
            query = query.filter(Listing.price <= filters.price_max)
            
        # Size filters (check both legacy size field and size_sqm field)
        if filters.size_min is not None:
            query = query.filter(
                or_(
                    Listing.size_sqm >= filters.size_min,
                    and_(Listing.size.isnot(None), Listing.size >= filters.size_min)
                )
            )
            
        if filters.size_max is not None:
            query = query.filter(
                or_(
                    Listing.size_sqm <= filters.size_max,
                    and_(Listing.size.isnot(None), Listing.size <= filters.size_max)
                )
            )
            
        # Room filters - check both legacy field and additional_info JSON
        if filters.rooms_min is not None or filters.rooms_max is not None:
            json_rooms_filter = create_rooms_filter(filters.rooms_min, filters.rooms_max)
            
            legacy_conditions = []
            if filters.rooms_min is not None:
                legacy_conditions.append(Listing.rooms >= filters.rooms_min)
            if filters.rooms_max is not None:
                legacy_conditions.append(Listing.rooms <= filters.rooms_max)
            
            if json_rooms_filter is not None:
                query = query.filter(
                    or_(
                        and_(Listing.rooms.isnot(None), *legacy_conditions),
                        json_rooms_filter
                    )
                )
            else:
                query = query.filter(and_(Listing.rooms.isnot(None), *legacy_conditions))
            
        # Bathroom filters - check both legacy field and additional_info JSON  
        if filters.bathrooms_min is not None or filters.bathrooms_max is not None:
            json_bathrooms_filter = create_bathrooms_filter(filters.bathrooms_min, filters.bathrooms_max)
            
            legacy_conditions = []
            if filters.bathrooms_min is not None:
                legacy_conditions.append(Listing.bathrooms >= filters.bathrooms_min)
            if filters.bathrooms_max is not None:
                legacy_conditions.append(Listing.bathrooms <= filters.bathrooms_max)
            
            if json_bathrooms_filter is not None:
                query = query.filter(
                    or_(
                        and_(Listing.bathrooms.isnot(None), *legacy_conditions),
                        json_bathrooms_filter
                    )
                )
            else:
                query = query.filter(and_(Listing.bathrooms.isnot(None), *legacy_conditions))
        
        # Boolean filters - check both legacy fields and additional_info JSON
        boolean_filters = [
            ('has_elevator', 'has_elevator', filters.has_elevator, create_elevator_filter),
            ('has_garden', 'has_garden', filters.has_garden, create_garden_filter),
            ('has_balcony', 'has_balcony', filters.has_balcony, create_balcony_filter),
            ('has_terrace', 'has_terrace', filters.has_terrace, create_terrace_filter),
            ('is_furnished', 'is_furnished', filters.is_furnished, create_furnished_filter),
            ('has_parking', 'parking', filters.has_parking, create_parking_filter),
        ]
        
        for filter_name, legacy_field_name, filter_value, json_filter_func in boolean_filters:
            if filter_value is not None:
                # Check if legacy field exists
                legacy_field = getattr(Listing, legacy_field_name, None)
                json_condition = json_filter_func(filter_value)
                
                if legacy_field is not None:
                    # Use both legacy field and JSON field
                    query = query.filter(
                        or_(
                            legacy_field == filter_value,
                            json_condition
                        )
                    )
                else:
                    # Only use JSON field
                    query = query.filter(json_condition)
        
        # Order by creation date (newest first)
        query = query.order_by(desc(Listing.created_at))
        
        # Get total count
        total = query.count()
        
        # Apply pagination and load owner relationship
        offset = (filters.page - 1) * filters.limit
        results = query.options(joinedload(Listing.owner)).offset(offset).limit(filters.limit).all()
        
        return results, total
    

    
    def get_best_deals(self, db: Session, *, limit: int = 4) -> List[Listing]:
        return db.query(Listing).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.price > 0
        ).options(joinedload(Listing.owner)).order_by(Listing.price).limit(limit).all()
    
    def get_cities(self, db: Session) -> List[Dict[str, Any]]:
        import random
        from urllib.parse import urlparse
        from pathlib import Path
        
        def get_thumbnail_url(image_url: str, size: str = 'small') -> str:
            """Convert an image URL to its thumbnail version"""
            if not image_url or size == 'original':
                return image_url
            
            try:
                # Parse the URL to extract the path and filename
                url = urlparse(image_url)
                path_parts = url.path.split('/')
                filename = path_parts[-1]
                
                # Extract the file extension
                file_path = Path(filename)
                stem = file_path.stem
                extension = file_path.suffix
                
                if not extension:
                    return image_url
                
                # Create the thumbnail filename
                thumbnail_filename = f"{stem}_{size}{extension}"
                
                # Replace the original filename with the thumbnail filename
                path_parts[-1] = thumbnail_filename
                new_path = '/'.join(path_parts)
                
                # Reconstruct the URL
                thumbnail_url = f"{url.scheme}://{url.netloc}{new_path}"
                return thumbnail_url
                
            except Exception:
                # If URL parsing fails, try simple string replacement
                last_dot_index = image_url.rfind('.')
                if last_dot_index == -1:
                    return image_url
                
                base_part = image_url[:last_dot_index]
                extension = image_url[last_dot_index:]
                
                return f"{base_part}_{size}{extension}"
        
        results = db.query(
            Listing.city, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.city.isnot(None)
        ).group_by(Listing.city).all()
        
        cities_with_images = []
        for city, count in results:
            # Get a random image from this city
            random_listing = db.query(Listing).filter(
                Listing.approval_status == ApprovalStatus.approved,
                Listing.city == city,
                Listing.images.isnot(None),
                func.json_array_length(Listing.images) > 0
            ).order_by(func.random()).first()
            
            city_image = None
            if random_listing and random_listing.images:
                # Pick a random image from the listing
                random_image = random.choice(random_listing.images)
                # Convert to small thumbnail for better performance
                city_image = get_thumbnail_url(random_image, 'small')
            
            cities_with_images.append({
                "city": city, 
                "count": count,
                "image": city_image
            })
        
        return cities_with_images
    
    def get_top_cities(self, db: Session, *, limit: int = 8) -> List[Dict[str, Any]]:
        """
        Retrieve top N cities by listing count with random images.
        Optimized version that only gets images for the top cities.
        """
        import random
        from urllib.parse import urlparse
        from pathlib import Path
        
        def get_thumbnail_url(image_url: str, size: str = 'small') -> str:
            """Convert an image URL to its thumbnail version"""
            if not image_url or size == 'original':
                return image_url
            
            try:
                # Parse the URL to extract the path and filename
                url = urlparse(image_url)
                path_parts = url.path.split('/')
                filename = path_parts[-1]
                
                # Extract the file extension
                file_path = Path(filename)
                stem = file_path.stem
                extension = file_path.suffix
                
                if not extension:
                    return image_url
                
                # Create the thumbnail filename
                thumbnail_filename = f"{stem}_{size}{extension}"
                
                # Replace the original filename with the thumbnail filename
                path_parts[-1] = thumbnail_filename
                new_path = '/'.join(path_parts)
                
                # Reconstruct the URL
                thumbnail_url = f"{url.scheme}://{url.netloc}{new_path}"
                return thumbnail_url
                
            except Exception:
                # If URL parsing fails, try simple string replacement
                last_dot_index = image_url.rfind('.')
                if last_dot_index == -1:
                    return image_url
                
                base_part = image_url[:last_dot_index]
                extension = image_url[last_dot_index:]
                
                return f"{base_part}_{size}{extension}"
        
        # First, get top cities by count (without images)
        top_cities_results = db.query(
            Listing.city, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.city.isnot(None)
        ).group_by(Listing.city).order_by(
            func.count(Listing.id).desc()
        ).limit(limit).all()
        
        # Now get images only for these top cities
        cities_with_images = []
        for city, count in top_cities_results:
            # Get a random image from this city
            random_listing = db.query(Listing).filter(
                Listing.approval_status == ApprovalStatus.approved,
                Listing.city == city,
                Listing.images.isnot(None),
                func.json_array_length(Listing.images) > 0
            ).order_by(func.random()).first()
            
            city_image = None
            if random_listing and random_listing.images:
                # Pick a random image from the listing
                random_image = random.choice(random_listing.images)
                # Convert to small thumbnail for better performance
                city_image = get_thumbnail_url(random_image, 'small')
            
            cities_with_images.append({
                "city": city, 
                "count": count,
                "image": city_image
            })
        
        return cities_with_images
    
    def get_all_cities_sorted(self, db: Session) -> List[Dict[str, Any]]:
        """
        Retrieve ALL cities sorted alphabetically for the cities page.
        This endpoint is specifically for the cities page, distinct from get_top_cities.
        """
        results = db.query(
            Listing.city, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.city.isnot(None)
        ).group_by(Listing.city).order_by(
            Listing.city.asc()  # Sort alphabetically
        ).all()
        
        return [{"city": city, "count": count} for city, count in results]

    def get_property_types(self, db: Session) -> List[Dict[str, Any]]:
        results = db.query(
            Listing.property_type, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.property_type.isnot(None)
        ).group_by(Listing.property_type).all()
        
        # Sort with "autre" at the end
        property_types = [{"property_type": property_type, "count": count} for property_type, count in results]
        property_types.sort(key=lambda x: (
            1 if x["property_type"].lower() == "autre" else 0,
            x["property_type"].lower()
        ))
        return property_types
    
    def get_property_categories(self, db: Session) -> List[Dict[str, Any]]:
        """
        Get all property categories with counts.
        
        Returns:
            List of dicts containing property_category and count
        """
        results = db.query(
            Listing.property_category, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.property_category.isnot(None)
        ).group_by(Listing.property_category).all()
        
        return [{"property_category": category, "count": count} for category, count in results]
    
    def get_neighborhoods(self, db: Session, *, city: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get neighborhoods with counts, optionally filtered by city.
        
        Args:
            db: Database session
            city: Optional city to filter neighborhoods
            
        Returns:
            List of dicts containing neighborhood and count
        """
        query = db.query(
            Listing.neighborhood, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.neighborhood.isnot(None),
            Listing.neighborhood != ""  # Filter out empty neighborhoods
        )
        
        if city:
            # Use case-insensitive search for city
            query = query.filter(func.lower(Listing.city) == func.lower(city))
            
        results = query.group_by(Listing.neighborhood).all()
        
        # Sort alphabetically for better UI presentation
        neighborhoods = [{"neighborhood": neighborhood, "count": count} for neighborhood, count in results]
        return sorted(neighborhoods, key=lambda x: x["neighborhood"])
    
    def get_neighborhoods_by_city(self, db: Session, *, city: str) -> List[Dict[str, Any]]:
        """Get all neighborhoods in a specific city with their listing counts.
        
        Args:
            db: Database session
            city: City name
            
        Returns:
            List of dictionaries with neighborhood and count keys
        """
        # Import the sqlalchemy func for case-insensitive comparison
        from sqlalchemy import func
        
        # Use case-insensitive comparison for the city
        results = db.query(
            Listing.neighborhood, 
            func.count(Listing.id).label('count')
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            func.lower(Listing.city) == func.lower(city),
            Listing.neighborhood.isnot(None),
            Listing.neighborhood != ""
        ).group_by(
            Listing.neighborhood
        ).order_by(
            func.count(Listing.id).desc()
        ).all()
        
        # Convert tuples to dictionaries and filter out None neighborhoods
        neighborhoods = [{"neighborhood": neighborhood, "count": count} for neighborhood, count in results if neighborhood]
        return sorted(neighborhoods, key=lambda x: x["neighborhood"])
    
    def get_by_city(self, db: Session, *, city: str, limit: int = 10, page: int = 1) -> Tuple[List[Listing], int, int]:
        """Get listings by city with pagination.
        
        Args:
            db: Database session
            city: City name
            limit: Number of listings per page
            page: Page number (1-indexed)
            
        Returns:
            Tuple of (listings, total_count, total_pages)
        """
        # Import the sqlalchemy func for case-insensitive comparison
        from sqlalchemy import func
        
        # Calculate offset
        skip = (page - 1) * limit
        
        # Get total count - case insensitive
        total = db.query(Listing).filter(
            Listing.approval_status == ApprovalStatus.approved,
            func.lower(Listing.city) == func.lower(city)
        ).count()
        
        # Get paginated listings - case insensitive, load owner relationship
        listings = db.query(Listing) \
            .filter(
                Listing.approval_status == ApprovalStatus.approved,
                func.lower(Listing.city) == func.lower(city)
            ) \
            .options(joinedload(Listing.owner)) \
            .order_by(desc(Listing.created_at)) \
            .offset(skip) \
            .limit(limit) \
            .all()
        
        # Calculate total pages (ceiling division)
        total_pages = (total + limit - 1) // limit
        
        return listings, total, total_pages
    
    def get_by_type(self, db: Session, *, property_type: str, limit: int = 10, page: int = 1) -> Tuple[List[Listing], int, int]:
        """Get listings by property type with pagination.
        
        Args:
            db: Database session
            property_type: Property type or category
            limit: Number of listings per page
            page: Page number (1-indexed)
            
        Returns:
            Tuple of (listings, total_count, total_pages)
        """
        # Import the sqlalchemy func for case-insensitive comparison
        from sqlalchemy import func
        
        # Calculate offset
        skip = (page - 1) * limit
        
        # Check if property_type is a category
        query = db.query(Listing).filter(Listing.approval_status == ApprovalStatus.approved)
        if property_type.lower() in ['residential', 'commercial', 'land']:
            # It's a category, use the property_category field
            query = query.filter(Listing.property_category == property_type.lower())
        else:
            # It's a specific property type
            query = query.filter(func.lower(Listing.property_type) == func.lower(property_type))
        
        # Get total count
        total = query.count()
        
        # Get paginated listings with owner relationship
        listings = query.options(joinedload(Listing.owner)).order_by(desc(Listing.created_at)).offset(skip).limit(limit).all()
        
        # Calculate total pages (ceiling division)
        total_pages = (total + limit - 1) // limit
        
        return listings, total, total_pages
    
    def clear_by_source(self, db: Session, *, source: str) -> int:
        """Clear all listings from a specific source.
        
        This method handles foreign key constraints by deleting dependent records first.
        
        Args:
            db: Database session
            source: Source name to filter by
            
        Returns:
            Number of listings deleted
        """
        # Import models here to avoid circular imports
        from app.models.view_history import ViewHistory
        from app.models.bookmark import Bookmark
        from app.models.listing_view import ListingView
        
        try:
            # Get listing IDs for the source
            listing_ids = db.query(Listing.id).filter(Listing.website_source == source).all()
            listing_ids = [lid[0] for lid in listing_ids]
            count = len(listing_ids)
            
            if count > 0:
                # Delete dependent records first to avoid foreign key constraint violations
                # Delete view history records for these listings
                db.query(ViewHistory).filter(ViewHistory.listing_id.in_(listing_ids)).delete(synchronize_session=False)
                
                # Delete bookmark records for these listings
                db.query(Bookmark).filter(Bookmark.listing_id.in_(listing_ids)).delete(synchronize_session=False)
                
                # Delete listing view records for these listings
                db.query(ListingView).filter(ListingView.listing_id.in_(listing_ids)).delete(synchronize_session=False)
                
                # Now delete the listings
                db.query(Listing).filter(Listing.website_source == source).delete(synchronize_session=False)
                
                # Commit all deletions
                db.commit()
            
            return count
            
        except Exception as e:
            db.rollback()
            raise e
    
    def remove_all(self, db: Session) -> int:
        """Remove all listings from the database.
        
        This method handles foreign key constraints by deleting dependent records first.
        
        Args:
            db: Database session
            
        Returns:
            Number of listings deleted
        """
        # Import models here to avoid circular imports
        from app.models.view_history import ViewHistory
        from app.models.bookmark import Bookmark
        from app.models.listing_view import ListingView
        
        try:
            # First, count the listings to be deleted
            count = db.query(Listing).count()
            
            # Delete dependent records first to avoid foreign key constraint violations
            # Delete all view history records
            view_history_count = db.query(ViewHistory).delete()
            
            # Delete all bookmark records
            bookmark_count = db.query(Bookmark).delete()
            
            # Delete all listing view records (public views)
            listing_view_count = db.query(ListingView).delete()
            
            # Now delete all listings
            listings_deleted = db.query(Listing).delete()
            
            # Commit all deletions
            db.commit()
            
            return count
            
        except Exception as e:
            db.rollback()
            raise e
    
    def get_property_types_by_city(
        self, db: Session, *, city: Optional[str] = None, neighborhoods: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Get property types with counts, filtered by city and neighborhoods.
        """
        query = db.query(Listing.property_type, func.count(Listing.id).label('count')).filter(
            Listing.approval_status == ApprovalStatus.approved
        )
        
        if city:
            # Use case-insensitive comparison for city
            query = query.filter(func.lower(Listing.city) == func.lower(city))
            
        if neighborhoods and len(neighborhoods) > 0:
            # Use case-insensitive comparison for neighborhoods
            query = query.filter(
                func.lower(Listing.neighborhood).in_([func.lower(n) for n in neighborhoods])
            )
            
        query = query.group_by(Listing.property_type)
        results = query.all()
        
        # Sort with "autre" at the end
        property_types = [{"property_type": pt, "count": count} for pt, count in results]
        property_types.sort(key=lambda x: (
            1 if x["property_type"].lower() == "autre" else 0,
            x["property_type"].lower()
        ))
        return property_types
    
    def get_property_types_by_category(self, db: Session) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get all property types grouped by their categories with counts.
        
        Returns:
            Dict where keys are category names and values are lists of property types with counts
        """
        # Get all categories and their property types with counts
        results = db.query(
            Listing.property_category,
            Listing.property_type, 
            func.count(Listing.id).label("count")
        ).filter(
            Listing.approval_status == ApprovalStatus.approved,
            Listing.property_category.isnot(None),
            Listing.property_type.isnot(None)
        ).group_by(
            Listing.property_category, 
            Listing.property_type
        ).all()
        
        # Organize results by category
        grouped_types = {}
        for category, property_type, count in results:
            if category not in grouped_types:
                grouped_types[category] = []
            grouped_types[category].append({
                "property_type": property_type,
                "count": count
            })
        
        # Sort property types within each category with "autre" at the end
        for category in grouped_types:
            grouped_types[category].sort(key=lambda x: (
                1 if x["property_type"].lower() == "autre" else 0,
                x["property_type"].lower()
            ))
        
        return grouped_types
    
    def get_pending_listings(self, db: Session, *, page: int = 1, limit: int = 10) -> Tuple[List[Listing], int]:
        """
        Get pending listings for admin approval.
        """
        query = db.query(Listing).filter(Listing.approval_status == 'pending')
        query = query.order_by(desc(Listing.created_at))
        
        total = query.count()
        offset = (page - 1) * limit
        results = query.offset(offset).limit(limit).all()
        
        return results, total
    
    def approve_listing(self, db: Session, *, listing_id: int, approved_by: int) -> Optional[Listing]:
        """
        Approve a pending listing.
        """
        from datetime import datetime
        from app.services.view_cache import view_cache
        
        listing = db.query(Listing).filter(Listing.id == listing_id).first()
        if not listing:
            return None
            
        listing.approval_status = 'approved'
        listing.approved_by = approved_by
        listing.approval_date = datetime.utcnow()
        listing.rejection_reason = None  # Clear any previous rejection reason
        
        db.commit()
        db.refresh(listing)
        
        # Invalidate popular listings cache since approval status changed
        view_cache.invalidate_popular_listings_cache()
        
        return listing
    
    def reject_listing(self, db: Session, *, listing_id: int, rejection_reason: str) -> Optional[Listing]:
        """
        Reject a pending listing.
        """
        from app.services.view_cache import view_cache
        
        listing = db.query(Listing).filter(Listing.id == listing_id).first()
        if not listing:
            return None
            
        listing.approval_status = 'rejected'
        listing.rejection_reason = rejection_reason
        listing.approved_by = None
        listing.approval_date = None
        
        db.commit()
        db.refresh(listing)
        
        # Invalidate popular listings cache since approval status changed
        view_cache.invalidate_popular_listings_cache()
        
        return listing


listing = CRUDListing(Listing)