"""Background tasks for location mapping and caching."""
import logging
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.services.location_cache import location_cache_service

logger = logging.getLogger(__name__)

def update_location_mappings_task() -> None:
    """
    Background task to update location mappings in Redis.
    This should be run periodically to keep the cache fresh.
    """
    try:
        logger.info("Starting location mappings background task...")
        
        # Create database session
        db: Session = SessionLocal()
        
        try:
            # Update the location mappings
            location_cache_service.update_location_mappings(db)
            logger.info("Location mappings background task completed successfully")
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Error in location mappings background task: {str(e)}")
        raise

def invalidate_location_cache_task() -> None:
    """
    Background task to invalidate location cache.
    This can be called when listings are added/updated/deleted.
    """
    try:
        logger.info("Invalidating location cache...")
        location_cache_service.invalidate_cache()
        logger.info("Location cache invalidated successfully")
        
    except Exception as e:
        logger.error(f"Error invalidating location cache: {str(e)}")
        raise 