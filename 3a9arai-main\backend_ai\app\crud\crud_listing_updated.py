"""
Updated CRUD operations for listings with additional_info JSON filtering support.
This version integrates the JSON filtering helpers while maintaining backward compatibility.
"""
from typing import List, Optional, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc
from app.crud.base import CRUDBase
from app.models.listing import Listing
from app.schemas.listing import ListingCreate, ListingUpdate, ListingFilters
from app.crud.crud_listing_helpers import (
    create_rooms_filter,
    create_bathrooms_filter,
    create_elevator_filter,
    create_garden_filter,
    create_balcony_filter,
    create_terrace_filter,
    create_furnished_filter,
    create_parking_filter
)


class CRUDListingUpdated(CRUDBase[Listing, ListingCreate, ListingUpdate]):
    def get_by_url(self, db: Session, *, url: str) -> Optional[Listing]:
        return db.query(Listing).filter(Listing.url == url).first()

    def get_filtered(
        self, db: Session, *, filters: ListingFilters
    ) -> Tuple[List[Listing], int]:
        query = db.query(Listing)
        
        # Apply basic filters (unchanged)
        if filters.cities:
            query = query.filter(Listing.city.in_(filters.cities))
            
        if filters.neighborhoods:
            query = query.filter(Listing.neighborhood.in_(filters.neighborhoods))
            
        if filters.property_types:
            query = query.filter(Listing.property_type.in_(filters.property_types))
            
        if filters.property_category:
            query = query.filter(Listing.property_category == filters.property_category)
            
        # Price filters (unchanged)
        if filters.price_range:
            price_range = filters.price_range.split('-')
            if len(price_range) == 2:
                min_price, max_price = price_range
                if min_price.isdigit():
                    query = query.filter(Listing.price >= int(min_price))
                if max_price.isdigit():
                    query = query.filter(Listing.price <= int(max_price))
            elif filters.price_range.endswith('+') and filters.price_range[:-1].isdigit():
                min_price = int(filters.price_range[:-1])
                query = query.filter(Listing.price >= min_price)
                
        if filters.price_min is not None:
            query = query.filter(Listing.price >= filters.price_min)
            
        if filters.price_max is not None:
            query = query.filter(Listing.price <= filters.price_max)
            
        # Size filters (check both legacy and new fields)
        if filters.size_min is not None:
            query = query.filter(
                or_(
                    Listing.size_sqm >= filters.size_min,
                    and_(Listing.size.isnot(None), Listing.size >= filters.size_min)
                )
            )
            
        if filters.size_max is not None:
            query = query.filter(
                or_(
                    Listing.size_sqm <= filters.size_max,
                    and_(Listing.size.isnot(None), Listing.size <= filters.size_max)
                )
            )
            
        # Room filters - check both legacy field and additional_info JSON
        if filters.rooms_min is not None or filters.rooms_max is not None:
            json_rooms_filter = create_rooms_filter(filters.rooms_min, filters.rooms_max)
            
            legacy_conditions = []
            if filters.rooms_min is not None:
                legacy_conditions.append(Listing.rooms >= filters.rooms_min)
            if filters.rooms_max is not None:
                legacy_conditions.append(Listing.rooms <= filters.rooms_max)
            
            if json_rooms_filter is not None:
                query = query.filter(
                    or_(
                        and_(Listing.rooms.isnot(None), *legacy_conditions),
                        json_rooms_filter
                    )
                )
            else:
                query = query.filter(and_(Listing.rooms.isnot(None), *legacy_conditions))
            
        # Bathroom filters - check both legacy field and additional_info JSON  
        if filters.bathrooms_min is not None or filters.bathrooms_max is not None:
            json_bathrooms_filter = create_bathrooms_filter(filters.bathrooms_min, filters.bathrooms_max)
            
            legacy_conditions = []
            if filters.bathrooms_min is not None:
                legacy_conditions.append(Listing.bathrooms >= filters.bathrooms_min)
            if filters.bathrooms_max is not None:
                legacy_conditions.append(Listing.bathrooms <= filters.bathrooms_max)
            
            if json_bathrooms_filter is not None:
                query = query.filter(
                    or_(
                        and_(Listing.bathrooms.isnot(None), *legacy_conditions),
                        json_bathrooms_filter
                    )
                )
            else:
                query = query.filter(and_(Listing.bathrooms.isnot(None), *legacy_conditions))
        
        # Boolean filters - check both legacy fields and additional_info JSON
        boolean_filters = [
            ('has_elevator', 'elevator', filters.has_elevator, create_elevator_filter),
            ('has_garden', 'has_garden', filters.has_garden, create_garden_filter),
            ('has_balcony', 'has_balcony', filters.has_balcony, create_balcony_filter),
            ('has_terrace', 'has_terrace', filters.has_terrace, create_terrace_filter),
            ('is_furnished', 'is_furnished', filters.is_furnished, create_furnished_filter),
            ('has_parking', 'parking', filters.has_parking, create_parking_filter),
        ]
        
        for filter_name, legacy_field_name, filter_value, json_filter_func in boolean_filters:
            if filter_value is not None:
                # Check if legacy field exists
                legacy_field = getattr(Listing, legacy_field_name, None)
                json_condition = json_filter_func(filter_value)
                
                if legacy_field is not None:
                    # Use both legacy field and JSON field
                    query = query.filter(
                        or_(
                            legacy_field == filter_value,
                            json_condition
                        )
                    )
                else:
                    # Only use JSON field
                    query = query.filter(json_condition)
        
        # Parking spaces filter (legacy field only for now, as JSON parsing is complex)
        if filters.parking_spaces_min is not None:
            query = query.filter(
                and_(
                    Listing.parking_spaces.isnot(None), 
                    Listing.parking_spaces >= filters.parking_spaces_min
                )
            )
            
        # Website source filter
        if filters.website_source:
            query = query.filter(
                or_(
                    Listing.website_source == filters.website_source,
                    Listing.scraper == filters.website_source
                )
            )
        
        # Other filters (unchanged)
        if filters.is_residential_project is not None:
            query = query.filter(Listing.is_residential_project == filters.is_residential_project)
        
        # Get total count before pagination
        total = query.count()
        
        # Apply sorting
        if filters.sort_by:
            if not hasattr(Listing, filters.sort_by):
                filters.sort_by = "created_at"
                
            sort_column = getattr(Listing, filters.sort_by)
            
            if filters.sort_order and filters.sort_order.lower() == "asc":
                query = query.order_by(sort_column)
            else:
                query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(desc(Listing.created_at))
        
        # Apply pagination
        skip = (filters.page - 1) * filters.limit
        query = query.offset(skip).limit(filters.limit)
        
        return query.all(), total

    def get_multi_by_filters(
        self, db: Session, *, filters: ListingFilters
    ) -> Tuple[List[Listing], int]:
        """
        Get multiple listings by applying filters with pagination.
        This is an alias for get_filtered method for API consistency.
        """
        return self.get_filtered(db, filters=filters)


# Create an instance for use
listing_updated = CRUDListingUpdated(Listing) 