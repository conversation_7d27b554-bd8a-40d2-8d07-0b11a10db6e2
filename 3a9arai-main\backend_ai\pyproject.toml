[project]
name = "backend"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi",
    "uvicorn",
    "sqlalchemy",
    "pydantic",
    "pydantic-settings",
    "psycopg2-binary",
    "alembic",
    "python-jose",
    "passlib",
    "python-multipart",
    "httpx",
    "beautifulsoup4",
    "requests",
    "python-dotenv",
    "selenium",
    "webdriver-manager",
    "email-validator",
    "psutil",
    "undetected-chromedriver",
    "celery",
    "redis",
    "selenium-wire",
    "openai",
    "mirascope",
    "lxml",
    "clerk-backend-api",
]
