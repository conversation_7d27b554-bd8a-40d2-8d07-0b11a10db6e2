from celery import Celery
import os
from celery.schedules import crontab

# Create Celery app
celery_app = Celery(
    'webscraper',
    broker=os.getenv('REDIS_URL', 'redis://redis:6379/0'),
    backend=os.getenv('REDIS_URL', 'redis://redis:6379/0'),
    include=['app.services.scrapers.tasks']
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    worker_prefetch_multiplier=1,  # Disable prefetching
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_create_missing_queues=True,
    task_routes={
        # Queue monitoring task - route to unified processing queue
        'monitor_queue_limits': {'queue': 'unified_processing'},
        'scrape_agenz_urls': {'queue': 'url_extraction'},
        'scrape_agenz_details': {'queue': 'detail_extraction'},
        # Sarouty tasks with dedicated queues
        'scrape_sarouty_urls': {'queue': 'sarouty_url_extraction'},
        'scrape_sarouty_details': {'queue': 'sarouty_detail_extraction'},
        # Avito Sale tasks with dedicated queues
        'scrape_avito_sale_urls': {'queue': 'avito_sale_url_extraction'},
        'scrape_avito_sale_details': {'queue': 'avito_sale_detail_extraction'},
        # Avito Rent tasks with dedicated queues
        'scrape_avito_rent_urls': {'queue': 'avito_rent_url_extraction'},
        'scrape_avito_rent_details': {'queue': 'avito_rent_detail_extraction'},
        # Legacy Avito tasks (for backward compatibility)
        'scrape_avito_urls': {'queue': 'avito_sale_url_extraction'},
        'scrape_avito_details': {'queue': 'avito_sale_detail_extraction'},
        # Unified processing task - route to unified processing queue
        'process_listings': {'queue': 'unified_processing'},
    },
    task_default_queue='default',
    task_default_exchange='default',
    task_default_routing_key='default',
    # Rate limits for LLM processing
    task_annotations={
        'process_listings': {
            'rate_limit': '1/s'  # One task per second
        }
    },
    # Worker-specific concurrency settings
    worker_concurrency={
        'celery-worker-url': 2,
        'celery-worker-details': 2,
        'celery-worker-llm': 1,  # Only one LLM worker
        'celery-worker-sarouty-url': 2,
        'celery-worker-sarouty-details': 2,
        'celery-worker-avito-sale-url': 2,
        'celery-worker-avito-sale-details': 2,
        'celery-worker-avito-rent-url': 2,
        'celery-worker-avito-rent-details': 2,
    },
    beat_schedule={
        # Queue monitoring task - runs every 15 seconds
        'monitor-queue-limits': {
            'task': 'monitor_queue_limits',
            'schedule': 15.0,  # Run every 15 seconds
        },
        'scrape-agenz-urls-daily': {
            'task': 'scrape_agenz_urls',
            'schedule': 10.0,  # Run every 10 seconds
        },
        'scrape-agenz-details': {
            'task': 'scrape_agenz_details',
            'schedule': 2.0,  # Run every 2 seconds
        },
        # Sarouty tasks
        'scrape-sarouty-urls-daily': {
            'task': 'scrape_sarouty_urls',
            'schedule': 10.0,  # Run every 10 seconds
        },
        'scrape-sarouty-details': {
            'task': 'scrape_sarouty_details',
            'schedule': 2.0,  # Run every 2 seconds
        },
        # Avito Sale tasks
        'scrape-avito-sale-urls-daily': {
            'task': 'scrape_avito_sale_urls',
            'schedule': 10.0,  # Run every 10 seconds
        },
        'scrape-avito-sale-details': {
            'task': 'scrape_avito_sale_details',
            'schedule': 2.0,  # Run every 2 seconds
        },
        # Avito Rent tasks
        'scrape-avito-rent-urls-daily': {
            'task': 'scrape_avito_rent_urls',
            'schedule': 10.0,  # Run every 10 seconds
        },
        'scrape-avito-rent-details': {
            'task': 'scrape_avito_rent_details',
            'schedule': 2.0,  # Run every 2 seconds
        },
        # Unified processing task for all scrapers
        'process-listings': {
            'task': 'process_listings',
            'schedule': 3.0,  # Run every 3 seconds
        },
    }
) 