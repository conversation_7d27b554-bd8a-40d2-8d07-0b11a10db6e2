from datetime import datetime, timedelta
from typing import List, Optional, Union, Dict, Any
from jose import jwt
from passlib.context import CryptContext
import secrets
import json
from fastapi import Depends, HTTPException, status, Security
from fastapi.security import OA<PERSON>2Pass<PERSON><PERSON>earer, SecurityScopes
from pydantic import ValidationError
from sqlalchemy.orm import Session, joinedload
import logging

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.user import User
from app.schemas.user import TokenData

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login",
    scopes={
        "user:read": "Read user information",
        "user:write": "Update user information",
        "admin": "Admin privileges",
        "listings:read": "Read property listings",
        "listings:write": "Manage property listings",
        "scrapers:read": "View scraper information",
        "scrapers:execute": "Execute scrapers",
    }
)

def create_access_token(
    subject: Union[str, Any], 
    scopes: List[str] = [], 
    expires_delta: Optional[timedelta] = None
) -> str:
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {
        "exp": expire.timestamp(),
        "sub": str(subject),
        "scopes": scopes,
        "jti": secrets.token_hex(8),  # JWT ID for token tracking
    }
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    
    return encoded_jwt

def create_refresh_token(
    subject: Union[str, Any],
) -> str:
    expire = datetime.utcnow() + timedelta(
        days=settings.REFRESH_TOKEN_EXPIRE_DAYS
    )
    
    to_encode = {
        "exp": expire.timestamp(),
        "sub": str(subject),
        "type": "refresh",
        "jti": secrets.token_hex(8),
    }
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    
    return encoded_jwt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

async def get_current_user(
    security_scopes: SecurityScopes,
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(lambda: SessionLocal())
) -> User:
    if security_scopes.scopes:
        authenticate_value = f'Bearer scope="{security_scopes.scope_str}"'
    else:
        authenticate_value = "Bearer"
        
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": authenticate_value},
    )
    
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_scopes = payload.get("scopes", [])
        token_data = TokenData(username=username, scopes=token_scopes)
    except (jwt.JWTError, ValidationError):
        raise credentials_exception
    
    try:    
        # Use joinedload to eagerly load the roles relationship
        user = db.query(User).options(joinedload(User.roles)).filter(User.username == token_data.username).first()
        if not user:
            raise credentials_exception
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Inactive user"
            )
                
        # Check if user has required scopes
        for scope in security_scopes.scopes:
            if scope not in token_data.scopes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Not enough permissions. Required: {scope}",
                    headers={"WWW-Authenticate": authenticate_value},
                )
                
        # Make sure roles are loaded while the session is still open
        user_roles = user.roles
    finally:
        db.close()
            
    return user

async def get_current_active_user(
    current_user: User = Security(get_current_user, scopes=["user:read"])
) -> User:
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )
    return current_user

async def get_current_active_superuser(
    current_user: User = Security(get_current_user, scopes=["admin"])
) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="Not enough privileges"
        )
    return current_user

def get_user_permissions(user: User) -> List[str]:
    """Extract all permissions from user roles"""
    permissions = []
    
    # Try to access roles, if already loaded
    try:
        for role in user.roles:
            try:
                role_permissions = json.loads(role.permissions)
                permissions.extend(role_permissions)
            except (json.JSONDecodeError, TypeError):
                continue
    except Exception as e:
        # If roles can't be accessed (detached instance), create a new session
        try:
            db = SessionLocal()
            db_user = db.query(User).options(joinedload(User.roles)).filter(User.id == user.id).first()
            if db_user:
                for role in db_user.roles:
                    try:
                        role_permissions = json.loads(role.permissions)
                        permissions.extend(role_permissions)
                    except (json.JSONDecodeError, TypeError):
                        continue
        except Exception as inner_e:
            # If all fails, log and continue with default permissions
            logging.error(f"Error accessing user roles: {str(inner_e)}")
        finally:
            if 'db' in locals():
                db.close()
    
    # Add default scopes
    permissions.append("user:read")
    
    # Superuser gets all permissions
    if user.is_superuser:
        permissions.append("admin")
    
    return list(set(permissions))  # Remove duplicates 