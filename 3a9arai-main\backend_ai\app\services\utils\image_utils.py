import os
import logging
import requests
import hashlib
from pathlib import Path
from typing import Optional
from urllib.parse import urlparse
import random
import time

logger = logging.getLogger(__name__)

# Create media directory if it doesn't exist
MEDIA_DIR = Path("./media/property_images")
MEDIA_DIR.mkdir(parents=True, exist_ok=True)

# Base URL for serving images directly from filesystem
BASE_URL = os.environ.get("BASE_URL", "http://localhost:8000")

# List of common user agents to rotate through
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]

# Session object to maintain cookies across requests
session = requests.Session()

def download_image(image_url: str, retry_count: int = 0) -> Optional[str]:
    """
    Download an image from a URL and save it locally.
    
    Args:
        image_url: URL of the image to download
        retry_count: Number of retry attempts made
        
    Returns:
        Direct filesystem URL to the downloaded image or None if download failed
    """
    try:
        # Generate a unique filename based on the URL
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        
        # Extract file extension from URL
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        
        # If no valid extension found, default to .jpg
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
            
        # Create filename with hash and extension
        filename = f"{url_hash}{extension}"
        filepath = MEDIA_DIR / filename
        
        # Check if file already exists
        if filepath.exists():
            return f"{BASE_URL}/media/property_images/{filename}"
        
        # Extract domain for referer header
        domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        
        # Set browser-like headers to avoid bot detection
        headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Referer": domain,
            "Origin": domain,
            "sec-ch-ua": '"Not.A/Brand";v="8", "Chromium";v="123", "Google Chrome";v="123"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "image",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-origin",
            "DNT": "1",
            "Connection": "keep-alive",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }
        
        # Download the image using the session with headers
        response = session.get(image_url, stream=True, timeout=15, headers=headers)
        response.raise_for_status()
        
        # Save the image
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                
        # Return direct filesystem URL
        return f"{BASE_URL}/media/property_images/{filename}"
        
    except requests.exceptions.HTTPError as http_err:
        status_code = http_err.response.status_code if hasattr(http_err, 'response') else "unknown"
        logger.error(f"HTTP error ({status_code}) downloading image {image_url}: {str(http_err)}")
        
        # If we encounter a 403 or 429 status code, try alternative approaches
        if (status_code == 403 or status_code == 429) and retry_count < 2:
            # Try alternative approach (with delay to avoid rate limiting)
            time.sleep(2 + random.random() * 3)  # Random delay between 2-5 seconds
            return try_alternative_download(image_url, retry_count + 1)
        
        return None
        
    except requests.exceptions.ConnectionError as conn_err:
        logger.error(f"Connection error downloading image {image_url}: {str(conn_err)}")
        
        # Try alternative approach if we haven't reached max retries
        if retry_count < 2:
            time.sleep(2)  # Wait before retry
            return try_alternative_download(image_url, retry_count + 1)
            
        return None
        
    except Exception as e:
        logger.error(f"Error downloading image {image_url}: {str(e)}")
        return None

def try_alternative_download(image_url: str, retry_count: int) -> Optional[str]:
    """
    Try alternative methods to download an image that failed with the primary method.
    
    Args:
        image_url: URL of the image to download
        retry_count: Current retry attempt number
        
    Returns:
        Direct filesystem URL to the downloaded image or None if download failed
    """
    
    try:
        # Create a new session with different headers
        alt_session = requests.Session()
        alt_headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "*/*",
            "Accept-Language": "en-US,en;q=0.9",
            # Remove most of the browser-specific headers that might trigger bot detection
        }
        
        # Use a different method depending on the retry count
        if retry_count == 1:
            # Try direct download with minimal headers
            return download_with_minimal_headers(image_url, alt_session, alt_headers)
        else:
            # Try using a different download approach on second retry
            # You could implement a proxy or alternative download method here
            return download_with_referer_chain(image_url)
            
    except Exception as e:
        logger.error(f"Alternative download method failed for {image_url}: {str(e)}")
        return None

def download_with_minimal_headers(image_url: str, session, headers) -> Optional[str]:
    """Download with minimal headers to avoid triggering anti-bot measures."""
    try:
        # Generate filename
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
        filename = f"{url_hash}{extension}"
        filepath = MEDIA_DIR / filename
        
        # Download with minimal headers
        response = session.get(image_url, stream=True, timeout=15, headers=headers)
        response.raise_for_status()
        
        # Save the image
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                
        return f"{BASE_URL}/media/property_images/{filename}"
        
    except Exception as e:
        logger.error(f"Minimal headers download failed: {str(e)}")
        return None

def download_with_referer_chain(image_url: str) -> Optional[str]:
    """
    Try to download by first visiting the referring page then the image.
    This can help bypass referer checks.
    """
    try:
        # Generate filename
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
        filename = f"{url_hash}{extension}"
        filepath = MEDIA_DIR / filename
        
        # Create a brand new session
        new_session = requests.Session()
        user_agent = random.choice(USER_AGENTS)
        
        # Extract domain and potential referer URL
        domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        base_path = "/".join(parsed_url.path.split("/")[:-1])
        potential_referer = f"{domain}{base_path}"
        
        # First, visit the potential referring page
        new_session.get(
            potential_referer,
            headers={"User-Agent": user_agent, "Accept": "text/html"}
        )
        
        # Then try to download the image
        response = new_session.get(
            image_url,
            stream=True,
            timeout=15,
            headers={
                "User-Agent": user_agent,
                "Referer": potential_referer,
                "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"
            }
        )
        response.raise_for_status()
        
        # Save the image
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
                
        return f"{BASE_URL}/media/property_images/{filename}"
        
    except Exception as e:
        logger.error(f"Referer chain download failed: {str(e)}")
        return None

def generate_filesystem_url(image_path: str) -> str:
    """
    Generate a direct filesystem URL for an image.
    
    Args:
        image_path: Path to the image (can be filename, relative path, or existing filesystem URL)
        
    Returns:
        Direct filesystem URL for the image
    """
    try:
        # If it's already a complete filesystem URL, return it
        if image_path.startswith("http") and "/media/property_images/" in image_path:
            return image_path
            
        # If it's already a complete ImgProxy URL, extract filename and convert
        if "imgproxy" in image_path.lower() or "/plain/local://images/property_images/" in image_path:
            # Extract filename from ImgProxy URL
            if "/plain/local://images/property_images/" in image_path:
                filename = image_path.split("/plain/local://images/property_images/")[-1]
                # Remove any additional parameters after the filename
                filename = filename.split("?")[0].split("#")[0]
            else:
                # Fallback: try to extract filename from end of URL
                filename = image_path.split("/")[-1].split("?")[0].split("#")[0]
            return f"{BASE_URL}/media/property_images/{filename}"
            
        # Handle relative paths like "property_images/filename" or "property_images/user_id/filename"
        if "/" in image_path:
            if image_path.startswith("property_images/"):
                # Remove the property_images prefix since it's already in our URL structure
                filename = image_path.replace("property_images/", "")
                return f"{BASE_URL}/media/property_images/{filename}"
            else:
                # Assume it's just a filename with subdirectory
                return f"{BASE_URL}/media/property_images/{image_path}"
        else:
            # Just a filename
            return f"{BASE_URL}/media/property_images/{image_path}"
            
    except Exception as e:
        logger.error(f"Error generating filesystem URL: {str(e)}")
        return f"{BASE_URL}/media/property_images/{image_path}"  # Return a best-guess URL

# Export BASE_URL for other modules that might need it
__all__ = ['download_image', 'generate_filesystem_url', 'generate_imgproxy_url', 'BASE_URL']

# Backward compatibility alias
generate_imgproxy_url = generate_filesystem_url