"""Update listing model with all PropertyListing schema fields

Revision ID: c230bb9e3ad6
Revises: 345147909c94
Create Date: 2025-06-02 17:31:49.347687

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c230bb9e3ad6'
down_revision: Union[str, None] = '345147909c94'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('size_sqm', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('note_about_size_sqm', sa.String(length=100), nullable=True))
    op.add_column('listings', sa.Column('additional_info', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('contact_name', sa.String(length=100), nullable=True))
    op.add_column('listings', sa.Column('contact_phone', sa.String(length=100), nullable=True))
    op.add_column('listings', sa.Column('contact_email', sa.String(length=100), nullable=True))
    op.add_column('listings', sa.Column('source_website', sa.String(length=50), nullable=True))
    op.drop_column('listings', 'parking_type')
    op.drop_column('listings', 'parking')
    op.drop_column('listings', 'land_type')
    op.drop_column('listings', 'indoor_features')
    op.drop_column('listings', 'has_balcony')
    op.drop_column('listings', 'total_floors')
    op.drop_column('listings', 'parking_spaces')
    op.drop_column('listings', 'is_furnished')
    op.drop_column('listings', 'land_zoning')
    op.drop_column('listings', 'security_features')
    op.drop_column('listings', 'has_reception')
    op.drop_column('listings', 'has_meeting_rooms')
    op.drop_column('listings', 'has_garden')
    op.drop_column('listings', 'orientation')
    op.drop_column('listings', 'image_url')
    op.drop_column('listings', 'is_serviced')
    op.drop_column('listings', 'is_part_of_project')
    op.drop_column('listings', 'has_terrace')
    op.drop_column('listings', 'commercial_type')
    op.drop_column('listings', 'monthly_fees')
    op.drop_column('listings', 'nearby_amenities')
    op.drop_column('listings', 'has_showroom')
    op.drop_column('listings', 'bathrooms')
    op.drop_column('listings', 'website_source')
    op.drop_column('listings', 'rooms')
    op.drop_column('listings', 'building_age')
    op.drop_column('listings', 'location')
    op.drop_column('listings', 'residential_project_name')
    op.drop_column('listings', 'size')
    op.drop_column('listings', 'terrace_size')
    op.drop_column('listings', 'features')
    op.drop_column('listings', 'floor')
    op.drop_column('listings', 'garden_size')
    op.drop_column('listings', 'is_residential_project')
    op.drop_column('listings', 'elevator')
    op.drop_column('listings', 'has_storage')
    op.drop_column('listings', 'balcony_size')
    op.drop_column('listings', 'outdoor_features')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('outdoor_features', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('balcony_size', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_storage', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('elevator', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('is_residential_project', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('garden_size', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('floor', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('features', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('terrace_size', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('size', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('residential_project_name', sa.VARCHAR(length=200), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('location', sa.VARCHAR(length=200), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('building_age', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('rooms', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('website_source', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('bathrooms', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_showroom', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('nearby_amenities', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('monthly_fees', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('commercial_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_terrace', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('is_part_of_project', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('is_serviced', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('image_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('orientation', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_garden', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_meeting_rooms', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_reception', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('security_features', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('land_zoning', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('is_furnished', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('parking_spaces', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('total_floors', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('has_balcony', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('indoor_features', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('land_type', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('parking', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.add_column('listings', sa.Column('parking_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.drop_column('listings', 'source_website')
    op.drop_column('listings', 'contact_email')
    op.drop_column('listings', 'contact_phone')
    op.drop_column('listings', 'contact_name')
    op.drop_column('listings', 'additional_info')
    op.drop_column('listings', 'note_about_size_sqm')
    op.drop_column('listings', 'size_sqm')
    # ### end Alembic commands ###
