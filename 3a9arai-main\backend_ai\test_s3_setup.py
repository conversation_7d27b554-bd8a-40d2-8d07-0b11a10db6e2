#!/usr/bin/env python3
"""
Test script for Digital Ocean Spaces (S3-compatible) integration.
This script validates the S3 setup and tests basic operations.
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_environment_variables():
    """Test that all required environment variables are set."""
    print("🔍 Testing environment variables...")
    
    required_vars = [
        'DO_SPACES_ACCESS_KEY',
        'DO_SPACES_SECRET_KEY', 
        'DO_SPACES_BUCKET_NAME',
        'DO_SPACES_REGION',
        'DO_SPACES_ENDPOINT'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("\nPlease set these environment variables in your .env file:")
        for var in missing_vars:
            if var == 'DO_SPACES_ACCESS_KEY':
                print(f"{var}=your_do_spaces_access_key")
            elif var == 'DO_SPACES_SECRET_KEY':
                print(f"{var}=your_do_spaces_secret_key")
            elif var == 'DO_SPACES_BUCKET_NAME':
                print(f"{var}=your_spaces_bucket_name")
            elif var == 'DO_SPACES_REGION':
                print(f"{var}=nyc3")
            elif var == 'DO_SPACES_ENDPOINT':
                print(f"{var}=https://nyc3.digitaloceanspaces.com")
        return False
    
    print("✅ All environment variables are set")
    return True

def test_s3_connection():
    """Test S3 connection and bucket access."""
    print("\n🔍 Testing S3 connection...")
    
    try:
        from app.services.utils.s3_image_utils import S3ImageUploader
        
        uploader = S3ImageUploader()
        
        # Test bucket access
        if uploader.check_bucket_exists():
            print("✅ Successfully connected to Digital Ocean Spaces bucket")
            return True
        else:
            print("❌ Could not access Digital Ocean Spaces bucket")
            print("   Please check your credentials and bucket name")
            return False
            
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_image_processing():
    """Test image processing functions."""
    print("\n🔍 Testing image processing...")
    
    try:
        from app.services.utils.s3_image_utils import S3ImageProcessor
        
        # Test with a sample image URL (this won't actually upload without valid creds)
        test_urls = ["https://example.com/test.jpg"]
        
        print("✅ S3 image processing functions loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error loading image processing functions: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Digital Ocean Spaces (S3) Integration\n")
    
    env_ok = test_environment_variables()
    
    if env_ok:
        connection_ok = test_s3_connection()
        processing_ok = test_image_processing()
        
        if connection_ok and processing_ok:
            print("\n🎉 All tests passed! S3 integration is ready to use.")
            print("\n📝 Next steps:")
            print("   1. Build and restart your containers: docker compose build && docker compose up -d")
            print("   2. Test image uploads through the API")
            print("   3. Check that images are uploaded to your Digital Ocean Spaces bucket")
        else:
            print("\n⚠️  Some tests failed. Please fix the issues above.")
    else:
        print("\n⚠️  Environment variables not configured. Please set them before testing.")

if __name__ == "__main__":
    main() 