from typing import List, Optional, Tuple, Any

from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from app.crud.base import CRUDBase
from app.models.bookmark import Bookmark
from app.models.listing import Listing
from app.models.user import User
from app.services.redis_cache import bookmark_cache


class CRUDBookmark(CRUDBase[Bookmark, None, None]):
    def create_bookmark(self, db: Session, *, user_id: int, listing_id: int) -> Bookmark:
        """Create a new bookmark for a user."""
        # Check if user exists
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
            
        # Check if listing exists
        listing = db.query(Listing).filter(Listing.id == listing_id).first()
        if not listing:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Listing not found"
            )
            
        # Check cache first for existing bookmark
        cached_status = bookmark_cache.is_bookmarked(user_id, listing_id)
        if cached_status is True:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Listing already bookmarked"
            )
        
        # Check database if not in cache
        if cached_status is None:
            existing_bookmark = db.query(Bookmark).filter(
                Bookmark.user_id == user_id,
                Bookmark.listing_id == listing_id
            ).first()
            
            if existing_bookmark:
                # Update cache with existing bookmark
                bookmark_cache.set_bookmark_status(user_id, listing_id, True)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Listing already bookmarked"
                )
        
        # Create new bookmark
        bookmark = Bookmark(user_id=user_id, listing_id=listing_id)
        db.add(bookmark)
        db.commit()
        db.refresh(bookmark)
        
        # Update cache
        bookmark_cache.add_bookmark_to_cache(user_id, listing_id)
        
        return bookmark
    
    def delete_bookmark(self, db: Session, *, user_id: int, listing_id: int) -> bool:
        """Delete a bookmark for a user."""
        # Check cache first
        cached_status = bookmark_cache.is_bookmarked(user_id, listing_id)
        if cached_status is False:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bookmark not found"
            )
        
        bookmark = db.query(Bookmark).filter(
            Bookmark.user_id == user_id,
            Bookmark.listing_id == listing_id
        ).first()
        
        if not bookmark:
            # Update cache to reflect database state
            bookmark_cache.set_bookmark_status(user_id, listing_id, False)
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bookmark not found"
            )
            
        db.delete(bookmark)
        db.commit()
        
        # Update cache
        bookmark_cache.remove_bookmark_from_cache(user_id, listing_id)
        
        return True
    
    def get_user_bookmarks(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> Tuple[List[Listing], int]:
        """Get all bookmarks for a user with pagination."""
        # Try to get from cache first
        cached_count = bookmark_cache.get_user_bookmarks_count(user_id)
        cached_listing_ids = bookmark_cache.get_user_bookmarked_listing_ids(user_id)
        
        if cached_count is not None and cached_listing_ids is not None:
            # Use cached data for pagination
            total = cached_count
            
            # Apply pagination to cached listing IDs
            sorted_ids = sorted(list(cached_listing_ids), reverse=True)  # Latest first
            paginated_ids = sorted_ids[skip:skip + limit]
            
            if paginated_ids:
                # Get the actual listings
                bookmarked_listings = db.query(Listing).filter(
                    Listing.id.in_(paginated_ids)
                ).all()
                
                # Sort listings to match the order of paginated_ids
                listings_dict = {listing.id: listing for listing in bookmarked_listings}
                ordered_listings = [listings_dict[listing_id] for listing_id in paginated_ids if listing_id in listings_dict]
            else:
                ordered_listings = []
            
            return ordered_listings, total
        
        # Cache miss - get from database and update cache
        bookmarks_query = db.query(Bookmark).filter(Bookmark.user_id == user_id)
        total = bookmarks_query.count()
        
        # Get actual listings
        bookmarked_listings = db.query(Listing).join(
            Bookmark, Bookmark.listing_id == Listing.id
        ).filter(
            Bookmark.user_id == user_id
        ).order_by(Bookmark.created_at.desc()).offset(skip).limit(limit).all()
        
        # Update cache with all bookmarked listing IDs
        all_bookmarked_ids = [bookmark.listing_id for bookmark in bookmarks_query.all()]
        bookmark_cache.set_user_bookmarked_listing_ids(user_id, all_bookmarked_ids)
        
        return bookmarked_listings, total
    
    def is_bookmarked(self, db: Session, *, user_id: int, listing_id: int) -> bool:
        """Check if a listing is bookmarked by a user."""
        # Check cache first
        cached_status = bookmark_cache.is_bookmarked(user_id, listing_id)
        if cached_status is not None:
            return cached_status
        
        # Cache miss - check database and update cache
        bookmark = db.query(Bookmark).filter(
            Bookmark.user_id == user_id,
            Bookmark.listing_id == listing_id
        ).first()
        
        is_bookmarked = bookmark is not None
        
        # Update cache with the result
        bookmark_cache.set_bookmark_status(user_id, listing_id, is_bookmarked)
        
        return is_bookmarked
    
    def get_bookmark_statuses(self, db: Session, *, user_id: int, listing_ids: List[int]) -> dict:
        """Get bookmark statuses for multiple listings efficiently.
        
        Args:
            db: Database session
            user_id: ID of the user
            listing_ids: List of listing IDs to check
            
        Returns:
            Dictionary mapping listing_id -> is_bookmarked
        """
        results = {}
        uncached_ids = []
        
        # Check cache for each listing
        for listing_id in listing_ids:
            cached_status = bookmark_cache.is_bookmarked(user_id, listing_id)
            if cached_status is not None:
                results[listing_id] = cached_status
            else:
                uncached_ids.append(listing_id)
        
        # Batch query for uncached listings
        if uncached_ids:
            bookmarks = db.query(Bookmark).filter(
                Bookmark.user_id == user_id,
                Bookmark.listing_id.in_(uncached_ids)
            ).all()
            
            bookmarked_ids = {bookmark.listing_id for bookmark in bookmarks}
            
            # Update results and cache
            for listing_id in uncached_ids:
                is_bookmarked = listing_id in bookmarked_ids
                results[listing_id] = is_bookmarked
                # Cache the result
                bookmark_cache.set_bookmark_status(user_id, listing_id, is_bookmarked)
        
        return results


bookmark = CRUDBookmark(Bookmark) 