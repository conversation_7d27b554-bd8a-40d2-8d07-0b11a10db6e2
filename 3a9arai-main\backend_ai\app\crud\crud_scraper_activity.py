from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, func

from app.crud.base import CRUDBase
from app.models.scraper_activity import ScraperActivity
from app.schemas.scraper_activity import ScraperActivityCreate, ScraperActivityUpdate


class CRUDScraperActivity(CRUDBase[ScraperActivity, ScraperActivityCreate, ScraperActivityUpdate]):
    def get_latest_activities(self, db: Session, *, limit: int = 20) -> List[ScraperActivity]:
        return db.query(ScraperActivity).order_by(desc(ScraperActivity.start_time)).limit(limit).all()
    
    def get_by_scraper(self, db: Session, *, scraper_name: str, limit: int = 10) -> List[ScraperActivity]:
        return db.query(ScraperActivity).filter(
            ScraperActivity.scraper_name == scraper_name
        ).order_by(desc(ScraperActivity.start_time)).limit(limit).all()
    
    def get_stats(self, db: Session) -> List[Dict[str, Any]]:
        # Get unique scraper names
        scraper_names = db.query(ScraperActivity.scraper_name).distinct().all()
        
        stats = []
        for (name,) in scraper_names:
            # Get activity count for this scraper
            activity_count = db.query(func.count(ScraperActivity.id)).filter(
                ScraperActivity.scraper_name == name
            ).scalar()
            
            # Get latest status for this scraper
            latest_activity = db.query(ScraperActivity).filter(
                ScraperActivity.scraper_name == name
            ).order_by(desc(ScraperActivity.start_time)).first()
            
            latest_status = latest_activity.status if latest_activity else "unknown"
            
            stats.append({
                "name": name,
                "activity_count": activity_count,
                "latest_status": latest_status
            })
        
        return stats


scraper_activity = CRUDScraperActivity(ScraperActivity) 