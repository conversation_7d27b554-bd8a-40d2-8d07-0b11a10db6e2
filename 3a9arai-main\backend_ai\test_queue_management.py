#!/usr/bin/env python3
"""Test script for queue management functionality."""

import sys
import os
import time
import redis
import json

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.services.scrapers.queue_manager import QueueLimitManager, get_queue_management_status

def setup_test_environment():
    """Set up test environment with Redis client."""
    redis_client = redis.Redis.from_url(
        os.environ.get('REDIS_URL', 'redis://redis:6379/0'),
        max_connections=20,
        socket_connect_timeout=5,
        socket_timeout=5,
        retry_on_timeout=True
    )
    return redis_client

def test_queue_manager():
    """Test the queue management functionality."""
    print("Testing Queue Management System")
    print("=" * 50)
    
    redis_client = setup_test_environment()
    manager = QueueLimitManager()
    
    # Test 1: Check initial state
    print("\n1. Testing initial state...")
    total_size = manager.get_total_llm_queue_size()
    print(f"   Initial total queue size: {total_size}")
    
    paused_scrapers = manager.get_paused_scrapers()
    print(f"   Initially paused scrapers: {paused_scrapers}")
    
    # Test 2: Check status for each scraper
    print("\n2. Testing scraper status check...")
    for scraper in manager.active_scrapers:
        is_running = manager.is_scraper_running(scraper)
        print(f"   {scraper}: {'running' if is_running else 'not running'}")
    
    # Test 3: Test queue limit checking
    print("\n3. Testing queue limit logic...")
    for scraper in manager.active_scrapers:
        should_allow = manager.should_allow_queue_addition(scraper)
        print(f"   {scraper} should allow queue addition: {should_allow}")
    
    # Test 4: Simulate high queue load
    print("\n4. Simulating high queue load...")
    test_queues = [
        "agenz_details_queue",
        "sarouty_processed_queue", 
        "avito_sale_details_queue",
        "avito_rent_details_queue"
    ]
    
    # Add test items to queues to simulate high load
    original_sizes = {}
    for queue_name in test_queues:
        original_size = redis_client.llen(queue_name)
        original_sizes[queue_name] = original_size
        print(f"   {queue_name}: {original_size} items")
        
        # Add test items to simulate reaching the limit
        test_items = 600  # Add enough to potentially trigger limits
        for i in range(test_items):
            test_data = {
                "url": f"https://test-{i}.com",
                "property_details": f"Test property {i}",
                "test": True
            }
            redis_client.rpush(queue_name, json.dumps(test_data))
    
    # Check new total
    new_total = manager.get_total_llm_queue_size()
    print(f"\n   New total queue size after adding test data: {new_total}")
    
    # Test 5: Run queue management check
    print("\n5. Running queue management check...")
    status = manager.check_and_manage_queue_limits()
    print(f"   Queue management result:")
    for key, value in status.items():
        print(f"     {key}: {value}")
    
    # Test 6: Check status API
    print("\n6. Testing status API...")
    api_status = get_queue_management_status()
    print(f"   API status result:")
    for key, value in api_status.items():
        print(f"     {key}: {value}")
    
    # Cleanup: Remove test data
    print("\n7. Cleaning up test data...")
    for queue_name in test_queues:
        current_size = redis_client.llen(queue_name)
        items_to_remove = current_size - original_sizes[queue_name]
        
        if items_to_remove > 0:
            print(f"   Removing {items_to_remove} test items from {queue_name}")
            # Remove items from the right side (newest items)
            for _ in range(items_to_remove):
                redis_client.rpop(queue_name)
    
    final_total = manager.get_total_llm_queue_size()
    print(f"\n   Final total queue size after cleanup: {final_total}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    try:
        test_queue_manager()
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 