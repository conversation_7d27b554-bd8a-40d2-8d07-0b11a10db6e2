from typing import Optional
from datetime import datetime
from pydantic import BaseModel

# Shared properties
class ScraperActivityBase(BaseModel):
    scraper_name: str
    status: str
    message: Optional[str] = None
    listings_added: int = 0
    start_time: datetime
    end_time: Optional[datetime] = None

# Properties to receive on scraper activity creation
class ScraperActivityCreate(ScraperActivityBase):
    pass

# Properties to receive on scraper activity update
class ScraperActivityUpdate(BaseModel):
    status: Optional[str] = None
    message: Optional[str] = None
    listings_added: Optional[int] = None
    end_time: Optional[datetime] = None

# Properties shared by models stored in DB
class ScraperActivityInDBBase(ScraperActivityBase):
    id: int

    class Config:
        from_attributes = True

# Properties to return to client
class ScraperActivity(ScraperActivityInDBBase):
    pass

# Properties stored in DB
class ScraperActivityInDB(ScraperActivityInDBBase):
    pass

# Scraper run request
class ScraperRunRequest(BaseModel):
    scraper_name: str

# Scraper run response
class ScraperRunResponse(BaseModel):
    success: bool
    message: str 