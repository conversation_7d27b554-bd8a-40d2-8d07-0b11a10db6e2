#!/usr/bin/env python3
"""Test script to verify transaction type fix for Avito rent scraper."""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.scrapers.llm_service import process_listing_text
from app.services.scrapers.models import PropertyListing

def test_transaction_type_logic():
    """Test that transaction type is set correctly based on scraper name."""
    
    # Mock data - minimal required to create a PropertyListing
    mock_text = """
    Sample property listing text
    """
    mock_images = ["https://example.com/image1.jpg"]
    mock_url = "https://example.com/listing/123"
    
    # Test cases
    test_cases = [
        ("avito_rent", "rent"),
        ("avito_sale", "sell"),
        ("AVITO_RENT", "rent"),  # Test case insensitive
        ("sarouty", "sell"),
        ("agenz", "sell"),
        ("test_rent_scraper", "rent"),
        ("rental_scraper", "rent"),
        ("sale_scraper", "sell"),
    ]
    
    print("Testing transaction type logic...")
    
    for scraper_name, expected_type in test_cases:
        print(f"\nTesting scraper: {scraper_name}")
        print(f"Expected transaction type: {expected_type}")
        
        # This would normally call the LLM, but we'll create a simple mock
        try:
            # Create a minimal PropertyListing with required fields
            listing_data = PropertyListing(
                url=mock_url,
                title="Test Property",
                price=100000,
                size_sqm=50,
                note_about_size_sqm="Test note",
                city="Casablanca",
                property_type="appartement",
                property_category="residential",
                description_html="<p>Test description</p>",
                contact_name="Test Contact",
                contact_phone="0123456789",
                images=mock_images,
                scraper=scraper_name,
                transaction_type="sell"  # Default value
            )
            
            # Apply the logic from our fix
            if 'rent' in scraper_name.lower():
                listing_data.transaction_type = 'rent'
            else:
                listing_data.transaction_type = 'sell'
                
            actual_type = listing_data.transaction_type
            
            print(f"Actual transaction type: {actual_type}")
            
            if actual_type == expected_type:
                print("✅ PASS")
            else:
                print("❌ FAIL")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print("\nTransaction type logic test completed!")

if __name__ == "__main__":
    test_transaction_type_logic() 