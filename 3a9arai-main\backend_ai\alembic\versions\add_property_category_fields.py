"""Add property category fields

Revision ID: 7a32bdf41e27
Revises: 5958c3fc0dee
Create Date: 2025-03-13 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7a32bdf41e27'
down_revision = '5958c3fc0dee'
branch_labels = None
depends_on = None


def upgrade():
    # Add property category fields
    op.add_column('listings', sa.Column('property_category', sa.String(50), nullable=True))
    
    # Add commercial specific fields
    op.add_column('listings', sa.Column('commercial_type', sa.String(50), nullable=True))
    op.add_column('listings', sa.Column('has_showroom', sa.Bo<PERSON>an(), nullable=True))
    op.add_column('listings', sa.Column('has_storage', sa.Boolean(), nullable=True))
    op.add_column('listings', sa.Column('has_reception', sa.<PERSON>(), nullable=True))
    op.add_column('listings', sa.Column('has_meeting_rooms', sa.<PERSON>(), nullable=True))
    op.add_column('listings', sa.Column('is_furnished', sa.Boolean(), nullable=True))
    
    # Add land specific fields
    op.add_column('listings', sa.Column('land_type', sa.String(50), nullable=True))
    op.add_column('listings', sa.Column('land_zoning', sa.String(100), nullable=True))
    op.add_column('listings', sa.Column('is_serviced', sa.Boolean(), nullable=True))
    
    # Add parking spaces field
    op.add_column('listings', sa.Column('parking_spaces', sa.Integer(), nullable=True))
    
    # Update existing listings based on property_type
    # Set residential property types
    op.execute("""
    UPDATE listings 
    SET property_category = 'residential' 
    WHERE property_type IN ('Appartement', 'Maison', 'Villa', 'Studio Apartment', 'house', 'apartment')
    """)
    
    # Set commercial property types
    op.execute("""
    UPDATE listings 
    SET property_category = 'commercial' 
    WHERE property_type IN ('Bureau', 'Local commercial', 'commercial property', 'Store', 'Office')
    """)
    
    # Set land property types
    op.execute("""
    UPDATE listings 
    SET property_category = 'land' 
    WHERE property_type IN ('Terrain', 'Plot')
    """)


def downgrade():
    # Remove added columns
    op.drop_column('listings', 'property_category')
    op.drop_column('listings', 'commercial_type')
    op.drop_column('listings', 'has_showroom')
    op.drop_column('listings', 'has_storage')
    op.drop_column('listings', 'has_reception')
    op.drop_column('listings', 'has_meeting_rooms')
    op.drop_column('listings', 'is_furnished')
    op.drop_column('listings', 'land_type')
    op.drop_column('listings', 'land_zoning')
    op.drop_column('listings', 'is_serviced')
    op.drop_column('listings', 'parking_spaces') 