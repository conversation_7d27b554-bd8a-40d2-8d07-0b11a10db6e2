"""test_is_residential_project_field

Revision ID: 6c25871c0233
Revises: 64731a069715
Create Date: 2025-04-23 11:11:33.679463

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision: str = '6c25871c0233'
down_revision: Union[str, None] = '64731a069715'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create an index on is_residential_project for faster queries
    op.create_index(
        'ix_listings_is_residential_project',
        'listings',
        ['is_residential_project'],
        unique=False
    )
    
    # Insert test data with residential projects
    connection = op.get_bind()
    
    # Check if we have existing listings
    result = connection.execute(text("SELECT COUNT(*) FROM listings"))
    count = result.scalar()
    
    # Only add test data if we have listings to update
    if count > 0:
        # Update 3 random listings to be residential projects
        connection.execute(text("""
            UPDATE listings 
            SET is_residential_project = TRUE,
                residential_project_name = 'Test Residential Project'
            WHERE id IN (
                SELECT id FROM listings 
                WHERE property_category = 'residential' 
                ORDER BY RANDOM() 
                LIMIT 3
            )
        """))
        
        print("Added residential project test data to 3 listings")


def downgrade() -> None:
    """Downgrade schema."""
    # Drop the index
    op.drop_index('ix_listings_is_residential_project', table_name='listings')
    
    # Remove test data
    connection = op.get_bind()
    connection.execute(text("""
        UPDATE listings 
        SET is_residential_project = NULL,
            residential_project_name = NULL
        WHERE is_residential_project = TRUE
    """))
