from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Request, Header
from sqlalchemy.orm import Session
import json
import hmac
import hashlib
from datetime import datetime
from jose import jwt

from app.api.deps import get_db, get_current_user_from_clerk, clerk_bearer
from app.models.user import User
from app.schemas.user import User as UserSchema

router = APIRouter()

@router.get("/test")
async def test_endpoint() -> Dict[str, Any]:
    """
    Simple test endpoint that doesn't require authentication.
    """
    return {
        "message": "Clerk auth endpoints are working",
        "timestamp": datetime.utcnow().isoformat(),
        "status": "ok"
    }

@router.get("/debug-auth")
async def debug_auth(
    authorization: str = Depends(clerk_bearer),
) -> Dict[str, Any]:
    """
    Debug endpoint to check what's in the JWT token.
    """
    try:
        token = authorization.credentials
        
        # Try to decode without verification to see the payload
        payload = jwt.get_unverified_claims(token)
        
        return {
            "token_received": True,
            "token_length": len(token),
            "payload": payload,
            "sub": payload.get("sub"),
            "email": payload.get("email"),
            "name": payload.get("name")
        }
    except Exception as e:
        return {
            "error": str(e),
            "token_received": bool(authorization.credentials if authorization else False),
            "token_length": len(authorization.credentials) if authorization and authorization.credentials else 0
        }

def verify_clerk_webhook(payload: bytes, signature: str, secret: str) -> bool:
    """Verify Clerk webhook signature"""
    if not signature:
        return False
    
    # Remove 'v1,' prefix if present
    if signature.startswith('v1,'):
        signature = signature[3:]
    
    # Create expected signature
    expected_signature = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(signature, expected_signature)

@router.post("/sync-admin-status")
async def sync_admin_status(
    current_user: User = Depends(get_current_user_from_clerk),
) -> Dict[str, Any]:
    """
    Sync admin status for the current user.
    This endpoint is called by the frontend after successful authentication
    to ensure admin status is synced from Clerk to database.
    """
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "clerk_user_id": current_user.clerk_user_id,
        "is_admin": current_user.is_superuser,
        "message": "Admin status synced successfully"
    }

@router.post("/webhook")
async def clerk_webhook(
    request: Request,
    db: Session = Depends(get_db),
    svix_signature: str = Header(None, alias="svix-signature"),
) -> Dict[str, Any]:
    """
    Handle Clerk webhooks for user events
    """
    # Get the raw payload
    payload = await request.body()
    
    # Verify webhook signature (you'll need to set CLERK_WEBHOOK_SECRET in your environment)
    # For now, we'll skip verification in development
    # webhook_secret = settings.CLERK_WEBHOOK_SECRET
    # if not verify_clerk_webhook(payload, svix_signature, webhook_secret):
    #     raise HTTPException(
    #         status_code=status.HTTP_401_UNAUTHORIZED,
    #         detail="Invalid webhook signature"
    #     )
    
    try:
        data = json.loads(payload)
        event_type = data.get("type")
        user_data = data.get("data", {})
        
        if event_type == "user.created":
            await handle_user_created(user_data, db)
        elif event_type == "user.updated":
            await handle_user_updated(user_data, db)
        elif event_type == "user.deleted":
            await handle_user_deleted(user_data, db)
        
        return {"status": "success"}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing webhook: {str(e)}"
        )

async def handle_user_created(user_data: Dict[str, Any], db: Session):
    """Handle user creation from Clerk"""
    clerk_user_id = user_data.get("id")
    email_addresses = user_data.get("email_addresses", [])
    
    # Get primary email
    primary_email = None
    for email in email_addresses:
        if email.get("id") == user_data.get("primary_email_address_id"):
            primary_email = email.get("email_address")
            break
    
    if not primary_email and email_addresses:
        primary_email = email_addresses[0].get("email_address")
    
    if not primary_email:
        raise ValueError("No email address found for user")
    
    # Check if user already exists
    existing_user = db.query(User).filter(User.clerk_user_id == clerk_user_id).first()
    if existing_user:
        return existing_user
    
    # Create new user
    user = User(
        clerk_user_id=clerk_user_id,
        email=primary_email,
        full_name=f"{user_data.get('first_name', '')} {user_data.get('last_name', '')}".strip(),
        is_active=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user

async def handle_user_updated(user_data: Dict[str, Any], db: Session):
    """Handle user updates from Clerk"""
    clerk_user_id = user_data.get("id")
    
    user = db.query(User).filter(User.clerk_user_id == clerk_user_id).first()
    if not user:
        # If user doesn't exist, create them
        return await handle_user_created(user_data, db)
    
    # Update user data
    email_addresses = user_data.get("email_addresses", [])
    primary_email = None
    for email in email_addresses:
        if email.get("id") == user_data.get("primary_email_address_id"):
            primary_email = email.get("email_address")
            break
    
    if primary_email:
        user.email = primary_email
    
    user.full_name = f"{user_data.get('first_name', '')} {user_data.get('last_name', '')}".strip()
    user.updated_at = datetime.utcnow()
    
    db.commit()
    db.refresh(user)
    
    return user

async def handle_user_deleted(user_data: Dict[str, Any], db: Session):
    """Handle user deletion from Clerk"""
    clerk_user_id = user_data.get("id")
    
    user = db.query(User).filter(User.clerk_user_id == clerk_user_id).first()
    if user:
        # Instead of deleting, we'll deactivate the user to preserve data integrity
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.commit()
    
    return {"status": "user deactivated"}

@router.get("/sync-user/{clerk_user_id}", response_model=UserSchema)
async def sync_user_from_clerk(
    clerk_user_id: str,
    db: Session = Depends(get_db),
) -> Any:
    """
    Sync a user from Clerk (for development/testing purposes)
    In production, this would typically be handled by webhooks
    """
    # Check if user already exists
    user = db.query(User).filter(User.clerk_user_id == clerk_user_id).first()
    if user:
        return user
    
    # For now, create a basic user record
    # In a real implementation, you'd fetch user data from Clerk's API
    user = User(
        clerk_user_id=clerk_user_id,
        email=f"user-{clerk_user_id}@example.com",  # Placeholder
        full_name="Clerk User",  # Placeholder
        is_active=True,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    
    return user 