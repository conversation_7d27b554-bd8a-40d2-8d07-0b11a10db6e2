"""Sarouty real estate scraper implementation using BeautifulSoup."""
import logging
import json
import time
import os
import re
import requests
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from app.services.scrapers.base_scraper import BaseScraper, redis_client
from app.services.scrapers.llm_service import process_listing_text
from app.models.listing import Listing
from app.db.session import SessionLocal
from app.services.scrapers.redis_utils import (
    get_next_page_url, get_last_page, set_last_page, add_next_page_url,
    acquire_url_collection_lock, release_url_collection_lock, set_url_collection_completed,
    is_url_collection_completed, add_to_url_queue, get_next_from_url_queue,
    add_to_details_queue, get_next_from_details_queue,
    add_to_processed_queue, get_next_from_processed_queue,
    check_control_queue
)

logger = logging.getLogger(__name__)

# Queue names for Sarouty scraper
URL_QUEUE = "sarouty_url_queue"
DETAILS_QUEUE = "sarouty_details_queue"
PROCESSED_QUEUE = "sarouty_processed_queue"
NEXT_PAGE_QUEUE = "sarouty_next_page_queue"

class SaroutyScraper(BaseScraper):
    """Scraper for Sarouty real estate website using BeautifulSoup."""
    
    def __init__(self, db):
        """Initialize the Sarouty scraper.
        
        Args:
            db: Database session
        """
        super().__init__(db, "sarouty")
        self.base_url = "https://www.sarouty.ma"
        self.search_url = f"{self.base_url}/acheter/proprietes-a-vendre.html"
        
        # Request headers to mimic a browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36'
        }
    
    def collect_listing_urls(self) -> List[str]:
        """Collect listing URLs from a single page of Sarouty website.
        
        Returns:
            List of listing URLs from the current page
        """
        # Check if we've marked URL collection as completed
        if is_url_collection_completed("sarouty"):
            logger.info("URL collection has been marked as completed. No more URLs to collect.")
            return []
            
        # First check if the scraper should continue running
        if not self.should_continue():
            logger.info("Scraper is set to stop, aborting URL collection.")
            return []
            
        # Try to acquire lock
        success, lock_value = acquire_url_collection_lock("sarouty")
        if not success:
            logger.info("Another worker is already collecting URLs")
            return []
            
        try:
            # Get the next page URL from the queue or use the default starting URL
            next_page_url = get_next_page_url("sarouty")
            
            # Get the last processed page from Redis
            current_page = get_last_page("sarouty")
            
            # If we have a next page URL from the queue, use it
            if next_page_url:
                page_url = next_page_url
                logger.info(f"Processing next page URL from queue: {page_url}")
            else:
                # Otherwise use the default URL with the current page number
                page_url = self.search_url if current_page == 1 else f"{self.search_url}?page={current_page}"
                logger.info(f"No next page URL in queue, using constructed URL: {page_url}")
            
            try:
                # Refresh lock to prevent expiration during long-running tasks
                success, _ = acquire_url_collection_lock("sarouty")
                
                logger.info(f"Processing page {current_page}: {page_url}")
                
                # Get URLs from the current page
                page_urls = self._get_listings_urls(page_url)
                
                if not page_urls:
                    logger.info(f"No URLs found on page {current_page}")
                    # Mark URL collection as completed since no URLs were found
                    set_url_collection_completed("sarouty")
                    # Update scraper status
                    from app.api.endpoints.scrapers import set_scraper_status
                    set_scraper_status("sarouty", "url_collection_completed", 
                                      message=f"URL collection completed at page {current_page}",
                                      last_page=current_page)
                    return []
                
                # Add new URLs to the queue
                new_urls_count = 0
                for url in page_urls:
                    if url not in self.processed_urls and url and len(url.strip()) > 0:
                        # Log the URL being added
                        logger.info(f"Adding URL to queue: {url}")
                        add_to_url_queue("sarouty", url)
                        self.processed_urls.add(url)
                        new_urls_count += 1
                
                logger.info(f"Collected {new_urls_count} new URLs from page {current_page}")
                
                # Store the current page in Redis
                set_last_page("sarouty", current_page)
                
                # Find and queue the next page URL
                self._queue_next_page_url(page_url, current_page)
                
                return page_urls
                
            except Exception as e:
                logger.error(f"Error collecting URLs from page {current_page}: {str(e)}")
                return []
                
        finally:
            # Release the lock we acquired
            release_url_collection_lock("sarouty", lock_value)
    
    def _queue_next_page_url(self, page_url: str, current_page: int) -> bool:
        """Find and queue the next page URL.
        
        Args:
            page_url: Current page URL
            current_page: Current page number
            
        Returns:
            True if next page URL was queued, False otherwise
        """
        try:
            # Check if the scraper should continue running
            if not self.should_continue():
                logger.info("Scraper is set to stop, not queueing next page URL")
                return False
            
            # Make the request to the page
            response = requests.get(page_url, headers=self.headers)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find the next page button
            next_button = soup.select_one('a.pagination__link.pagination__link--next')
            if not next_button:
                logger.info(f"No Next button found on page {current_page} - reached end of pagination")
                # Mark URL collection as completed
                set_url_collection_completed("sarouty")
                # Update scraper status
                from app.api.endpoints.scrapers import set_scraper_status
                set_scraper_status("sarouty", "url_collection_completed", 
                                  message=f"URL collection completed at page {current_page} (reached end of pagination)",
                                  last_page=current_page)
                return False
            
            # Get the next page URL
            next_href = next_button.get('href')
            if next_href:
                next_page_url = f"{self.base_url}{next_href}" if not next_href.startswith('http') else next_href
                logger.info(f"Found next page URL: {next_page_url}")
                # Add the next page URL to a dedicated queue
                add_next_page_url("sarouty", next_page_url)
                # Update the current page counter
                next_page = current_page + 1
                set_last_page("sarouty", next_page)
                logger.info(f"Queued next page URL {next_page_url} for page {next_page}")
                return True
            else:
                logger.warning("Next button found but no href attribute")
            
            return False
                
        except Exception as e:
            logger.error(f"Error queueing next page URL: {str(e)}")
            return False
    
    def extract_listing_html(self, url: str) -> str:
        """Extract HTML content from a listing URL.
        
        Args:
            url: URL of the listing
            
        Returns:
            HTML content of the listing page
        """
        try:
            logger.info(f"Getting HTML from {url}")
            response = requests.get(url, headers=self.headers)
            if response.status_code != 200:
                logger.error(f"Failed to get HTML from {url}: Status code {response.status_code}")
                return ""
            
            return response.text
        except Exception as e:
            logger.error(f"Error getting HTML from {url}: {str(e)}")
            return ""
    
    def extract_listing_details(self, url: str) -> Dict[str, Any]:
        """Extract details and images from a listing URL.
        
        Args:
            url: URL of the listing
            
        Returns:
            Dictionary containing HTML content and image URLs
        """
        try:
            logger.info(f"Getting details from {url}")
            
            # Get HTML content
            html = self.extract_listing_html(url)
            if not html:
                return {}
            
            # Get property details
            property_details = self._get_property_details(url, html)
            
            # Get image URLs
            image_urls = self._get_property_images(url, html)
            
            # Remove None values from images
            image_urls = [url for url in image_urls if url is not None]
            logger.info(f"Image URLs: {image_urls}")
            
            return {
                "url": url,
                "html": html,
                "images": image_urls,
                "property_details": property_details
            }
            
        except Exception as e:
            logger.error(f"Error extracting details from {url}: {str(e)}")
            return {}
    
    def _get_listings_urls(self, page_url: str) -> List[str]:
        """Get listing URLs from a search page.
        
        Args:
            page_url: URL of the search page
            
        Returns:
            List of listing URLs
        """
        try:
            # Make a request to the website
            logger.info(f"Getting listings from {page_url}")
            response = requests.get(page_url, headers=self.headers)
            if response.status_code != 200:
                logger.error(f"Failed to get listings from {page_url}: Status code {response.status_code}")
                return []
            
            # Parse the HTML content
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Find all property card links
            card_list_items = soup.select('a.card__link')
            logger.info(f"Found {len(card_list_items)} listing cards")
            
            if not card_list_items:
                logger.info("No listing cards found - likely reached end of listings")
                return []
            
            urls = []
            for item in card_list_items:
                href = item.get('href')
                if href:
                    # Ensure the URL is absolute
                    full_url = f"{self.base_url}{href}" if not href.startswith('http') else href
                    urls.append(full_url)
                    logger.debug(f"Found URL: {full_url}")
            
            if not urls:
                logger.info("No valid URLs found - likely reached end of listings")
                return []
            
            logger.info(f"Found {len(urls)} URLs on page {page_url}")
            return urls
            
        except Exception as e:
            logger.error(f"Error getting URLs from {page_url}: {str(e)}")
            return []
    
    def _get_property_images(self, url: str, html: str = None) -> List[str]:
        """Get image URLs from a listing page.
        
        Args:
            url: URL of the listing
            html: HTML content of the listing page (optional)
            
        Returns:
            List of image URLs
        """
        try:
            if not html:
                html = self.extract_listing_html(url)
                if not html:
                    return []
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # Find all script tags and get the longest one
            script_elements = soup.find_all('script')
            longest_script = max(script_elements, key=lambda x: len(x.string) if x.string else 0)
            
            if longest_script and longest_script.string:
                script_content = longest_script.string
                
                # Find all window.* variable assignments using regex
                variable_pattern = re.compile(r'window\.[a-zA-Z0-9_.]+\s*=')
                variable_matches = list(variable_pattern.finditer(script_content))
                
                variables_dict = {}
                
                # Process each variable match
                for i, match in enumerate(variable_matches):
                    var_name = match.group(0)[:-1].strip()  # Remove the '=' and any whitespace
                    start_pos = match.end()
                    
                    # Find the end of this variable's value (next variable or end of script)
                    if i < len(variable_matches) - 1:
                        # Find the last semicolon before the next variable
                        next_var_pos = variable_matches[i+1].start()
                        semicolon_pos = script_content.rfind(';', start_pos, next_var_pos)
                        if semicolon_pos != -1:
                            end_pos = semicolon_pos
                        else:
                            end_pos = next_var_pos
                    else:
                        # For the last variable, find the last semicolon in the script
                        semicolon_pos = script_content.rfind(';', start_pos)
                        if semicolon_pos != -1:
                            end_pos = semicolon_pos
                        else:
                            end_pos = len(script_content)
                    
                    # Extract the value
                    value = script_content[start_pos:end_pos].strip()
                    variables_dict[var_name] = value
                
                # Extract the property settings
                property_string = variables_dict.get("window.propertyfinder.settings.property")
                if property_string:
                    payload_start = property_string.find('payload:') + len('payload:')
                    tags_marker = property_string.find('tags')
                    if payload_start > 0 and tags_marker > payload_start:
                        # Remove the comma at the end of the payload
                        payload_content = property_string[payload_start:tags_marker].strip()
                        payload_content = payload_content.rstrip(',')
                        
                        try:
                            # Load the payload as JSON
                            payload_json = json.loads(payload_content)
                            
                            # Get all images from the payload
                            included_data = payload_json.get("included", [])
                            images = []
                            for item in included_data:
                                if item.get("type") == "property_image" and "links" in item:
                                    if "full_screen" in item["links"]:
                                        # Try to check if the full_screen image is accessible
                                        try:
                                            import requests
                                            full_screen_url = item["links"]["full_screen"]
                                            response = requests.head(full_screen_url, timeout=5)
                                            if response.status_code == 200:
                                                images.append(full_screen_url)
                                            elif "medium" in item["links"]:
                                                # Fallback to medium if full_screen is not available
                                                images.append(item["links"]["medium"])
                                        except Exception as e:
                                            logger.warning(f"Error checking image URL: {str(e)}")
                                            # Fallback to medium if there's an error checking full_screen
                                            if "medium" in item["links"]:
                                                images.append(item["links"]["medium"])
                                    elif "medium" in item["links"]:
                                        # If full_screen is not available, use medium
                                        images.append(item["links"]["medium"])
                            
                            logger.info(f"Found {len(images)} images for {url}")
                            logger.info(len(payload_json))
                            return images
                        except json.JSONDecodeError as e:
                            logger.error(f"Error parsing JSON payload: {str(e)}")
            
            # Fallback method if the above didn't work
            img_elements = soup.select('img.carousel__image')
            images = [img.get('src') for img in img_elements if img.get('src')]
            logger.info(f"Fallback method found {len(images)} images for {url}")
            return images
            
        except Exception as e:
            logger.error(f"Error getting images for {url}: {str(e)}")
            return []
    
    def _get_property_details(self, url: str, html: str = None) -> Dict[str, Any]:
        """Get property details from a listing page.
        
        Args:
            url: URL of the listing
            html: HTML content of the listing page (optional)
            
        Returns:
            Dictionary containing property details
        """
        try:
            if not html:
                html = self.extract_listing_html(url)
                if not html:
                    return {}
            
            soup = BeautifulSoup(html, 'html.parser')
            
            # Find first script with type application/ld+json
            script_elements = soup.find_all('script')
            for script in script_elements:
                if script.get('type') == 'application/ld+json' and script.string:
                    try:
                        logger.info(script.string)
                        return json.loads(script.string)
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON in script: {str(e)}")
                        continue
            
            return {}
            
        except Exception as e:
            logger.error(f"Error getting property details for {url}: {str(e)}")
            return {}
    
    def process_listing_with_llm(self, url: str, html: str, scraper_name: str, images: List[str]) -> Optional[Dict[str, Any]]:
        """Process listing HTML with LLM.
        
        Args:
            url: URL of the listing
            html: HTML content of the listing
            scraper_name: Name of the scraper
            images: List of image URLs
            
        Returns:
            Processed listing data or None if processing failed
        """
        listing_data = process_listing_text(html, scraper_name, url, images)                
        return listing_data
    
    def save_listing(self, listing_data) -> bool:
        """Save a listing to the database.
        
        Args:
            listing_data: Listing data to save
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            db = SessionLocal()
            
            try:
                logger.info(f"Saving listing: {listing_data}")
                url = listing_data.url
                if not url:
                    logger.error("Invalid listing data: missing URL")
                    return False
                
                # Check if listing already exists
                existing_listing = db.query(Listing).filter(Listing.url == url).first()
                
                # Clean the data
                clean_data = {}
                for key, value in listing_data.__dict__.items():
                    if key in ['id', 'scrape_date']:
                        continue
                    if key == 'source_website':
                        clean_data['website_source'] = value
                    else:
                        clean_data[key] = value
                
                # Scraper-generated listings are auto-approved
                clean_data['approval_status'] = 'approved'
                clean_data['is_user_generated'] = False
                
                if existing_listing:
                    logger.info(f"Updating existing listing with URL: {url}")
                    for key, value in clean_data.items():
                        if hasattr(existing_listing, key):
                            setattr(existing_listing, key, value)
                else:
                    logger.info(f"Creating new listing with URL: {url}")
                    db_listing = Listing(**clean_data)
                    logger.info(f"Adding listing to database: {db_listing}")
                    db.add(db_listing)
                
                db.commit()
                logger.info(f"Successfully saved listing for {url}")
                return True
                
            except Exception as e:
                logger.error(f"Error saving listing: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                db.rollback()
                return False
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error in save_listing: {str(e)}")
            return False
    
    def scrape(self) -> int:
        """Main scraping method for Sarouty.
        
        Returns:
            Number of listings processed
        """
        try:
            if not self.start():
                logger.error("Failed to start Sarouty scraper")
                return 0
            
            processed_count = 0
            
            # Collect initial URLs
            self.collect_listing_urls()
            
            while self.is_running and not self.should_stop:
                # Check for control commands
                command = check_control_queue("sarouty")
                if command:
                    if command.get("command") == "stop":
                        self.should_stop = True
                        self.is_running = False
                        break
                
                # Process URLs from the queue
                url = get_next_from_url_queue("sarouty")
                if url and url not in self.processed_urls:
                    try:
                        # Extract listing details including HTML and images
                        listing_details = self.extract_listing_details(url)
                        if listing_details and 'html' in listing_details:
                            html = listing_details['html']
                            images = listing_details.get('images', [])
                            
                            # Filter out None values from images
                            images = [img for img in images if img is not None]
                            
                            # Process with LLM
                            listing_data = self.process_listing_with_llm(url, html, "sarouty", images)
                            if listing_data:
                                # Add to processed queue
                                add_to_processed_queue("sarouty", listing_data)
                                
                                # Save to database
                                if self.save_listing(listing_data):
                                    processed_count += 1
                                    self.log_activity("running", f"Processed listing {url}", processed_count)
                            else:
                                # If LLM processing failed, requeue the detail data for later processing
                                logger.warning(f"LLM processing failed for {url}, requeueing for later processing")
                                # Create a structured object to requeue
                                add_to_details_queue("sarouty", url, html, images)
                        else:
                            # If extracting details failed, requeue the URL 
                            logger.warning(f"Failed to extract details for {url}, requeueing for later processing")
                            add_to_url_queue("sarouty", url)
                        
                    except Exception as e:
                        logger.error(f"Error processing URL {url}: {str(e)}")
                        # Requeue the URL
                        add_to_url_queue("sarouty", url)
                
                # Process details from the queue
                details_data = get_next_from_details_queue("sarouty")
                if details_data:
                    try:
                        url = details_data.get("url")
                        html = details_data.get("html")
                        images = details_data.get("images", [])
                        
                        # Filter out None values from images
                        images = [img for img in images if img is not None]
                        
                        if url and html:
                            # Process with LLM
                            listing_data = self.process_listing_with_llm(url, html, "sarouty", images)
                            if listing_data:
                                # Add to processed queue
                                add_to_processed_queue("sarouty", listing_data)
                                
                                # Save to database
                                if self.save_listing(listing_data):
                                    processed_count += 1
                                    self.log_activity("running", f"Processed listing {url}", processed_count)
                            else:
                                # If LLM processing failed, requeue it
                                logger.warning(f"LLM processing failed for {url}, requeueing for later processing")
                                add_to_details_queue("sarouty", url, html, images)
                        else:
                            # Missing required data, log and discard
                            logger.error("Details missing required data (url or html), discarding")
                    except Exception as e:
                        logger.error(f"Error processing details: {str(e)}")
                        # Requeue on any exception
                        if details_data and "url" in details_data and "html" in details_data:
                            add_to_details_queue(
                                "sarouty", 
                                details_data["url"], 
                                details_data["html"], 
                                details_data.get("images", []),
                                details_data["html"]
                            )
                
                # Process items from the processed queue
                processed_data = get_next_from_processed_queue("sarouty")
                if processed_data:
                    try:
                        # Convert dict to object with attributes for save_listing
                        class ListingObject:
                            def __init__(self, **kwargs):
                                for key, value in kwargs.items():
                                    setattr(self, key, value)
                        
                        listing_obj = ListingObject(**processed_data)
                        if self.save_listing(listing_obj):
                            processed_count += 1
                            self.log_activity("running", f"Saved listing {processed_data.get('url')}", processed_count)
                        else:
                            # If saving failed, add back to the processed queue
                            logger.warning("Failed to save processed listing, requeueing for later")
                            add_to_processed_queue("sarouty", processed_data)
                    except Exception as e:
                        logger.error(f"Error saving processed listing: {str(e)}")
                        # Requeue on any exception
                        if processed_data:
                            add_to_processed_queue("sarouty", processed_data)
                
                # Sleep briefly to prevent CPU overload
                time.sleep(1)
            
            # Log completion
            self.log_activity("success", f"Completed scraping {processed_count} listings", processed_count)
            return processed_count
            
        except Exception as e:
            logger.error(f"Error in Sarouty scraper: {str(e)}")
            self.log_activity("error", str(e))
            return processed_count
            
        finally:
            self.stop()