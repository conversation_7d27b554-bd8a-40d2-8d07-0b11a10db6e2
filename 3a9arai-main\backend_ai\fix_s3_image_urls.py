#!/usr/bin/env python3
"""
Fix script to update existing database records that have S3 image data as dictionaries
instead of simple URL strings.

This script will:
1. Find all listings with S3 image dictionaries in the images field
2. Extract the 'original' URLs from those dictionaries
3. Update the database records with simple URL strings
"""

import sys
import json
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
try:
    from app.db.session import SessionLocal
    from app.models.listing import Listing
except ImportError:
    # Try with absolute imports
    import sys, os
    sys.path.append('/app')
    from app.db.session import SessionLocal
    from app.models.listing import Listing

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_original_urls_from_db_field(images_field):
    """
    Extract original URLs from an images field that might contain dictionaries.
    
    Args:
        images_field: The images field from the database (could be list of strings or dicts)
        
    Returns:
        List of original URL strings
    """
    if not images_field:
        return []
    
    original_urls = []
    
    for img in images_field:
        if isinstance(img, dict) and 'original' in img:
            # S3 dictionary format: {'original': '...', 'small': '...'}
            original_urls.append(img['original'])
        elif isinstance(img, str):
            # Already a simple URL string
            original_urls.append(img)
        else:
            logger.warning(f"Unknown image format: {img}")
    
    return original_urls

def fix_s3_image_urls():
    """Fix all listings with S3 dictionary image data."""
    db = SessionLocal()
    try:
        # Get all listings
        all_listings = db.query(Listing).all()
        
        fixed_count = 0
        total_count = len(all_listings)
        
        logger.info(f"Processing {total_count} listings...")
        
        for listing in all_listings:
            if not listing.images:
                continue
                
            # Check if any images are dictionaries
            has_dict_format = False
            for img in listing.images:
                if isinstance(img, dict):
                    has_dict_format = True
                    break
            
            if has_dict_format:
                logger.info(f"Fixing listing ID {listing.id}: {listing.title[:50]}...")
                
                # Extract original URLs
                original_urls = extract_original_urls_from_db_field(listing.images)
                
                # Update the listing
                listing.images = original_urls
                
                logger.info(f"  Updated {len(original_urls)} image URLs")
                fixed_count += 1
        
        # Commit all changes
        db.commit()
        
        logger.info(f"✅ Fixed {fixed_count} out of {total_count} listings")
        
        if fixed_count > 0:
            logger.info("Database updated successfully!")
        else:
            logger.info("No listings needed fixing - all image URLs are already in correct format")
            
    except Exception as e:
        logger.error(f"Error fixing S3 image URLs: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def validate_fix():
    """Validate that the fix worked correctly."""
    logger.info("\n🔍 Validating the fix...")
    
    db = SessionLocal()
    try:
        # Check for any remaining dictionary formats
        all_listings = db.query(Listing).all()
        
        problematic_listings = []
        
        for listing in all_listings:
            if not listing.images:
                continue
                
            for img in listing.images:
                if isinstance(img, dict):
                    problematic_listings.append(listing.id)
                    break
        
        if problematic_listings:
            logger.warning(f"❌ Still found {len(problematic_listings)} listings with dictionary format:")
            for listing_id in problematic_listings[:5]:  # Show first 5
                logger.warning(f"  - Listing ID: {listing_id}")
        else:
            logger.info("✅ All image URLs are now in correct string format!")
            
    except Exception as e:
        logger.error(f"Error validating fix: {e}")
    finally:
        db.close()

def main():
    """Main function."""
    logger.info("🚀 Starting S3 Image URL Fix Script")
    
    try:
        # Apply the fix
        fix_s3_image_urls()
        
        # Validate the fix
        validate_fix()
        
        logger.info("\n🎉 S3 Image URL fix completed successfully!")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 