from sqlalchemy import <PERSON>umn, Integer, ForeignKey, DateTime, UniqueConstraint, Index
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.session import Base

class ViewHistory(Base):
    __tablename__ = "view_history"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    listing_id = Column(Integer, ForeignKey("listings.id"), nullable=False)
    viewed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="view_history")
    listing = relationship("Listing", back_populates="viewed_by")
    
    # Index for efficient querying by user and timestamp
    __table_args__ = (
        Index('ix_view_history_user_viewed_at', 'user_id', 'viewed_at'),
        Index('ix_view_history_user_listing', 'user_id', 'listing_id'),
    ) 