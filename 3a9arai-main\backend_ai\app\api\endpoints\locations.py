"""Location API endpoints."""
import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, BackgroundTasks, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.services.location_cache import location_cache_service
from app.worker.tasks.location_tasks import update_location_mappings_task

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/suggestions", response_model=List[Dict[str, Any]])
def get_location_suggestions(
    q: Optional[str] = Query(None, description="Search query to filter suggestions"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of suggestions to return"),
) -> Any:
    """
    Get location suggestions for search autocomplete.
    Returns cities and city+neighborhood combinations from Redis cache.
    """
    try:
        suggestions = location_cache_service.get_location_suggestions(query=q, limit=limit)
        return suggestions
    except Exception as e:
        logger.error(f"Error getting location suggestions: {str(e)}")
        return []

@router.get("/neighborhoods/{city}", response_model=List[Dict[str, Any]])
def get_neighborhoods_for_city(
    city: str,
) -> Any:
    """
    Get neighborhoods for a specific city from cache.
    """
    try:
        neighborhoods = location_cache_service.get_neighborhoods_for_city(city)
        return neighborhoods
    except Exception as e:
        logger.error(f"Error getting neighborhoods for city {city}: {str(e)}")
        return []

@router.post("/refresh-cache")
def refresh_location_cache(
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Manually trigger a refresh of the location cache.
    This runs as a background task.
    """
    try:
        background_tasks.add_task(update_location_mappings_task)
        return {"message": "Location cache refresh initiated"}
    except Exception as e:
        logger.error(f"Error initiating location cache refresh: {str(e)}")
        return {"error": "Failed to initiate cache refresh"}

@router.delete("/cache")
def invalidate_location_cache() -> Any:
    """
    Manually invalidate the location cache.
    """
    try:
        location_cache_service.invalidate_cache()
        return {"message": "Location cache invalidated"}
    except Exception as e:
        logger.error(f"Error invalidating location cache: {str(e)}")
        return {"error": "Failed to invalidate cache"} 