#!/usr/bin/env python3
"""
Migration script to convert ImgProxy URLs to direct filesystem URLs.

This script will:
1. Connect to the database
2. Find all listings with ImgProxy URLs in the images column
3. Convert ImgProxy URLs to direct filesystem URLs
4. Update the database records

Usage:
    python migrate_images_to_filesystem.py [--dry-run]
"""

import sys
import os
import re
import json
import argparse
from urllib.parse import urlparse, unquote
from typing import List, Optional

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.models.listing import Listing

# Configuration
BASE_URL = os.environ.get("BASE_URL", "http://localhost:8000")
IMGPROXY_DOMAINS = [
    "nomilliondollarhere.ddns.net:8080",
    "images.3a9arai.ma",
    "imgproxy:8080"
]

def extract_filename_from_imgproxy_url(imgproxy_url: str) -> Optional[str]:
    """
    Extract the original filename from an ImgProxy URL.
    
    ImgProxy URLs look like:
    http://domain:8080/[signature]/plain/local://images/property_images/filename.jpg
    
    Args:
        imgproxy_url: The ImgProxy URL to parse
        
    Returns:
        The filename or None if not parseable
    """
    try:
        parsed = urlparse(imgproxy_url)
        
        # Check if this is an ImgProxy URL
        if not any(domain in parsed.netloc for domain in IMGPROXY_DOMAINS):
            return None
            
        # Split the path and look for the local:// part
        path_parts = parsed.path.split('/')
        
        # Find the local:// part
        for i, part in enumerate(path_parts):
            if part.startswith('local:'):
                # The rest should be the local path
                local_path = '/'.join(path_parts[i:])
                # Remove 'local://' prefix
                local_path = local_path.replace('local://', '')
                # Decode URL encoding
                local_path = unquote(local_path)
                
                # Extract filename from path like 'images/property_images/filename.jpg'
                if 'property_images/' in local_path:
                    filename = local_path.split('property_images/')[-1]
                    return filename
                    
        return None
        
    except Exception as e:
        print(f"Error parsing ImgProxy URL {imgproxy_url}: {e}")
        return None

def convert_imgproxy_url_to_filesystem(imgproxy_url: str) -> str:
    """
    Convert an ImgProxy URL to a direct filesystem URL.
    
    Args:
        imgproxy_url: The ImgProxy URL to convert
        
    Returns:
        Direct filesystem URL or original URL if conversion fails
    """
    filename = extract_filename_from_imgproxy_url(imgproxy_url)
    
    if filename:
        return f"{BASE_URL}/media/property_images/{filename}"
    else:
        print(f"Warning: Could not convert URL: {imgproxy_url}")
        return imgproxy_url

def convert_images_list(images: List[str]) -> List[str]:
    """
    Convert a list of image URLs from ImgProxy to filesystem URLs.
    
    Args:
        images: List of image URLs
        
    Returns:
        List of converted URLs
    """
    converted = []
    
    for image_url in images:
        if not image_url:
            continue
            
        # Check if it's already a filesystem URL
        if "/media/property_images/" in image_url:
            converted.append(image_url)
            continue
            
        # Check if it's an ImgProxy URL
        if any(domain in image_url for domain in IMGPROXY_DOMAINS):
            converted_url = convert_imgproxy_url_to_filesystem(image_url)
            converted.append(converted_url)
        else:
            # Keep original if it's not an ImgProxy URL
            converted.append(image_url)
            
    return converted

def migrate_listing_images(db: Session, dry_run: bool = False) -> dict:
    """
    Migrate all listing images from ImgProxy URLs to filesystem URLs.
    
    Args:
        db: Database session
        dry_run: If True, don't actually update the database
        
    Returns:
        Dictionary with migration statistics
    """
    stats = {
        "total_listings": 0,
        "listings_with_images": 0,
        "listings_updated": 0,
        "images_converted": 0,
        "errors": []
    }
    
    try:
        # Get all listings
        listings = db.query(Listing).all()
        stats["total_listings"] = len(listings)
        
        print(f"Processing {len(listings)} listings...")
        
        for listing in listings:
            try:
                if not listing.images or len(listing.images) == 0:
                    continue
                    
                stats["listings_with_images"] += 1
                
                # Convert images
                original_images = listing.images
                converted_images = convert_images_list(original_images)
                
                # Check if any images were actually converted
                images_changed = original_images != converted_images
                
                if images_changed:
                    stats["listings_updated"] += 1
                    
                    # Count converted images
                    for orig, conv in zip(original_images, converted_images):
                        if orig != conv:
                            stats["images_converted"] += 1
                    
                    print(f"Listing {listing.id}: Converting {len(original_images)} images")
                    
                    if not dry_run:
                        listing.images = converted_images
                        db.add(listing)
                    else:
                        print(f"  DRY RUN: Would update images from:")
                        for orig, conv in zip(original_images, converted_images):
                            if orig != conv:
                                print(f"    {orig} -> {conv}")
                                
            except Exception as e:
                error_msg = f"Error processing listing {listing.id}: {e}"
                print(error_msg)
                stats["errors"].append(error_msg)
                
        if not dry_run:
            db.commit()
            print("Database changes committed.")
        else:
            print("DRY RUN: No changes made to database.")
            
    except Exception as e:
        error_msg = f"Database error: {e}"
        print(error_msg)
        stats["errors"].append(error_msg)
        if not dry_run:
            db.rollback()
            
    return stats

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Migrate ImgProxy URLs to filesystem URLs")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL for filesystem URLs")
    
    args = parser.parse_args()
    
    global BASE_URL
    BASE_URL = args.base_url
    
    print("=" * 60)
    print("ImgProxy to Filesystem URL Migration")
    print("=" * 60)
    print(f"Base URL: {BASE_URL}")
    print(f"Dry run: {args.dry_run}")
    print()
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Run migration
        stats = migrate_listing_images(db, dry_run=args.dry_run)
        
        # Print results
        print()
        print("=" * 60)
        print("Migration Results")
        print("=" * 60)
        print(f"Total listings: {stats['total_listings']}")
        print(f"Listings with images: {stats['listings_with_images']}")
        print(f"Listings updated: {stats['listings_updated']}")
        print(f"Images converted: {stats['images_converted']}")
        
        if stats["errors"]:
            print(f"Errors: {len(stats['errors'])}")
            for error in stats["errors"]:
                print(f"  - {error}")
        else:
            print("No errors occurred.")
            
        if args.dry_run:
            print("\nThis was a dry run. No changes were made to the database.")
            print("Run without --dry-run to apply changes.")
        else:
            print("\nMigration completed successfully!")
            
    finally:
        db.close()

if __name__ == "__main__":
    main() 