from pydantic import BaseModel
from datetime import datetime
from typing import Optional

from app.schemas.listing import Listing

class ViewHistoryBase(BaseModel):
    user_id: int
    listing_id: int

class ViewHistoryCreate(ViewHistoryBase):
    pass

class ViewHistoryUpdate(BaseModel):
    viewed_at: Optional[datetime] = None

class ViewHistoryInDBBase(ViewHistoryBase):
    id: int
    viewed_at: datetime

    class Config:
        from_attributes = True

class ViewHistory(ViewHistoryInDBBase):
    pass

class ViewHistoryInDB(ViewHistoryInDBBase):
    pass

class ViewHistoryResponse(ViewHistoryInDBBase):
    listing: Optional[Listing] = None 