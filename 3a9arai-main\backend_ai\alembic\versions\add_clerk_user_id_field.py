"""add_clerk_user_id_field

Revision ID: clerk_integration_001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'clerk_integration_001'
down_revision = None  # Update this with the latest revision ID
head = None

def upgrade():
    # Add clerk_user_id column
    op.add_column('users', sa.Column('clerk_user_id', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_users_clerk_user_id'), 'users', ['clerk_user_id'], unique=True)
    
    # Make username nullable
    op.alter_column('users', 'username',
                    existing_type=sa.String(length=50),
                    nullable=True)
    
    # Make hashed_password nullable
    op.alter_column('users', 'hashed_password',
                    existing_type=sa.String(length=255),
                    nullable=True)


def downgrade():
    # Remove clerk_user_id column
    op.drop_index(op.f('ix_users_clerk_user_id'), table_name='users')
    op.drop_column('users', 'clerk_user_id')
    
    # Make username not nullable again
    op.alter_column('users', 'username',
                    existing_type=sa.String(length=50),
                    nullable=False)
    
    # Make hashed_password not nullable again
    op.alter_column('users', 'hashed_password',
                    existing_type=sa.String(length=255),
                    nullable=False) 