#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to generate thumbnails for existing images in the media directory.

This script will:
1. Find all existing images in the media/property_images directory
2. Generate small, medium, and large thumbnails for each image
3. Skip images that already have thumbnails
4. Report progress and any errors

Usage:
    python scripts/generate_thumbnails.py [--force] [--dry-run]
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import List, Set
from PIL import Image

# Add the backend directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.utils.filesystem_image_utils import (
    MEDIA_DIR, 
    THUMBNAIL_SIZES, 
    create_thumbnail,
    generate_thumbnails_for_image
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_all_image_files(directory: Path) -> List[Path]:
    """
    Get all image files in the directory and subdirectories.
    
    Args:
        directory: Directory to search for images
        
    Returns:
        List of image file paths
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}
    image_files = []
    
    for file_path in directory.rglob('*'):
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            # Skip files that are already thumbnails
            if not any(suffix in file_path.stem for suffix in ['_small']):
                image_files.append(file_path)
    
    return image_files

def get_existing_thumbnails(image_path: Path) -> Set[str]:
    """
    Get the set of existing thumbnail sizes for an image.
    
    Args:
        image_path: Path to the original image
        
    Returns:
        Set of existing thumbnail sizes ('small')
    """
    existing = set()
    stem = image_path.stem
    extension = image_path.suffix
    parent = image_path.parent
    
    for size in THUMBNAIL_SIZES.keys():
        thumbnail_path = parent / f"{stem}_{size}{extension}"
        if thumbnail_path.exists():
            existing.add(size)
    
    return existing

def process_image(image_path: Path, force: bool = False, dry_run: bool = False) -> dict:
    """
    Process a single image to generate thumbnails.
    
    Args:
        image_path: Path to the image to process
        force: Whether to overwrite existing thumbnails
        dry_run: Whether to only simulate the operation
        
    Returns:
        Dictionary with processing results
    """
    result = {
        'file': str(image_path),
        'success': False,
        'created': [],
        'skipped': [],
        'errors': []
    }
    
    try:
        # Check existing thumbnails
        existing_thumbnails = get_existing_thumbnails(image_path)
        
        for size_name, max_dimensions in THUMBNAIL_SIZES.items():
            thumbnail_name = f"{image_path.stem}_{size_name}{image_path.suffix}"
            
            if size_name in existing_thumbnails and not force:
                result['skipped'].append(size_name)
                logger.debug(f"Skipping {thumbnail_name} - already exists")
                continue
            
            if dry_run:
                result['created'].append(f"{size_name} (dry-run)")
                logger.info(f"Would create {thumbnail_name}")
                continue
            
            # Create thumbnail
            thumbnail_url = create_thumbnail(image_path, max_dimensions, f"_{size_name}")
            
            if thumbnail_url:
                result['created'].append(size_name)
                logger.info(f"Created {thumbnail_name}")
            else:
                result['errors'].append(f"Failed to create {size_name} thumbnail")
                logger.error(f"Failed to create {thumbnail_name}")
        
        result['success'] = len(result['errors']) == 0
        
    except Exception as e:
        error_msg = f"Error processing {image_path}: {str(e)}"
        result['errors'].append(error_msg)
        logger.error(error_msg)
    
    return result

def main():
    """Main function to process all images."""
    parser = argparse.ArgumentParser(description='Generate thumbnails for existing images')
    parser.add_argument('--force', action='store_true', 
                       help='Overwrite existing thumbnails')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be done without actually doing it')
    parser.add_argument('--directory', type=str, default=None,
                       help='Specific directory to process (default: all media)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine the directory to process
    if args.directory:
        process_dir = Path(args.directory)
        if not process_dir.exists():
            logger.error(f"Directory {process_dir} does not exist")
            return 1
    else:
        process_dir = MEDIA_DIR
    
    logger.info(f"Processing images in: {process_dir}")
    logger.info(f"Force overwrite: {args.force}")
    logger.info(f"Dry run: {args.dry_run}")
    
    # Find all image files
    image_files = get_all_image_files(process_dir)
    logger.info(f"Found {len(image_files)} image files to process")
    
    if not image_files:
        logger.info("No images found to process")
        return 0
    
    # Process each image
    total_created = 0
    total_skipped = 0
    total_errors = 0
    successful_files = 0
    
    for i, image_path in enumerate(image_files, 1):
        logger.info(f"Processing {i}/{len(image_files)}: {image_path.name}")
        
        result = process_image(image_path, args.force, args.dry_run)
        
        if result['success']:
            successful_files += 1
        
        total_created += len(result['created'])
        total_skipped += len(result['skipped'])
        total_errors += len(result['errors'])
        
        # Print summary for this file if there were issues or verbose mode
        if result['errors'] or args.verbose:
            if result['created']:
                logger.info(f"  Created: {', '.join(result['created'])}")
            if result['skipped']:
                logger.info(f"  Skipped: {', '.join(result['skipped'])}")
            if result['errors']:
                logger.error(f"  Errors: {'; '.join(result['errors'])}")
    
    # Final summary
    logger.info("=" * 60)
    logger.info("THUMBNAIL GENERATION SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total files processed: {len(image_files)}")
    logger.info(f"Successful files: {successful_files}")
    logger.info(f"Failed files: {len(image_files) - successful_files}")
    logger.info(f"Thumbnails created: {total_created}")
    logger.info(f"Thumbnails skipped: {total_skipped}")
    logger.info(f"Errors encountered: {total_errors}")
    
    if args.dry_run:
        logger.info("This was a dry run - no files were actually created")
    
    # Check disk space usage
    try:
        if not args.dry_run:
            total_size = sum(f.stat().st_size for f in process_dir.rglob('*') if f.is_file())
            logger.info(f"Total media directory size: {total_size / (1024*1024):.1f} MB")
    except Exception as e:
        logger.warning(f"Could not calculate directory size: {e}")
    
    return 0 if total_errors == 0 else 1

if __name__ == "__main__":
    sys.exit(main()) 