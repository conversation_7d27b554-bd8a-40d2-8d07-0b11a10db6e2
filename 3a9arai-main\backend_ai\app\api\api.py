from fastapi import APIRouter

from app.api.endpoints import auth, users, scrapers, listings, monitor, bookmarks, clerk_auth, view_history, admin, listing_views, images, locations

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(clerk_auth.router, prefix="/clerk", tags=["Clerk Authentication"])
api_router.include_router(admin.router, prefix="/admin", tags=["Admin"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(scrapers.router, prefix="/scrapers", tags=["Scrapers"])
api_router.include_router(listings.router, prefix="/listings", tags=["Listings"])
api_router.include_router(listing_views.router, prefix="/listing-views", tags=["Listing Views"])
api_router.include_router(bookmarks.router, prefix="/bookmarks", tags=["Bookmarks"])
api_router.include_router(view_history.router, prefix="/view-history", tags=["View History"])
api_router.include_router(images.router, prefix="/images", tags=["Images"])
api_router.include_router(locations.router, prefix="/locations", tags=["Locations"])
api_router.include_router(monitor.router, prefix="/monitor", tags=["Monitoring"]) 