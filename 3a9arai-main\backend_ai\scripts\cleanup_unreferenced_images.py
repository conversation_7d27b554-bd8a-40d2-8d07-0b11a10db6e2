#!/usr/bin/env python3
"""
Scrip<PERSON> to clean up unreferenced images from the media directory.

This script will:
1. Find all images referenced in the database
2. Find all image files in the media directory
3. Delete files that exist but aren't referenced in the database
4. Delete orphaned thumbnails
5. Report space savings

Usage:
    python scripts/cleanup_unreferenced_images.py [--dry-run] [--verbose]
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Set, List, Tuple
from sqlalchemy.orm import Session

# Add the backend directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.listing import Listing
from app.services.utils.filesystem_image_utils import MEDIA_DIR

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_referenced_images(db: Session) -> Set[str]:
    """
    Get all image filenames that are referenced in the database.
    
    Args:
        db: Database session
        
    Returns:
        Set of image filenames (without path) that are referenced
    """
    referenced_files = set()
    
    # Get all listings
    listings = db.query(Listing).all()
    
    for listing in listings:
        # Handle images field (JSON array)
        if listing.images:
            for image_url in listing.images:
                if image_url and "/media/property_images/" in image_url:
                    # Extract filename from URL
                    filename = image_url.split("/")[-1]
                    referenced_files.add(filename)
                    
                    # Also add thumbnail versions
                    stem = Path(filename).stem
                    extension = Path(filename).suffix
                    referenced_files.add(f"{stem}_small{extension}")
        
        # Handle legacy image_url field
        if listing.image_url and "/media/property_images/" in listing.image_url:
            filename = listing.image_url.split("/")[-1]
            referenced_files.add(filename)
            
            # Also add thumbnail version
            stem = Path(filename).stem
            extension = Path(filename).suffix
            referenced_files.add(f"{stem}_small{extension}")
    
    logger.info(f"Found {len(referenced_files)} referenced image files (including thumbnails)")
    return referenced_files

def get_all_image_files(directory: Path) -> List[Path]:
    """
    Get all image files in the directory and subdirectories.
    
    Args:
        directory: Directory to search for images
        
    Returns:
        List of image file paths
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.gif'}
    image_files = []
    
    for file_path in directory.rglob('*'):
        if file_path.is_file() and file_path.suffix.lower() in image_extensions:
            image_files.append(file_path)
    
    return image_files

def calculate_file_size(file_path: Path) -> int:
    """Get file size in bytes."""
    try:
        return file_path.stat().st_size
    except (OSError, FileNotFoundError):
        return 0

def delete_unreferenced_images(referenced_files: Set[str], media_dir: Path, dry_run: bool = True) -> Tuple[int, int]:
    """
    Delete image files that aren't referenced in the database.
    
    Args:
        referenced_files: Set of filenames that are referenced
        media_dir: Media directory path
        dry_run: If True, only simulate deletion
        
    Returns:
        Tuple of (files_deleted, bytes_freed)
    """
    all_files = get_all_image_files(media_dir)
    files_to_delete = []
    total_size = 0
    
    for file_path in all_files:
        # Get relative filename (handle subdirectories)
        try:
            relative_path = file_path.relative_to(media_dir)
            filename = str(relative_path).replace(os.sep, '/')  # Use forward slashes for consistency
            
            # Check if this file is referenced
            if filename not in referenced_files and file_path.name not in referenced_files:
                files_to_delete.append(file_path)
                file_size = calculate_file_size(file_path)
                total_size += file_size
                
                if dry_run:
                    logger.info(f"Would delete: {file_path.name} ({file_size / 1024:.1f} KB)")
                else:
                    logger.info(f"Deleting: {file_path.name} ({file_size / 1024:.1f} KB)")
                    
        except ValueError:
            # File is not within media_dir, skip it
            continue
    
    if not dry_run:
        deleted_count = 0
        for file_path in files_to_delete:
            try:
                file_path.unlink()
                deleted_count += 1
            except (OSError, FileNotFoundError) as e:
                logger.error(f"Failed to delete {file_path}: {e}")
        
        return deleted_count, total_size
    
    return len(files_to_delete), total_size

def find_orphaned_thumbnails(media_dir: Path) -> List[Path]:
    """
    Find thumbnail files that don't have corresponding original images.
    
    Args:
        media_dir: Media directory path
        
    Returns:
        List of orphaned thumbnail file paths
    """
    all_files = get_all_image_files(media_dir)
    thumbnails = []
    originals = set()
    orphaned = []
    
    # Separate thumbnails and originals
    for file_path in all_files:
        if '_small' in file_path.stem:
            thumbnails.append(file_path)
        else:
            originals.add(file_path.name)
    
    # Find orphaned thumbnails
    for thumbnail_path in thumbnails:
        # Get the original filename
        original_name = thumbnail_path.name.replace('_small', '')
        
        if original_name not in originals:
            orphaned.append(thumbnail_path)
    
    return orphaned

def main():
    """Main function to clean up unreferenced images."""
    parser = argparse.ArgumentParser(description='Clean up unreferenced images from media directory')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be deleted without actually deleting')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--include-orphaned-thumbnails', action='store_true',
                       help='Also clean up orphaned thumbnail files')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("Starting image cleanup process...")
    logger.info(f"Media directory: {MEDIA_DIR}")
    logger.info(f"Dry run: {args.dry_run}")
    
    # Get database session
    db = SessionLocal()
    
    try:
        # Get all referenced images from database
        logger.info("Scanning database for referenced images...")
        referenced_files = get_referenced_images(db)
        
        # Get all files in media directory
        logger.info("Scanning media directory for files...")
        all_files = get_all_image_files(MEDIA_DIR)
        logger.info(f"Found {len(all_files)} total image files")
        
        # Calculate current total size
        current_total_size = sum(calculate_file_size(f) for f in all_files)
        logger.info(f"Current total size: {current_total_size / (1024*1024):.1f} MB")
        
        # Delete unreferenced images
        logger.info("Finding unreferenced images...")
        files_deleted, bytes_freed = delete_unreferenced_images(
            referenced_files, MEDIA_DIR, args.dry_run
        )
        
        # Handle orphaned thumbnails if requested
        orphaned_freed = 0
        orphaned_count = 0
        if args.include_orphaned_thumbnails:
            logger.info("Finding orphaned thumbnails...")
            orphaned_thumbnails = find_orphaned_thumbnails(MEDIA_DIR)
            
            if orphaned_thumbnails:
                logger.info(f"Found {len(orphaned_thumbnails)} orphaned thumbnails")
                
                for thumbnail in orphaned_thumbnails:
                    size = calculate_file_size(thumbnail)
                    orphaned_freed += size
                    
                    if args.dry_run:
                        logger.info(f"Would delete orphaned thumbnail: {thumbnail.name} ({size / 1024:.1f} KB)")
                    else:
                        try:
                            thumbnail.unlink()
                            orphaned_count += 1
                            logger.info(f"Deleted orphaned thumbnail: {thumbnail.name} ({size / 1024:.1f} KB)")
                        except (OSError, FileNotFoundError) as e:
                            logger.error(f"Failed to delete {thumbnail}: {e}")
        
        # Final summary
        total_freed = bytes_freed + orphaned_freed
        total_deleted = files_deleted + orphaned_count
        
        logger.info("=" * 60)
        logger.info("IMAGE CLEANUP SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total files processed: {len(all_files)}")
        logger.info(f"Referenced files: {len(referenced_files)}")
        logger.info(f"Files to delete: {total_deleted}")
        logger.info(f"Space to free: {total_freed / (1024*1024):.1f} MB")
        logger.info(f"Percentage of space freed: {(total_freed / current_total_size * 100):.1f}%")
        
        if args.dry_run:
            logger.info("This was a dry run - no files were actually deleted")
            logger.info("Run without --dry-run to perform the actual cleanup")
        else:
            logger.info(f"Successfully deleted {total_deleted} files")
            logger.info(f"Freed {total_freed / (1024*1024):.1f} MB of space")
        
        return 0 if total_freed > 0 else 1
        
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return 1
        
    finally:
        db.close()

if __name__ == "__main__":
    sys.exit(main()) 