#!/bin/bash
set -e

echo "Python version: $(python --version)"
echo "Python path: $(which python)"

echo "Waiting for database to be ready..."
# Wait for the database to be ready
while ! pg_isready -h ${POSTGRES_SERVER:-db} -p ${POSTGRES_PORT:-5432} -U ${POSTGRES_USER:-postgres} > /dev/null 2>&1; do
  echo "Waiting for database to be ready..."
  sleep 1
done

echo "Database is ready!"
echo "Initializing database..."

# Run the database initialization
if python -m app.db.init_db; then
  echo "Database initialization complete!"
else
  echo "Database initialization failed!"
  echo "Continuing anyway, but you may need to manually initialize the database."
fi

# Start the FastAPI application with uvicorn
echo "Starting FastAPI application..."
exec uvicorn app.main:app --host 0.0.0.0 --port 8000