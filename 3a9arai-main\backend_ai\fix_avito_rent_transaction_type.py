#!/usr/bin/env python3
"""
Script to fix transaction type for existing Avito rent listings.
This script will find all listings where scraper is 'avito_rent' but transaction_type is 'sell',
and update them to have transaction_type 'rent'.
"""

import sys
import os
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the app directory to the path so we can import models
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models.listing import Listing
from app.db.session import SessionLocal

def fix_avito_rent_transaction_types():
    """Fix transaction types for Avito rent listings."""
    
    print("🔍 Starting Avito rent transaction type fix...")
    print(f"📅 Timestamp: {datetime.now()}")
    print("-" * 60)
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Find all listings that need to be fixed
        print("🔍 Searching for listings that need fixing...")
        
        # Query for listings with avito_rent scraper but wrong transaction type
        listings_to_fix = db.query(Listing).filter(
            Listing.scraper == "avito_rent",
            Listing.transaction_type == "sell"
        ).all()
        
        print(f"📊 Found {len(listings_to_fix)} listings that need fixing")
        
        if len(listings_to_fix) == 0:
            print("✅ No listings need fixing! All Avito rent listings already have correct transaction type.")
            return
        
        print("\n📋 Details of listings to be fixed:")
        print("-" * 60)
        
        for i, listing in enumerate(listings_to_fix, 1):
            print(f"{i:3d}. ID: {listing.id:6d} | URL: {listing.url[:80]}...")
            print(f"     Title: {listing.title[:60]}...")
            print(f"     Current transaction_type: {listing.transaction_type}")
            print(f"     Will be changed to: rent")
            print()
        
        # Ask for confirmation
        print(f"⚠️  About to update {len(listings_to_fix)} listings.")
        response = input("Do you want to proceed? (y/N): ").lower().strip()
        
        if response != 'y':
            print("❌ Operation cancelled by user.")
            return
        
        # Perform the update
        print("\n🔄 Updating listings...")
        
        updated_count = 0
        failed_count = 0
        
        for listing in listings_to_fix:
            try:
                listing.transaction_type = "rent"
                listing.updated_at = datetime.utcnow()
                updated_count += 1
                
                if updated_count % 10 == 0:
                    print(f"   Updated {updated_count} listings...")
                    
            except Exception as e:
                print(f"❌ Failed to update listing ID {listing.id}: {e}")
                failed_count += 1
        
        # Commit all changes
        print("💾 Committing changes to database...")
        db.commit()
        
        print("\n" + "=" * 60)
        print("✅ SUMMARY")
        print("=" * 60)
        print(f"📊 Total listings found: {len(listings_to_fix)}")
        print(f"✅ Successfully updated: {updated_count}")
        print(f"❌ Failed to update: {failed_count}")
        
        if failed_count > 0:
            print(f"⚠️  {failed_count} listings failed to update. Check the logs above.")
        else:
            print("🎉 All listings updated successfully!")
            
        print(f"📅 Completed at: {datetime.now()}")
        
    except Exception as e:
        print(f"❌ Error occurred: {e}")
        print("🔄 Rolling back changes...")
        db.rollback()
        raise
        
    finally:
        db.close()

def verify_fix():
    """Verify that the fix was applied correctly."""
    
    print("\n🔍 Verifying fix...")
    print("-" * 30)
    
    db = SessionLocal()
    
    try:
        # Count listings with avito_rent scraper and sell transaction type (should be 0)
        wrong_count = db.query(Listing).filter(
            Listing.scraper == "avito_rent",
            Listing.transaction_type == "sell"
        ).count()
        
        # Count listings with avito_rent scraper and rent transaction type
        correct_count = db.query(Listing).filter(
            Listing.scraper == "avito_rent",
            Listing.transaction_type == "rent"
        ).count()
        
        print(f"📊 Avito rent listings with 'sell' transaction type: {wrong_count}")
        print(f"📊 Avito rent listings with 'rent' transaction type: {correct_count}")
        
        if wrong_count == 0:
            print("✅ Verification successful! All Avito rent listings have correct transaction type.")
        else:
            print(f"⚠️  Verification failed! {wrong_count} listings still have wrong transaction type.")
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        
    finally:
        db.close()

def show_statistics():
    """Show statistics about transaction types by scraper."""
    
    print("\n📊 STATISTICS BY SCRAPER")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Get statistics using raw SQL for better performance
        result = db.execute(text("""
            SELECT 
                scraper, 
                transaction_type, 
                COUNT(*) as count
            FROM listings 
            WHERE scraper IS NOT NULL
            GROUP BY scraper, transaction_type
            ORDER BY scraper, transaction_type
        """))
        
        current_scraper = None
        for row in result:
            scraper, transaction_type, count = row
            
            if scraper != current_scraper:
                if current_scraper is not None:
                    print()
                print(f"🔧 {scraper}:")
                current_scraper = scraper
            
            print(f"  {transaction_type}: {count} listings")
        
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")
        
    finally:
        db.close()

if __name__ == "__main__":
    print("🛠️  AVITO RENT TRANSACTION TYPE FIX SCRIPT")
    print("=" * 60)
    
    # Show current statistics
    show_statistics()
    
    # Run the fix
    fix_avito_rent_transaction_types()
    
    # Verify the fix
    verify_fix()
    
    # Show updated statistics
    show_statistics()
    
    print("\n🎉 Script completed!") 