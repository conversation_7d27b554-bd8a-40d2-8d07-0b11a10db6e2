from sqlalchemy import Column, Integer, String, Text, DateTime
from datetime import datetime

from app.db.session import Base

class ScraperActivity(Base):
    __tablename__ = "scraper_activity"

    id = Column(Integer, primary_key=True, index=True)
    scraper_name = Column(String(50), nullable=False)
    status = Column(String(20), nullable=False)
    message = Column(Text, nullable=True)
    listings_added = Column(Integer, default=0)
    start_time = Column(DateTime, nullable=False, default=datetime.utcnow)
    end_time = Column(DateTime, nullable=True) 