"""add view_history table

Revision ID: add_view_history_table
Revises: a7a874c33253
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_view_history_table'
down_revision = 'a7a874c33253'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('view_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('listing_id', sa.Integer(), nullable=False),
    sa.Column('viewed_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['listing_id'], ['listings.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_view_history_user_listing', 'view_history', ['user_id', 'listing_id'], unique=False)
    op.create_index('ix_view_history_user_viewed_at', 'view_history', ['user_id', 'viewed_at'], unique=False)
    op.create_index(op.f('ix_view_history_id'), 'view_history', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_view_history_id'), table_name='view_history')
    op.drop_index('ix_view_history_user_viewed_at', table_name='view_history')
    op.drop_index('ix_view_history_user_listing', table_name='view_history')
    op.drop_table('view_history')
    # ### end Alembic commands ### 