#!/usr/bin/env python3

import sys
import os
sys.path.append('/app')

from app.db.session import SessionLocal
from app.models.listing import Listing
import json
from collections import Counter

def main():
    db = SessionLocal()
    try:
        listings = db.query(Listing).all()
        print(f'Analyzing {len(listings)} listings...')
        
        all_field_names = []
        field_name_examples = {}
        
        for listing in listings:
            if listing.additional_info:
                for info_item in listing.additional_info:
                    name = info_item.get('name')
                    value = info_item.get('value')
                    notes = info_item.get('notes_for_user')
                    
                    if name:
                        all_field_names.append(name)
                        if name not in field_name_examples:
                            field_name_examples[name] = {
                                'values': [],
                                'with_notes': []
                            }
                        
                        field_name_examples[name]['values'].append(value)
                        if notes:
                            field_name_examples[name]['with_notes'].append({
                                'value': value,
                                'notes': notes
                            })
        
        # Count frequencies
        field_counts = Counter(all_field_names)
        
        print('\nField names and their frequencies:')
        for name, count in field_counts.most_common():
            print(f'"{name}": {count} times')
            
        print('\nDetailed field examples:')
        for name, data in field_name_examples.items():
            print(f'\n=== {name} ===')
            unique_values = list(set(str(v) for v in data['values'] if v is not None))
            print(f'  Example values: {unique_values[:5]}')  # Show first 5 unique values
            if data['with_notes']:
                print(f'  Examples with notes:')
                for item in data['with_notes'][:2]:  # Show first 2 examples with notes
                    print(f'    Value: {item["value"]} -> Note: {item["notes"]}')
                    
    finally:
        db.close()

if __name__ == '__main__':
    main() 