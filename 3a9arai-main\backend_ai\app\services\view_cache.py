"""Redis cache service for optimizing view tracking operations."""
import json
import logging
import os
import time
from typing import List, Optional, Set, Tuple, Dict, Any
from datetime import datetime, timedelta
import redis
from app.services.scrapers.redis_utils import redis_client

logger = logging.getLogger(__name__)

class ViewCacheService:
    """Redis cache service for view tracking operations to improve performance."""
    
    def __init__(self):
        """Initialize the view cache service."""
        self.redis = redis_client
        self.view_count_ttl = 3600  # 1 hour TTL for view counts
        self.popular_listings_ttl = 1800  # 30 minutes TTL for popular listings
        self.user_history_ttl = 1800  # 30 minutes TTL for user history
        self.view_dedup_ttl = 3600  # 1 hour TTL for view deduplication
        
    def _get_view_count_key(self, listing_id: int, hours: int = 24) -> str:
        """Generate cache key for listing view count."""
        return f"view_count:{listing_id}:{hours}h"
    
    def _get_popular_listings_key(self, hours: int = 24, limit: int = 10) -> str:
        """Generate cache key for popular listings."""
        return f"popular_listings:{hours}h:{limit}"
    
    def _get_user_history_key(self, user_id: int) -> str:
        """Generate cache key for user's view history listing IDs."""
        return f"user_history:{user_id}"
    
    def _get_user_history_count_key(self, user_id: int) -> str:
        """Generate cache key for user's view history count."""
        return f"user_history_count:{user_id}"
    
    def _get_view_dedup_key(self, listing_id: int, ip_address: str) -> str:
        """Generate cache key for view deduplication."""
        return f"view_dedup:{listing_id}:{ip_address}"
    
    def _get_view_tracked_key(self, listing_id: int, ip_address: str) -> str:
        """Generate cache key to track if a view was recently recorded."""
        return f"view_tracked:{listing_id}:{ip_address}"
    
    # View Count Caching
    def get_view_count(self, listing_id: int, hours: int = 24) -> Optional[int]:
        """Get cached view count for a listing.
        
        Args:
            listing_id: ID of the listing
            hours: Time period for counting views
            
        Returns:
            View count if cached, None if not in cache
        """
        try:
            key = self._get_view_count_key(listing_id, hours)
            cached_count = self.redis.get(key)
            
            if cached_count is not None:
                return int(cached_count.decode('utf-8'))
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting view count from cache for listing {listing_id}: {str(e)}")
            return None
    
    def set_view_count(self, listing_id: int, count: int, hours: int = 24):
        """Set view count in cache.
        
        Args:
            listing_id: ID of the listing
            count: View count to cache
            hours: Time period this count represents
        """
        try:
            key = self._get_view_count_key(listing_id, hours)
            self.redis.setex(key, self.view_count_ttl, str(count))
            logger.debug(f"Cached view count for listing {listing_id}: {count}")
            
        except Exception as e:
            logger.error(f"Error setting view count cache for listing {listing_id}: {str(e)}")
    
    def increment_view_count(self, listing_id: int, hours: int = 24) -> int:
        """Increment view count in cache and return new count.
        
        Args:
            listing_id: ID of the listing
            hours: Time period for counting views
            
        Returns:
            New view count
        """
        try:
            key = self._get_view_count_key(listing_id, hours)
            new_count = self.redis.incr(key)
            self.redis.expire(key, self.view_count_ttl)
            logger.debug(f"Incremented view count for listing {listing_id}: {new_count}")
            return new_count
            
        except Exception as e:
            logger.error(f"Error incrementing view count cache for listing {listing_id}: {str(e)}")
            return 1  # Default to 1 if error
    
    # Popular Listings Caching
    def get_popular_listings(self, hours: int = 24, limit: int = 10) -> Optional[List[int]]:
        """Get cached popular listing IDs.
        
        Args:
            hours: Time period for popularity calculation
            limit: Number of listings to return
            
        Returns:
            List of listing IDs if cached, None if not in cache
        """
        try:
            key = self._get_popular_listings_key(hours, limit)
            cached_data = self.redis.get(key)
            
            if cached_data is not None:
                listing_ids = json.loads(cached_data.decode('utf-8'))
                return listing_ids
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting popular listings from cache: {str(e)}")
            return None
    
    def set_popular_listings(self, listing_ids: List[int], hours: int = 24, limit: int = 10):
        """Set popular listings in cache.
        
        Args:
            listing_ids: List of popular listing IDs
            hours: Time period this popularity represents
            limit: Limit used for this query
        """
        try:
            key = self._get_popular_listings_key(hours, limit)
            data = json.dumps(listing_ids)
            self.redis.setex(key, self.popular_listings_ttl, data)
            logger.debug(f"Cached popular listings: {len(listing_ids)} items")
            
        except Exception as e:
            logger.error(f"Error setting popular listings cache: {str(e)}")
    
    def invalidate_popular_listings_cache(self):
        """Invalidate all popular listings cache entries."""
        try:
            pattern = "popular_listings:*"
            keys = self.redis.keys(pattern)
            
            if keys:
                self.redis.delete(*keys)
                logger.debug(f"Invalidated {len(keys)} popular listings cache keys")
            
        except Exception as e:
            logger.error(f"Error invalidating popular listings cache: {str(e)}")
    
    # User View History Caching
    def get_user_history_listing_ids(self, user_id: int) -> Optional[List[int]]:
        """Get cached user's view history listing IDs.
        
        Args:
            user_id: ID of the user
            
        Returns:
            List of listing IDs if cached, None if not in cache
        """
        try:
            key = self._get_user_history_key(user_id)
            cached_data = self.redis.get(key)
            
            if cached_data is not None:
                listing_ids = json.loads(cached_data.decode('utf-8'))
                return listing_ids
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user history from cache for user {user_id}: {str(e)}")
            return None
    
    def set_user_history_listing_ids(self, user_id: int, listing_ids: List[int]):
        """Cache user's view history listing IDs.
        
        Args:
            user_id: ID of the user
            listing_ids: List of viewed listing IDs (ordered by most recent first)
        """
        try:
            key = self._get_user_history_key(user_id)
            data = json.dumps(listing_ids)
            self.redis.setex(key, self.user_history_ttl, data)
            
            # Also cache the count
            count_key = self._get_user_history_count_key(user_id)
            self.redis.setex(count_key, self.user_history_ttl, len(listing_ids))
            
            logger.debug(f"Cached user history for user {user_id}: {len(listing_ids)} items")
            
        except Exception as e:
            logger.error(f"Error caching user history for user {user_id}: {str(e)}")
    
    def get_user_history_count(self, user_id: int) -> Optional[int]:
        """Get cached count of user's view history.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Count if cached, None if not in cache
        """
        try:
            key = self._get_user_history_count_key(user_id)
            cached_count = self.redis.get(key)
            
            if cached_count is not None:
                return int(cached_count.decode('utf-8'))
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user history count from cache for user {user_id}: {str(e)}")
            return None
    
    def add_to_user_history(self, user_id: int, listing_id: int):
        """Add a listing to user's view history cache and invalidate if needed.
        
        Args:
            user_id: ID of the user
            listing_id: ID of the viewed listing
        """
        try:
            # Get current history
            current_history = self.get_user_history_listing_ids(user_id)
            
            if current_history is not None:
                # Remove if already exists and add to front
                if listing_id in current_history:
                    current_history.remove(listing_id)
                current_history.insert(0, listing_id)
                
                # Maintain max 100 entries
                if len(current_history) > 100:
                    current_history = current_history[:100]
                
                # Update cache
                self.set_user_history_listing_ids(user_id, current_history)
            else:
                # Cache doesn't exist, will be populated on next request
                self.invalidate_user_history_cache(user_id)
            
        except Exception as e:
            logger.error(f"Error adding to user history cache for user {user_id}: {str(e)}")
    
    def invalidate_user_history_cache(self, user_id: int):
        """Invalidate user's view history cache.
        
        Args:
            user_id: ID of the user
        """
        try:
            history_key = self._get_user_history_key(user_id)
            count_key = self._get_user_history_count_key(user_id)
            
            self.redis.delete(history_key, count_key)
            logger.debug(f"Invalidated view history cache for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error invalidating user history cache for user {user_id}: {str(e)}")
    
    # View Deduplication Caching
    def should_track_view(self, listing_id: int, ip_address: str) -> bool:
        """Check if a view should be tracked (not a duplicate within the hour).
        
        Args:
            listing_id: ID of the listing
            ip_address: IP address of the viewer
            
        Returns:
            True if view should be tracked, False if it's a duplicate
        """
        try:
            key = self._get_view_dedup_key(listing_id, ip_address)
            exists = self.redis.exists(key)
            
            if not exists:
                # Set deduplication marker for 1 hour
                self.redis.setex(key, self.view_dedup_ttl, '1')
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking view deduplication for listing {listing_id}: {str(e)}")
            return True  # Default to tracking on error
    
    def mark_view_tracked(self, listing_id: int, ip_address: str):
        """Mark that a view has been tracked for deduplication.
        
        Args:
            listing_id: ID of the listing
            ip_address: IP address of the viewer
        """
        try:
            key = self._get_view_tracked_key(listing_id, ip_address)
            self.redis.setex(key, self.view_dedup_ttl, '1')
            logger.debug(f"Marked view as tracked for listing {listing_id}")
            
        except Exception as e:
            logger.error(f"Error marking view as tracked for listing {listing_id}: {str(e)}")
    
    # Batch Operations
    def get_view_counts_batch(self, listing_ids: List[int], hours: int = 24) -> Dict[int, int]:
        """Get view counts for multiple listings efficiently.
        
        Args:
            listing_ids: List of listing IDs
            hours: Time period for counting views
            
        Returns:
            Dictionary mapping listing_id -> view_count
        """
        try:
            if not listing_ids:
                return {}
            
            # Build pipeline for batch operations
            pipe = self.redis.pipeline()
            keys = []
            
            for listing_id in listing_ids:
                key = self._get_view_count_key(listing_id, hours)
                keys.append(key)
                pipe.get(key)
            
            results = pipe.execute()
            
            # Process results
            view_counts = {}
            for i, listing_id in enumerate(listing_ids):
                if results[i] is not None:
                    view_counts[listing_id] = int(results[i].decode('utf-8'))
                # Note: Missing entries will be fetched from database
            
            return view_counts
            
        except Exception as e:
            logger.error(f"Error getting batch view counts: {str(e)}")
            return {}
    
    def set_view_counts_batch(self, view_counts: Dict[int, int], hours: int = 24):
        """Set view counts for multiple listings efficiently.
        
        Args:
            view_counts: Dictionary mapping listing_id -> view_count
            hours: Time period these counts represent
        """
        try:
            if not view_counts:
                return
            
            pipe = self.redis.pipeline()
            
            for listing_id, count in view_counts.items():
                key = self._get_view_count_key(listing_id, hours)
                pipe.setex(key, self.view_count_ttl, str(count))
            
            pipe.execute()
            logger.debug(f"Batch cached view counts for {len(view_counts)} listings")
            
        except Exception as e:
            logger.error(f"Error setting batch view counts: {str(e)}")
    
    # Cache Warming
    def warm_view_counts(self, view_data: List[Tuple[int, int]], hours: int = 24):
        """Warm up the cache with view count data.
        
        Args:
            view_data: List of (listing_id, count) tuples
            hours: Time period these counts represent
        """
        try:
            view_counts = {listing_id: count for listing_id, count in view_data}
            self.set_view_counts_batch(view_counts, hours)
            logger.debug(f"Warmed view count cache with {len(view_data)} entries")
            
        except Exception as e:
            logger.error(f"Error warming view count cache: {str(e)}")
    
    # Cache Statistics
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            # Count keys by pattern
            view_count_keys = len(self.redis.keys("view_count:*"))
            popular_keys = len(self.redis.keys("popular_listings:*"))
            history_keys = len(self.redis.keys("user_history:*"))
            dedup_keys = len(self.redis.keys("view_dedup:*"))
            
            return {
                "view_count_keys": view_count_keys,
                "popular_listings_keys": popular_keys,
                "user_history_keys": history_keys,
                "dedup_keys": dedup_keys,
                "total_keys": view_count_keys + popular_keys + history_keys + dedup_keys
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {str(e)}")
            return {}


# Global instance
view_cache = ViewCacheService() 