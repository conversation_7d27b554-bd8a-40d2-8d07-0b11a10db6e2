from sqlalchemy import create_engine, inspect, text
import os

# Get database URL from environment or use the default
# Read the database credentials from the app's config
try:
    from app.core.config import settings
    DATABASE_URL = settings.SQLALCHEMY_DATABASE_URI
except ImportError:
    # Use environment variable or backup default
    DATABASE_URL = os.getenv("DATABASE_URL", "*************************************")

def run_migration():
    """Run manual migration for property category fields"""
    print(f"Connecting to database: {DATABASE_URL}")
    engine = create_engine(DATABASE_URL)
    inspector = inspect(engine)
    
    # Get existing columns
    existing_columns = inspector.get_columns("listings")
    existing_column_names = [col["name"] for col in existing_columns]
    print(f"Existing columns: {', '.join(existing_column_names)}")
    
    # Define columns to add with appropriate data types
    columns_to_add = {
        "property_category": "VARCHAR(50)",
        "commercial_type": "VARCHAR(50)",
        "has_showroom": "BOOLEAN",
        "has_storage": "BOOLEAN", 
        "has_reception": "BOOLEAN",
        "has_meeting_rooms": "BOOLEAN",
        "is_furnished": "BOOLEAN",
        "land_type": "VARCHAR(50)",
        "land_zoning": "VARCHAR(100)",
        "is_serviced": "BOOLEAN",
        "parking_spaces": "INTEGER"
    }
    
    # Create connection and begin transaction
    with engine.connect() as connection:
        with connection.begin():
            # Add each column if it doesn't exist
            for column, data_type in columns_to_add.items():
                if column not in existing_column_names:
                    print(f"Adding column '{column}' with type '{data_type}'")
                    connection.execute(text(f"ALTER TABLE listings ADD COLUMN {column} {data_type}"))
                else:
                    print(f"Column '{column}' already exists, skipping")
            
            # Update property_category for existing records if the column was just added
            if "property_category" not in existing_column_names:
                print("Updating property_category based on property_type...")
                # Set residential property types
                connection.execute(text("""
                UPDATE listings 
                SET property_category = 'residential' 
                WHERE property_type IN ('Appartement', 'Maison', 'Villa', 'Studio Apartment', 'house', 'apartment')
                """))
                
                # Set commercial property types
                connection.execute(text("""
                UPDATE listings 
                SET property_category = 'commercial' 
                WHERE property_type IN ('Bureau', 'Local commercial', 'commercial property', 'Store', 'Office')
                """))
                
                # Set land property types
                connection.execute(text("""
                UPDATE listings 
                SET property_category = 'land' 
                WHERE property_type IN ('Terrain', 'Plot')
                """))
                
                # Set default for remaining records
                connection.execute(text("""
                UPDATE listings 
                SET property_category = 'residential' 
                WHERE property_category IS NULL OR property_category = ''
                """))
    
    print("Migration completed successfully")

def purge_database():
    """Purge all records from the database"""
    print(f"Connecting to database: {DATABASE_URL}")
    engine = create_engine(DATABASE_URL)
    
    # Check for and purge only the listings table
    tables_to_purge = ["listings"]
    
    with engine.connect() as connection:
        # Get all table names in the database
        inspector = inspect(engine)
        all_tables = inspector.get_table_names()
        print(f"Available tables in database: {', '.join(all_tables)}")
        
        with connection.begin():
            # First count records in each table
            for table in tables_to_purge:
                result = connection.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"Found {count} records in table '{table}'")
            
            # Then delete all records from each table
            for table in tables_to_purge:
                connection.execute(text(f"DELETE FROM {table}"))
                print(f"Deleted all records from table '{table}'")
            
            # Reset sequences (auto-increment IDs)
            for table in tables_to_purge:
                try:
                    connection.execute(text(f"ALTER SEQUENCE {table}_id_seq RESTART WITH 1"))
                    print(f"Reset sequence for table '{table}'")
                except Exception as e:
                    print(f"Error resetting sequence for table '{table}': {str(e)}")
            
            # Verify deletion
            for table in tables_to_purge:
                result = connection.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.fetchone()[0]
                print(f"Table '{table}' now has {count} records")
    
    print("Database purge completed successfully")

if __name__ == "__main__":
    # run_migration()  # Comment out to prevent running migration
    purge_database()  # Uncomment to run the purge 