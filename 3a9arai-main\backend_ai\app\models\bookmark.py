from sqlalchemy import <PERSON>umn, Integer, ForeignKey, DateTime, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.session import Base

class Bookmark(Base):
    __tablename__ = "bookmarks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    listing_id = Column(Integer, ForeignKey("listings.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="bookmarks")
    listing = relationship("Listing", back_populates="bookmarked_by")
    
    # Ensure a user can only bookmark a listing once
    __table_args__ = (
        UniqueConstraint('user_id', 'listing_id', name='uq_user_listing_bookmark'),
    ) 