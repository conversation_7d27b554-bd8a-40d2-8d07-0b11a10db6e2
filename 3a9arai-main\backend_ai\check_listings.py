#!/usr/bin/env python3

import sys
import os
sys.path.append('/app')

from app.db.session import SessionLocal
from app.models.listing import Listing
import json

def main():
    db = SessionLocal()
    try:
        count = db.query(Listing).count()
        print(f'Total listings: {count}')
        
        if count > 0:
            listing = db.query(Listing).first()
            print('\nSample listing data:')
            print(f'ID: {listing.id}')
            print(f'Title: {listing.title}')
            
            # Check what attributes exist
            print('\nListing attributes:')
            attrs = [attr for attr in dir(listing) if not attr.startswith('_') and not callable(getattr(listing, attr))]
            for attr in sorted(attrs):
                try:
                    value = getattr(listing, attr)
                    if value is not None:
                        print(f'{attr}: {value}')
                except Exception as e:
                    print(f'{attr}: <error: {e}>')
            
            print('\nAdditional Info content:')
            if hasattr(listing, 'additional_info') and listing.additional_info:
                print(json.dumps(listing.additional_info, indent=2))
            else:
                print('No additional_info found or field does not exist')
                
            # Check specific fields
            print('\nChecking specific fields:')
            fields_to_check = ['size', 'size_sqm', 'rooms', 'bathrooms', 'additional_info']
            for field in fields_to_check:
                if hasattr(listing, field):
                    value = getattr(listing, field)
                    print(f'{field}: {value}')
                else:
                    print(f'{field}: <field does not exist>')
                    
    finally:
        db.close()

if __name__ == '__main__':
    main() 