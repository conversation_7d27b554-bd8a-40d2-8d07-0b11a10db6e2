#!/usr/bin/env python3

import sys
import os
sys.path.append('/app')

from app.api.deps import prepare_listing_for_response
from app.db.session import SessionLocal
from app.models.listing import Listing
import json

def main():
    db = SessionLocal()
    try:
        listing = db.query(Listing).first()
        print('Before processing:')
        print(f'  Has rooms: {hasattr(listing, "rooms")}')
        print(f'  Has bathrooms: {hasattr(listing, "bathrooms")}')
        print(f'  Has size: {hasattr(listing, "size")}')

        result = prepare_listing_for_response(listing)
        print('\nAfter processing:')
        print(f'  rooms: {result.get("rooms")}')
        print(f'  bathrooms: {result.get("bathrooms")}')
        print(f'  floor: {result.get("floor")}')
        print(f'  elevator: {result.get("elevator")}')
        print(f'  size: {result.get("size")}')
        print(f'  rooms_notes: {result.get("rooms_notes")}')
        print(f'  elevator_notes: {result.get("elevator_notes")}')
        print(f'  has_balcony: {result.get("has_balcony")}')
        print(f'  has_terrace: {result.get("has_terrace")}')
        print(f'  living_rooms: {result.get("living_rooms")}')
        
        print('\nAdditional info structure:')
        if result.get('additional_info'):
            for item in result['additional_info'][:3]:  # Show first 3 items
                print(f'  {item["name"]}: {item["value"]} (notes: {item.get("notes_for_user", "None")})')
                
    finally:
        db.close()

if __name__ == '__main__':
    main() 