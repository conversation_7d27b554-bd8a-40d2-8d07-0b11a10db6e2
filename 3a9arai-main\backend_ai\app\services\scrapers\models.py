"""Models for scraper data structures."""
from typing import Optional, Dict, List, Union, Literal, Any
from pydantic import BaseModel, validator, Field
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)
def load_locations_from_json(file_path: str) -> dict:
    """Loads city and district data from the specified JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # The root key is "cities" which contains the list
            return data["cities"]
    except (FileNotFoundError, json.JSONDecodeError) as e:
        logger.info(f"Error loading or parsing {file_path}: {e}")
        return []

# --- Dynamic Model Creation ---

# Load the location data
location_data = load_locations_from_json('cities_and_districts_extracted.json')

# Extract city and district names into flat, unique lists
# Using a set for districts to ensure uniqueness, then converting to list
all_cities = sorted([city['name'] for city in location_data if city.get('name')])
all_districts = sorted(list(set(
    district
    for city in location_data if city.get('districts')
    for district in city['districts']
)))

# Handle the case where there might be no districts
if not all_districts:
    # Pydantic's Literal requires at least one value.
    # We use a placeholder if the list is empty.
    all_districts = [""]


# Dynamically create the Literal types
CityName = Literal[tuple(all_cities)]
logger.info(f"CityName: {CityName}")
DistrictName = Literal[tuple(all_districts)]
logger.info(f"DistrictName: {DistrictName}")

    
class AdditionalInfo(BaseModel):
    """A structured representation of a single piece of extracted information."""
    name: str = Field(..., description="The name of the feature or attribute.")
    value: Any = Field(..., description="The value of the feature or attribute.")
    notes_for_user: Optional[str] = Field(None, description="Optional context, warnings, or clarifications for the end-user.")

class PropertyListing(BaseModel):
    """
    A schema that enforces core fields and captures all other data 
    in a structured list, using standardized names for common features.
    """
    
    # 1. Core Vitals (Mandatory)
    url: str = Field(..., description="The source URL of the listing.")
    title: str = Field(..., description="A full, descriptive title for the property page.")
    price: Union[float, str] = Field(..., description="The listing price as a numeric value.")
    size_sqm: float = Field(..., description="The total size of the property in square meters.")
    note_about_size_sqm: str = Field(...)
    city: str = Field(..., description="The city where the property is located.")
    property_type: Literal["appartement", "maison", "terrain", "bureau", "local commercial", "autre", "residence"] = Field(..., description="Primary type of the property (e.g., Appartement, Villa).")
    property_category: Literal["residential", "commercial", "land"] = Field(..., description="High-level category: 'residential', 'commercial', 'land'.")
    description_html: str = Field(..., description="A well-structured HTML description.")
    contact_name: str = Field(..., description="The name of the contact person.")
    contact_phone: str = Field(..., description="The phone number of the contact person.")
    contact_email: Optional[str] = Field(None, description="The email of the contact person.") 
    images: List[str] = Field(..., description="The images of the property.")
    # 2. Flexible, Structured Information
    additional_info: List[AdditionalInfo] = Field(
        default_factory=list,
        description="A list of all other extracted details and amenities, each as a name-value pair."
    )
    
    # 3. Location, Contact & Metadata
    city: Literal[tuple(all_cities)] = Field(..., description="The detected city name. Must be one of the predefined values.")
    neighborhood: str = Field(None, description="The detected neighborhood/district.")
    scraper: str = Field(..., description="The name of the scraper used.")
    price_per_sqm: Optional[float] = Field(None, description="The price per square meter of the property.")
    transaction_type: Literal['sell', 'rent'] = Field(default='sell', description="Type of transaction: sell or rent.")
    
    @validator('price_per_sqm', always=True)
    def calculate_price_per_sqm(cls, v, values):
        """Calculate price per square meter if not already set."""
        if v is not None:
            # If a value is already set but it's zero, treat as None
            if v == 0:
                v = None
            else:
                return v
        
        try:
            price = values.get('price', 0)
            size = values.get('size', 0)
            
            # Convert string price to float if it's numeric
            if isinstance(price, str):
                price = float(price.replace(',', '')) if price.replace(',', '').isdigit() else 0
            
            # Only calculate if we have valid values
            if size and size > 0 and price and price > 0:
                calculated = round(float(price) / float(size), 2)
                # Don't return if the calculated value is extremely high or low (likely error)
                if 10 <= calculated <= 100000:  # Reasonable range for price/sqm
                    return calculated
        except (ValueError, TypeError, ZeroDivisionError):
            pass
        
        return None
    
    class Config:
        """Pydantic model configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }