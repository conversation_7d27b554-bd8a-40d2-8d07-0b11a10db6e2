#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to remove all listings that have no images.

This script will:
1. Connect to the database
2. Find all listings with no images (empty, null, or empty list)
3. Optionally delete the listings
4. Provide statistics about what was removed

Usage:
    python remove_listings_without_images.py [--dry-run] [--confirm]
"""

import sys
import os
import argparse
from typing import List, Optional

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.models.listing import Listing

def has_valid_images(images: Optional[List[str]]) -> bool:
    """
    Check if a listing has valid images.
    
    Args:
        images: List of image URLs or None
        
    Returns:
        True if the listing has at least one non-empty image URL
    """
    if not images:
        return False
    
    if not isinstance(images, list):
        return False
    
    # Check if there's at least one non-empty image URL
    for image_url in images:
        if image_url and isinstance(image_url, str) and image_url.strip():
            return True
    
    return False

def find_listings_without_images(db: Session) -> List[Listing]:
    """
    Find all listings that have no valid images.
    
    Args:
        db: Database session
        
    Returns:
        List of listings without valid images
    """
    listings_without_images = []
    
    # Get all listings
    all_listings = db.query(Listing).all()
    
    for listing in all_listings:
        if not has_valid_images(listing.images):
            listings_without_images.append(listing)
    
    return listings_without_images

def remove_listings_without_images(db: Session, dry_run: bool = False, confirmed: bool = False) -> dict:
    """
    Remove all listings that have no valid images.
    
    Args:
        db: Database session
        dry_run: If True, don't actually delete the listings
        confirmed: If True, skip confirmation prompt
        
    Returns:
        Dictionary with removal statistics
    """
    stats = {
        "total_listings": 0,
        "listings_without_images": 0,
        "listings_removed": 0,
        "errors": []
    }
    
    try:
        # Get total count first
        total_listings = db.query(Listing).count()
        stats["total_listings"] = total_listings
        
        print(f"Scanning {total_listings} listings for those without images...")
        
        # Find listings without images
        listings_to_remove = find_listings_without_images(db)
        stats["listings_without_images"] = len(listings_to_remove)
        
        if not listings_to_remove:
            print("No listings without images found.")
            return stats
        
        print(f"Found {len(listings_to_remove)} listings without valid images:")
        
        # Show details of listings to be removed
        for listing in listings_to_remove[:10]:  # Show first 10 as examples
            images_info = "No images" if not listing.images else f"Empty images list: {listing.images}"
            print(f"  - Listing {listing.id}: {listing.title[:50] if listing.title else 'No title'}... ({images_info})")
        
        if len(listings_to_remove) > 10:
            print(f"  ... and {len(listings_to_remove) - 10} more listings")
        
        # Confirmation check
        if not dry_run and not confirmed:
            print(f"\nThis will permanently delete {len(listings_to_remove)} listings from the database.")
            response = input("Are you sure you want to continue? (yes/no): ").lower().strip()
            if response not in ['yes', 'y']:
                print("Operation cancelled by user.")
                return stats
        
        # Remove listings
        if not dry_run:
            print(f"\nRemoving {len(listings_to_remove)} listings...")
            
            for listing in listings_to_remove:
                try:
                    db.delete(listing)
                    stats["listings_removed"] += 1
                    
                    if stats["listings_removed"] % 100 == 0:
                        print(f"  Removed {stats['listings_removed']} listings...")
                        
                except Exception as e:
                    error_msg = f"Error removing listing {listing.id}: {e}"
                    print(error_msg)
                    stats["errors"].append(error_msg)
            
            db.commit()
            print("Database changes committed.")
        else:
            print(f"\nDRY RUN: Would remove {len(listings_to_remove)} listings.")
            stats["listings_removed"] = len(listings_to_remove)
            
    except Exception as e:
        error_msg = f"Database error: {e}"
        print(error_msg)
        stats["errors"].append(error_msg)
        if not dry_run:
            db.rollback()
            
    return stats

def analyze_image_status(db: Session) -> dict:
    """
    Analyze the current state of images in the database.
    
    Args:
        db: Database session
        
    Returns:
        Dictionary with analysis results
    """
    analysis = {
        "total_listings": 0,
        "listings_with_images": 0,
        "listings_without_images": 0,
        "listings_with_empty_arrays": 0,
        "listings_with_null_images": 0,
        "total_images": 0
    }
    
    all_listings = db.query(Listing).all()
    analysis["total_listings"] = len(all_listings)
    
    for listing in all_listings:
        if listing.images is None:
            analysis["listings_with_null_images"] += 1
            analysis["listings_without_images"] += 1
        elif isinstance(listing.images, list):
            if len(listing.images) == 0:
                analysis["listings_with_empty_arrays"] += 1
                analysis["listings_without_images"] += 1
            else:
                # Check if there are any valid (non-empty) image URLs
                valid_images = [img for img in listing.images if img and isinstance(img, str) and img.strip()]
                if valid_images:
                    analysis["listings_with_images"] += 1
                    analysis["total_images"] += len(valid_images)
                else:
                    analysis["listings_without_images"] += 1
        else:
            # Unexpected data type
            analysis["listings_without_images"] += 1
    
    return analysis

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Remove listings without images")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be removed without actually removing")
    parser.add_argument("--confirm", action="store_true", help="Skip confirmation prompt")
    parser.add_argument("--analyze-only", action="store_true", help="Only analyze the current state, don't remove anything")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("Remove Listings Without Images Script")
    print("=" * 60)
    
    if args.analyze_only:
        print("Analysis mode: Checking current state of images in database...")
    elif args.dry_run:
        print("Dry run mode: No changes will be made to the database")
    else:
        print("WARNING: This will permanently delete listings from the database!")
    
    print()
    
    # Create database session
    db = SessionLocal()
    
    try:
        if args.analyze_only:
            # Just analyze and show statistics
            analysis = analyze_image_status(db)
            
            print("Database Analysis Results:")
            print("-" * 30)
            print(f"Total listings: {analysis['total_listings']}")
            print(f"Listings with images: {analysis['listings_with_images']}")
            print(f"Listings without images: {analysis['listings_without_images']}")
            print(f"  - With null images: {analysis['listings_with_null_images']}")
            print(f"  - With empty arrays: {analysis['listings_with_empty_arrays']}")
            print(f"Total images in database: {analysis['total_images']}")
            
            if analysis['listings_with_images'] > 0:
                avg_images = analysis['total_images'] / analysis['listings_with_images']
                print(f"Average images per listing (with images): {avg_images:.2f}")
        else:
            # Run removal process
            stats = remove_listings_without_images(db, dry_run=args.dry_run, confirmed=args.confirm)
            
            # Print results
            print()
            print("=" * 60)
            print("Removal Results")
            print("=" * 60)
            print(f"Total listings: {stats['total_listings']}")
            print(f"Listings without images: {stats['listings_without_images']}")
            print(f"Listings removed: {stats['listings_removed']}")
            
            if stats["errors"]:
                print(f"Errors: {len(stats['errors'])}")
                for error in stats["errors"]:
                    print(f"  - {error}")
            else:
                print("No errors occurred.")
                
            if args.dry_run:
                print("\nThis was a dry run. No changes were made to the database.")
                print("Run without --dry-run to actually remove the listings.")
            else:
                print(f"\nSuccessfully removed {stats['listings_removed']} listings without images!")
                remaining = stats['total_listings'] - stats['listings_removed']
                print(f"Remaining listings in database: {remaining}")
            
    finally:
        db.close()

if __name__ == "__main__":
    main() 