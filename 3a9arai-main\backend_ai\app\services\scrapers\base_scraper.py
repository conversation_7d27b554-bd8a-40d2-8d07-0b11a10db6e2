"""Base class for real estate scrapers using distributed architecture with queue management."""
import logging
import os
import json
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import List, Dict, Any, Optional, Callable, Set
from sqlalchemy.orm import Session
import redis
from celery import Celery
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.remote.webdriver import WebDriver
from app.db.session import SessionLocal
from app.models.listing import Listing
from app.models.scraper_activity import ScraperActivity
from app.services.scrapers.llm_service import process_listing_text
from app.core.config import settings
from app.services.scrapers.redis_utils import (
    init_scraper_queues, purge_queues, purge_scraper_queues_only, get_queue_length, get_all_queue_lengths,
    add_to_url_queue, add_to_details_queue, add_to_processed_queue,
    set_scraper_status, get_scraper_status, check_control_queue, 
    send_control_command, get_queue_name
)
from app.services.utils.s3_image_utils import S3ImageProcessor

logger = logging.getLogger(__name__)

# Initialize Celery
celery = Celery('scraper_tasks',
                broker=os.environ.get('REDIS_URL', 'redis://redis:6379/0'),
                backend=os.environ.get('REDIS_URL', 'redis://redis:6379/0'))

# Initialize Redis
redis_client = redis.Redis.from_url(
    os.environ.get('REDIS_URL', 'redis://redis:6379/0'),
    max_connections=20,  # Limit connections in pool
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True
)

class BaseScraper(ABC):
    """Base class for all property scrapers using a distributed architecture with queue management."""
    
    def __init__(self, db: Session, name: str):
        """Initialize the scraper.
        
        Args:
            db: Database session
            name: Name of the scraper
        """
        self.db = db
        self.name = name
        self.activity_id = None
        self.progress_callback = None
        self.processed_urls = set()
        self.is_running = False
        self.should_stop = False
        
        # Queue names
        self.url_queue = f"{name}_url_queue"
        self.details_queue = f"{name}_details_queue"
        self.processed_queue = f"{name}_processed_queue"
        self.control_queue = f"{name}_control_queue"
        
        # Status key in Redis
        self.status_key = f"{name}_status"
        
        # Selenium Hub configuration
        self.selenium_hub_url = os.environ.get('SELENIUM_HUB_URL', 'http://selenium-hub:4444/wd/hub')
        
        # Initialize queues in Redis if they don't exist
        self._init_queues()
    
    def get_selenium_driver(self) -> WebDriver:
        """Get a Selenium WebDriver instance from the Selenium Hub.
        
        Returns:
            WebDriver: A configured WebDriver instance
        """
        try:
            # Configure ChromeOptions
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--start-maximized")  # Run in headless mode
            
            # Connect to Selenium Hub
            driver = webdriver.Remote(
                command_executor=self.selenium_hub_url,
                options=chrome_options
            )
            
            # Set timeouts
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            logger.info(f"Successfully created WebDriver instance from Selenium Hub: {self.selenium_hub_url}")
            return driver
            
        except Exception as e:
            logger.error(f"Error creating WebDriver instance: {str(e)}")
            raise
    
    def _init_queues(self):
        """Initialize Redis queues if they don't exist."""
        init_scraper_queues(self.name)
    
    def start(self) -> bool:
        """Start the scraper.
        
        Returns:
            bool: True if the scraper started successfully, False otherwise
        """
        try:
            if self.is_running:
                logger.warning(f"Scraper {self.name} is already running")
                return False
            
            self.is_running = True
            
            # Set scraper status to running in Redis
            set_scraper_status(self.name, "running")
            
            # Clear control queue
            control_queue = get_queue_name(self.name, "control")
            redis_client.delete(control_queue)
            
            # Log start activity
            self.log_activity("started", "Scraper started")
            
            logger.info(f"Scraper {self.name} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting scraper {self.name}: {str(e)}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """Stop the scraper.
        
        Returns:
            bool: True if the scraper stopped successfully, False otherwise
        """
        try:
            if not self.is_running:
                logger.warning(f"Scraper {self.name} is not running")
                return False
            
            # Set scraper status to stopped in Redis
            set_scraper_status(self.name, "stopped")
            
            # Send stop signal to control queue
            send_control_command(self.name, "stop")
            
            # Purge only scraper input queues, preserving LLM processing queues
            purge_scraper_queues_only(self.name)
            
            # Wait for scraper to stop (with timeout)
            timeout = 30  # seconds
            start_time = time.time()
            
            while self.is_running and (time.time() - start_time) < timeout:
                time.sleep(1)
            
            if self.is_running:
                logger.warning(f"Scraper {self.name} did not stop gracefully within timeout")
                self.is_running = False
            
            # Log stop activity
            self.log_activity("stopped", "Scraper stopped")
            
            logger.info(f"Scraper {self.name} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping scraper {self.name}: {str(e)}")
            return False
    
    def purge_queues(self) -> bool:
        """Purge all task queues.
        
        Returns:
            bool: True if the queues were purged successfully, False otherwise
        """
        return purge_queues(self.name)
    
    def purge_scraper_queues_only(self) -> bool:
        """Purge only scraper input queues, preserving LLM processing queues.
        
        This method only purges the URL queue (urls to be scraped) but preserves
        the details and processed queues which contain items for LLM processing.
        
        Returns:
            bool: True if the queues were purged successfully, False otherwise
        """
        return purge_scraper_queues_only(self.name)
    
    def should_continue(self) -> bool:
        """Check if the scraper should continue running.
        
        Returns:
            bool: True if the scraper should continue, False otherwise
        """
        try:
            # Check Redis for stop signal
            status = get_scraper_status(self.name)
            logger.info(f"Scraper {self.name} status: {status}")
            if status and status == "stopped":
                logger.info(f"Scraper {self.name} status is 'stopped', should not continue")
                return False
            
            # Check control queue for commands - safely
            try:
                command = check_control_queue(self.name)
                if command and command.get("command"):
                    cmd = command.get("command")
                    if cmd == "stop":
                        self.should_stop = True
                        self.is_running = False
                        logger.info(f"Scraper {self.name} received stop command")
                        return False
                    elif cmd == "pause":
                        logger.info(f"Scraper {self.name} received pause command, waiting for resume")
                        # Wait for resume command
                        while True:
                            time.sleep(1)
                            resume_command = check_control_queue(self.name)
                            if resume_command and resume_command.get("command") == "resume":
                                logger.info(f"Scraper {self.name} received resume command, continuing")
                                break
                            # Also check if stop command received while paused
                            elif resume_command and resume_command.get("command") == "stop":
                                logger.info(f"Scraper {self.name} received stop command while paused")
                                self.should_stop = True
                                self.is_running = False
                                return False
                            # Check if scraper was manually stopped via status
                            current_status = get_scraper_status(self.name)
                            if current_status and current_status == "stopped":
                                logger.info(f"Scraper {self.name} was manually stopped while paused")
                                return False
            except Exception as e:
                logger.error(f"Error checking control queue in should_continue: {str(e)}")
                # Return True by default on check_control_queue error to prevent unintended stopping
                return True
            
            # Return False if should_stop flag is set
            if hasattr(self, 'should_stop') and self.should_stop:
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking if scraper should continue: {str(e)}")
            # Return True by default to prevent unintended stopping due to errors
            return True
    
    def pause(self) -> bool:
        """Pause the scraper.
        
        Returns:
            bool: True if the scraper paused successfully, False otherwise
        """
        try:
            if not self.is_running:
                logger.warning(f"Scraper {self.name} is not running")
                return False
            
            # Send pause signal to control queue
            send_control_command(self.name, "pause")
            
            logger.info(f"Scraper {self.name} paused successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing scraper {self.name}: {str(e)}")
            return False
    
    def resume(self) -> bool:
        """Resume the scraper.
        
        Returns:
            bool: True if the scraper resumed successfully, False otherwise
        """
        try:
            if not self.is_running:
                logger.warning(f"Scraper {self.name} is not running")
                return False
            
            # Send resume signal to control queue
            send_control_command(self.name, "resume")
            
            logger.info(f"Scraper {self.name} resumed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error resuming scraper {self.name}: {str(e)}")
            return False
    
    def check_control_queue(self) -> None:
        """Check the control queue for commands."""
        try:
            command = check_control_queue(self.name)
            if command and "command" in command:
                if command["command"] == "stop":
                    self.should_stop = True
                    self.is_running = False
                elif command["command"] == "pause":
                    while True:
                        # Check for resume command
                        resume_command = check_control_queue(self.name)
                        if resume_command and resume_command.get("command") == "resume":
                            break
                        time.sleep(1)
        except Exception as e:
            logger.error(f"Error checking control queue: {str(e)}")
    
    def get_queue_length(self, queue_name: str) -> int:
        """Get the length of a specific queue.
        
        Args:
            queue_name: Name of the queue
            
        Returns:
            Number of items in the queue
        """
        return get_queue_length(queue_name)
    
    def get_all_queue_lengths(self) -> Dict[str, int]:
        """Get the length of all queues.
        
        Returns:
            Dictionary with queue names as keys and their lengths as values
        """
        return get_all_queue_lengths(self.name)
    
    def add_to_url_queue(self, url: str) -> None:
        """Add a URL to the URL queue.
        
        Args:
            url: URL to add to the queue
        """
        add_to_url_queue(self.name, url)
    
    def add_to_details_queue(self, url: str, html: str, images: List[str] = None) -> None:
        """Add listing details to the details queue.
        
        Args:
            url: URL of the listing
            html: HTML content of the listing
            images: List of image URLs (URLs only, not processed yet)
        """
        # Store original image URLs without processing
        # Image downloading will happen after successful LLM processing
        add_to_details_queue(self.name, url, html, images or [])
    
    def process_images_for_imgproxy(self, image_urls: List[str]) -> List[str]:
        """Process images by downloading them and uploading to S3.
        
        Takes a list of image URLs, downloads them and uploads to S3,
        and returns a list of S3 URLs.
        
        Args:
            image_urls: List of original image URLs
            
        Returns:
            List of S3 URLs for the processed images
        """
        return S3ImageProcessor.process_images_for_s3(image_urls)
    
    def add_to_processed_queue(self, listing_data: Dict[str, Any]) -> None:
        """Add processed listing data to the processed queue.
        
        Args:
            listing_data: Processed listing data
        """
        add_to_processed_queue(self.name, listing_data)
    
    def log_activity(self, status: str, message: str = "", listings_added: int = 0) -> ScraperActivity:
        """Log scraper activity to database.
        
        Args:
            status: Status of the activity (started, running, success, error)
            message: Message describing the activity
            listings_added: Number of listings added
            
        Returns:
            The created ScraperActivity instance
        """
        try:
            # Create a new activity if we don't have one, otherwise update the existing one
            if not self.activity_id:
                activity = ScraperActivity(
                    scraper_name=self.name,
                    status=status,
                    message=message,
                    listings_added=listings_added,
                    start_time=datetime.utcnow()
                )
                self.db.add(activity)
                self.db.commit()
                self.db.refresh(activity)
                self.activity_id = activity.id
                return activity
            else:
                activity = self.db.query(ScraperActivity).get(self.activity_id)
                if activity:
                    activity.status = status
                    activity.message = message
                    activity.listings_added = listings_added
                    if status in ['success', 'error']:
                        activity.end_time = datetime.utcnow()
                    self.db.commit()
                    return activity
                
                # If activity not found, create a new one
                return self.log_activity(status, message, listings_added)
                
        except Exception as e:
            logger.error(f"Failed to log scraper activity: {e}")
            self.db.rollback()
            # Don't raise the exception, just log it
            return None
    
    def process_listing_with_llm(self, url: str, html: str, source_website: str, images: List[str] = None) -> Dict[str, Any]:
        """Process listing HTML using LLM to extract structured data.
        
        Args:
            url: URL of the listing
            html: HTML content of the listing
            source_website: Source website name
            images: List of image URLs
            
        Returns:
            Structured listing data
        """
        try:
            listing_data = process_listing_text(html, source_website, url, images or [])
            return listing_data
        except Exception as e:
            logger.error(f"Error processing listing with LLM: {str(e)}")
            return {}
    
    def save_listing(self, listing_data: Dict[str, Any]) -> bool:
        """Save a listing to the database.
        
        Args:
            listing_data: Dictionary containing listing information
            
        Returns:
            bool: True if the listing was saved successfully, False otherwise
        """
        try:
            url = listing_data.get("url")
            if not url:
                logger.error("Listing data has no URL, cannot save")
                return False
            
            db = SessionLocal()
            existing_listing = db.query(Listing).filter(Listing.url == url).first()
            
            clean_data = {}
            for key, value in listing_data.items():
                if key in ['id', 'scrape_date']:
                    continue
                if key == 'source_website':
                    clean_data['website_source'] = value
                else:
                    clean_data[key] = value
            
            # Scraper-generated listings are auto-approved
            clean_data['approval_status'] = 'approved'
            clean_data['is_user_generated'] = False
            
            if existing_listing:
                logger.info(f"Updating existing listing with URL: {url}")
                for key, value in clean_data.items():
                    if hasattr(existing_listing, key):
                        setattr(existing_listing, key, value)
                db.commit()
                logger.info(f"Successfully updated listing: {existing_listing.id}")
            else:
                logger.info(f"Creating new listing with URL: {url}")
                db_listing = Listing(**clean_data)
                db.add(db_listing)
                db.commit()
                db.refresh(db_listing)
                logger.info(f"Successfully saved new listing: {db_listing.id}")
            
            db.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving listing: {str(e)}")
            if 'db' in locals():
                db.rollback()
                db.close()
            return False
    
    @abstractmethod
    def collect_listing_urls(self) -> List[str]:
        """Collect listing URLs from the source website.
        
        Returns:
            List of listing URLs
        """
        pass
    
    @abstractmethod
    def extract_listing_html(self, url: str) -> str:
        """Extract HTML content from a listing URL.
        
        Args:
            url: URL of the listing
            
        Returns:
            HTML content of the listing
        """
        pass
    
    @abstractmethod
    def scrape(self) -> int:
        """Main scraping method to be implemented by subclasses.
        
        Returns:
            Number of listings processed
        """
        pass
