"""LLM service for property listing data extraction."""
import os
import json
import logging
from openai import OpenAI
from mirascope import llm
from app.services.scrapers.models import PropertyListing
import time
import redis
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
logger = logging.getLogger(__name__)

# Initialize Redis client with connection pool configuration
redis_client = redis.Redis.from_url(
    os.environ.get('REDIS_URL', 'redis://redis:6379/0'),
    max_connections=20,  # Limit connections in pool
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True
)

# Redis key for the LLM rate limit status
LLM_RATE_LIMIT_KEY = "llm_rate_limited"
LLM_RATE_LIMIT_UNTIL_KEY = "llm_rate_limited_until"
LLM_RATE_LIMIT_DEFAULT_PAUSE = 180  # Default pause time in seconds when rate limited (increased from 120)
LLM_RATE_LIMIT_MAX_PAUSE = 900  # Maximum pause time in seconds (15 minutes)

# Redis keys for LLM queue control
LLM_QUEUE_STATUS_KEY = "llm_queue_status"
LLM_QUEUE_CONTROL_KEY = "llm_queue_control"

def set_llm_queue_status(status: str) -> None:
    """Set the LLM queue status.
    
    Args:
        status: Status to set ('running', 'stopped', 'paused')
    """
    redis_client.set(LLM_QUEUE_STATUS_KEY, status)
    logger.info(f"LLM queue status set to: {status}")

def get_llm_queue_status() -> str:
    """Get the current LLM queue status.
    
    Returns:
        Current status ('running', 'stopped', 'paused') or 'running' as default
    """
    status = redis_client.get(LLM_QUEUE_STATUS_KEY)
    return status.decode('utf-8') if status else "running"

def should_llm_queue_continue() -> bool:
    """Check if the LLM queue should continue processing.
    
    Returns:
        True if LLM queue should continue, False otherwise
    """
    status = get_llm_queue_status()
    return status == "running"

def send_llm_queue_control_command(command: str) -> None:
    """Send a control command to the LLM queue.
    
    Args:
        command: Command to send ('start', 'stop', 'pause', 'resume')
    """
    command_data = {
        "command": command,
        "timestamp": time.time()
    }
    redis_client.rpush(LLM_QUEUE_CONTROL_KEY, json.dumps(command_data))
    logger.info(f"LLM queue control command sent: {command}")

def check_llm_queue_control() -> Optional[Dict[str, Any]]:
    """Check for LLM queue control commands.
    
    Returns:
        Command data dictionary or None if no commands
    """
    command_data = redis_client.lpop(LLM_QUEUE_CONTROL_KEY)
    if not command_data:
        return None
    
    try:
        return json.loads(command_data.decode('utf-8'))
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding LLM queue control command: {str(e)}")
        return None

# Load cities and districts data
def load_cities_data():
    """Load cities and districts data from JSON file."""
    try:
        cities_file_path = os.path.join(os.path.dirname(__file__), 'cities_and_districts_extracted.json')
        with open(cities_file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        logger.error(f"Error loading cities data: {str(e)}")
        return {}  # Return empty dict if file not found or other error

def is_rate_limited() -> bool:
    """Check if LLM processing is currently rate limited.
    
    Returns:
        True if rate limited, False otherwise
    """
    # Check if rate limit flag is set
    rate_limited = redis_client.get(LLM_RATE_LIMIT_KEY)
    if not rate_limited:
        return False
    
    # Check if we've passed the rate limit duration
    rate_limited_until = redis_client.get(LLM_RATE_LIMIT_UNTIL_KEY)
    if rate_limited_until:
        try:
            until_timestamp = float(rate_limited_until.decode('utf-8'))
            remaining_seconds = max(0, int(until_timestamp - time.time()))
            
            if remaining_seconds <= 0:
                # Rate limit has expired, clear the flags
                logger.info("Rate limit period has expired, clearing flags")
                redis_client.delete(LLM_RATE_LIMIT_KEY)
                redis_client.delete(LLM_RATE_LIMIT_UNTIL_KEY)
                return False
            else:
                # Still rate limited, log remaining time
                logger.info(f"LLM API still rate limited for {remaining_seconds} more seconds (until {datetime.fromtimestamp(until_timestamp).strftime('%H:%M:%S')})")
                return True
        except (ValueError, TypeError) as e:
            logger.error(f"Error parsing rate limit timestamp: {str(e)}")
    
    # Still rate limited
    return True

def set_rate_limited(duration_seconds: int = LLM_RATE_LIMIT_DEFAULT_PAUSE) -> None:
    """Set the rate limit flag and expiration time.
    
    Args:
        duration_seconds: How long to pause LLM processing in seconds
    """
    # Calculate expiration timestamp
    expiration_time = time.time() + duration_seconds
    expiration_dt = datetime.fromtimestamp(expiration_time)
    
    # Check if there was already a rate limit in place
    was_already_limited = redis_client.exists(LLM_RATE_LIMIT_KEY)
    
    # Set rate limit flags in Redis
    redis_client.set(LLM_RATE_LIMIT_KEY, "1")
    redis_client.set(LLM_RATE_LIMIT_UNTIL_KEY, str(expiration_time))
    
    # Log the rate limiting with more details
    if was_already_limited:
        logger.warning(f"LLM API rate limit extended - pausing requests for {duration_seconds} seconds until {expiration_dt.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        logger.warning(f"LLM API rate limited - pausing requests for {duration_seconds} seconds until {expiration_dt.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Log more user-friendly time information if duration is long
    if duration_seconds >= 300:  # 5 minutes or more
        minutes = duration_seconds // 60
        seconds = duration_seconds % 60
        logger.warning(f"⚠️ Long rate limit pause: {minutes} minutes and {seconds} seconds. Consider reducing request frequency or increasing pause between tasks.")

@llm.call(
    provider="google", 
    model="gemini-2.0-flash", 
    response_model=PropertyListing,
    json_mode=True,
)
def extract_listing_info(text: str, source_website: str = "", url: str = "") -> str:
    """Extract property listing information from text using Mistral LLM.
    
    Args:
        text: The text content of the listing page
        source_website: The source website name
        url: The URL of the listing
        city_district_info: JSON string with city and district information
        
    Returns:
        Structured property listing data
    """

    
    return f"""As an expert real estate data extractor, your goal is to populate the `PropertyListing` schema. All extracted text values MUST be in French.

        1.  **Extract Core Vitals**:
            * You MUST extract and populate the mandatory fields: `url`, `title`, `price`, `size_sqm`, `city`, `property_type`, `property_category`, and `description_html`.
            * You can optionally add extra context about the size of the property depending on its type. This should be information that would clarify to the user what this surface area might represent to avoid misleading them.
            * There are only certain combinations of property_type and property_category that are valid. You MUST ensure that the combination is valid. This is dependent on context of the listing.
            * The property_type and property_category must be consistent with the listing's text. "Maison" is always of property_category "Résidential".

        2.  **Extract All Other Details into `additional_info`**:
            * Identify every other piece of information in the listing. For each piece of information, create a JSON object with a "name" and a "value" and add it to the `additional_info` list.

        3.  **Use Standardized Names for Common Features**:
            * When you encounter the following common features, you **MUST** use the exact `name` provided below. This is crucial for consistency.
                * Number of rooms: `\"Pièces\"`
                * Number of bedrooms: `\"Chambres\"`
                * Number of bathrooms/showers: `\"Salles de bain\"`
                * Floor number: `\"Étage\"`
                * Total floors in building: `\"Étages totaux\"`
                * Building age or construction year: `\"Année de construction\"`
                * Parking details: `\"Parking\"` (value can be "Oui", "Titré", etc.)
                * Presence of an elevator: `\"Ascenseur\"` (value should be "Oui" or "Non")
                * Presence of a balcony: `\"Balcon\"`
                * Presence of a terrace: `\"Terrasse\"`
                * Presence of a garden: `\"Jardin\"`
                * Presence of a pool: `\"Piscine\"`
                * Property orientation: `\"Orientation\"`
                * If the property is furnished: `\"Meublé\"`
                * Surface : `\"Surface\"'
            * For any feature **NOT** on this list, use a descriptive name based on the listing's text.

        4.  **Add Context with `notes_for_user`**:
            * After extracting a feature's `name` and `value`, consider if there is any ambiguity or important context in the source text that a user should know about.
            * If so, add that context to the `notes_for_user` field.
            * **Use this for**:
                * **Ambiguities**
                * **Conditions**
                * **Warnings**
            * If there is no ambiguity, you can omit the `notes_for_user` field or leave it as null/empty.
            * This note should be reliant on the description and common problems and confusions that the end user might be exposed to that a real estate agent would already be aware of.
        5. **Extract the city and neighborhood from the listing content**:
            * You MUST extract the city and neighborhood from the listing content.
            * The city and neighborhood must be one of the predefined values.
        URL: {url}
        Source website: {source_website}
        Listing content: 
        {text}
"""


def process_listing_text(text: str, source_website: str, url: str, images: List[str]) -> PropertyListing:
    """Process listing text to extract structured data.
    
    Args:
        text: The text content of the listing page
        source_website: The source website name
        url: The URL of the listing
        images: List of image URLs
        
    Returns:
        PropertyListing: Structured property listing data
        
    Raises:
        Exception: If required fields are missing and the listing data is invalid (handled by Pydantic)
        Exception: If the API is rate limited (should be caught and retried later)
    """
    # First, check if we're currently rate limited
    if is_rate_limited():
        rate_limited_until = redis_client.get(LLM_RATE_LIMIT_UNTIL_KEY)
        wait_time = "unknown"
        if rate_limited_until:
            try:
                until_timestamp = float(rate_limited_until.decode('utf-8'))
                wait_time = f"{int(until_timestamp - time.time())} seconds"
            except ValueError:
                pass
        error_msg = f"LLM API currently rate limited. Try again after {wait_time}."
        logger.warning(error_msg)
        raise RuntimeError(error_msg)


    listing_data = extract_listing_info(text, source_website, url)
    # Set essential properties
    listing_data.url = url
    listing_data.scraper = source_website
    listing_data.images = images if images else []
    
    # Set transaction type based on scraper name
    if 'rent' in source_website.lower():
        listing_data.transaction_type = 'rent'
    else:
        listing_data.transaction_type = 'sell'
    
    # Pydantic will automatically validate required fields
    # If any required field is missing, it will raise a ValidationError
    
    logger.info(f"Processed listing data for {url}: title='{listing_data.title}', city='{listing_data.city}', neighborhood='{listing_data.neighborhood}', transaction_type='{listing_data.transaction_type}'")
    return listing_data
