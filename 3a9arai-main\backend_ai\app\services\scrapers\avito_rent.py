"""Avito Rent scraper implementation for rental listings."""
import logging
import json
import time
import requests
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from sqlalchemy.orm import Session
from app.services.scrapers.base_scraper import BaseScraper
from app.services.scrapers.redis_utils import (
    add_to_url_queue, add_to_details_queue, add_to_processed_queue,
    get_next_from_url_queue, get_queue_length, set_url_collection_completed,
    is_url_collection_completed, acquire_url_collection_lock, release_url_collection_lock,
    get_next_page_url, get_last_page, set_last_page, add_next_page_url
)

logger = logging.getLogger(__name__)

class AvitoRentScraper(BaseScraper):
    """Scraper for Avito real estate rental listings."""
    
    def __init__(self, db: Session):
        """Initialize the Avito Rent scraper.
        
        Args:
            db: Database session
        """
        super().__init__(db, "avito_rent")
        self.base_url = "https://www.avito.ma/fr/maroc/locations_immobilieres-%C3%A0_louer"
        self.current_page = 1
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
    def collect_listing_urls(self) -> List[str]:
        """Extract listing URLs from the search page.
        
        Returns:
            List of listing URLs
        """
        # Check if we've marked URL collection as completed
        if is_url_collection_completed(self.name):
            logger.info("URL collection has been marked as completed. No more URLs to collect.")
            return []
            
        # First check if the scraper should continue running
        if not self.should_continue():
            logger.info("Scraper is set to stop, aborting URL collection.")
            return []
            
        # Try to acquire lock
        success, lock_value = acquire_url_collection_lock(self.name)
        if not success:
            logger.info("Another worker is already collecting URLs")
            return []
            
        try:
            # Get the next page URL from the queue or use the default starting URL
            next_page_url = get_next_page_url(self.name)
            
            # Get the last processed page from Redis
            current_page = get_last_page(self.name)
            
            # If we have a next page URL from the queue, use it
            if next_page_url:
                page_url = next_page_url
                logger.info(f"Processing next page URL from queue: {page_url}")
            else:
                # Otherwise use the default URL with the current page number
                page_url = self.base_url if current_page == 1 else f"{self.base_url}?o={current_page}"
                logger.info(f"No next page URL in queue, using constructed URL: {page_url}")
            
            try:
                # Refresh lock to prevent expiration during long-running tasks
                success, _ = acquire_url_collection_lock(self.name)
                
                logger.info(f"Processing page {current_page}: {page_url}")
                
                # Make the request
                response = requests.get(page_url, headers=self.headers)
                response.raise_for_status()
                
                # Parse with BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Find the script tag with __NEXT_DATA__
                script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
                if not script_tag:
                    logger.error("Could not find __NEXT_DATA__ script tag")
                    return []
                    
                # Parse the JSON data
                json_data = json.loads(script_tag.string)
                
                # Extract listings from the JSON data
                ads = json_data.get('props', {}).get('pageProps', {}).get('initialReduxState', {}).get('ad', {}).get('search', {}).get('ads', [])
                
                if not ads:
                    logger.info(f"No ads found on page {current_page}")
                    # Mark URL collection as completed since no URLs were found
                    set_url_collection_completed(self.name)
                    # Update scraper status
                    from app.api.endpoints.scrapers import set_scraper_status
                    set_scraper_status(self.name, "url_collection_completed", 
                                      message=f"URL collection completed at page {current_page}",
                                      last_page=current_page)
                    return []
                
                # Add new URLs to the queue
                new_urls_count = 0
                for ad in ads:
                    if 'href' in ad:
                        url = ad['href']
                        if url not in self.processed_urls and url and len(url.strip()) > 0:
                            # Log the URL being added
                            logger.info(f"Adding URL to queue: {url}")
                            add_to_url_queue(self.name, url)
                            self.processed_urls.add(url)
                            new_urls_count += 1
                
                logger.info(f"Collected {new_urls_count} new URLs from page {current_page}")
                
                # Store the current page in Redis
                set_last_page(self.name, current_page)
                
                # Find and queue the next page URL
                self._queue_next_page_url(page_url, current_page)
                
                return [ad['href'] for ad in ads if 'href' in ad]
                
            except Exception as e:
                logger.error(f"Error collecting URLs from page {current_page}: {str(e)}")
                set_url_collection_completed(self.name)
                from app.api.endpoints.scrapers import set_scraper_status
                set_scraper_status(self.name, "url_collection_completed", 
                                  message=f"URL collection completed at page {current_page} (reached end of pagination)",
                                  last_page=current_page)
                return []
                
        finally:
            # Release the lock we acquired
            release_url_collection_lock(self.name, lock_value)
            
    def _queue_next_page_url(self, page_url: str, current_page: int) -> bool:
        """Find and queue the next page URL.
        
        Args:
            page_url: Current page URL
            current_page: Current page number
            
        Returns:
            True if next page URL was queued, False otherwise
        """
        try:
            # Check if the scraper should continue running
            if not self.should_continue():
                logger.info("Scraper is set to stop, not queueing next page URL")
                return False
            
            
            # Get the next page URL
            next_page_url = f"{self.base_url}?o={current_page + 1}"
            logger.info(f"Found next page URL: {next_page_url}")
            # Add the next page URL to a dedicated queue
            add_next_page_url(self.name, next_page_url)
            # Update the current page counter
            next_page = current_page + 1
            set_last_page(self.name, next_page)
            logger.info(f"Queued next page URL {next_page_url} for page {next_page}")
            return True
                
        except Exception as e:
            logger.info(f"No Next button found on page {current_page} - reached end of pagination")
            # Mark URL collection as completed
            set_url_collection_completed(self.name)
            # Update scraper status
            from app.api.endpoints.scrapers import set_scraper_status
            set_scraper_status(self.name, "url_collection_completed", 
                                message=f"URL collection completed at page {current_page} (reached end of pagination)",
                                last_page=current_page)
            return False
            
    def extract_listing_html(self, url: str) -> Optional[str]:
        """Extract the HTML content of a listing page.
        
        Args:
            url: URL of the listing
            
        Returns:
            HTML content as string or None if extraction failed
        """
        try:
            if not self.should_continue():
                return None
                
            logger.info(f"Extracting HTML from listing: {url}")
            
            # Make the request
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            # Get the page content
            page_source = response.text
            
            # Parse with BeautifulSoup to validate HTML
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Find the script tag with __NEXT_DATA__
            script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
            if not script_tag:
                logger.error("Could not find __NEXT_DATA__ script tag")
                return None
                
            return page_source
            
        except Exception as e:
            logger.error(f"Error extracting HTML from listing: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
            
    def _extract_images(self, json_data: Dict, is_immo_neuf: bool) -> List[str]:
        """Extract image URLs from JSON data."""
        try:
            if is_immo_neuf:
                images = (json_data.get('project_media', {})
                          .get('photos', []))
                return [img_dict['src'] for img_dict in images]
            else:
                images = (json_data.get('props', {})
                        .get('pageProps', {})
                        .get('componentProps', {})
                        .get('adInfo', {})
                        .get('ad', {})
                        .get('images', []))
            
                return [img_dict['paths']['fullHd'] for img_dict in images]
            
        except Exception as e:
            logger.error(f"Error extracting images: {e}")
            return []

    def extract_listing_details(self, url: str) -> Optional[Dict[str, Any]]:
        """Extract listing details from a listing page.
        
        Args:
            url: URL of the listing
            
        Returns:
            Dictionary containing listing details or None if extraction failed
        """
        try:
            if not self.should_continue():
                return None
                
            logger.info(f"Processing listing: {url}")
            
            # Make the request
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            # Get the page content
            page_source = response.text
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Find the script tag with __NEXT_DATA__
            script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
            if not script_tag:
                logger.error("Could not find __NEXT_DATA__ script tag")
                return None
                
            # Parse the JSON data
            json_data = json.loads(script_tag.string)
            
            # Extract ad info
            ad_info = json_data.get('props', {}).get('pageProps', {}).get('componentProps', {}).get('adInfo', {}).get('ad', {})
            
            if not ad_info:
                logger.error("Could not find ad info in JSON data")
                ad_info = (json_data.get('props', {})
                            .get('pageProps', {})
                            .get('dehydratedState', {})
                            .get('queries', {}))

                ad_info = ad_info[0].get('state', {}).get('data', {}).get('unit_details', {})

                if not ad_info:
                    logger.error("Could not find ad info in another location")
                    return None
                
            # Extract images
            images = self._extract_images(json_data, False)
            
            # Create details dictionary
            details = {
                "url": url,
                "html": page_source,
                "images": images,
                "property_details": ad_info
            }
            
            # Add to details queue
            add_to_details_queue(self.name, url, page_source, images, ad_info)
            
            return details
            
        except Exception as e:
            logger.error(f"Error extracting listing details: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
            
    def scrape(self) -> int:
        """Run the scraper.
        
        Returns:
            Number of listings processed
        """
        try:
            if not self.start():
                return 0
                
            processed_count = 0
            
            # First collect URLs
            urls = self.collect_listing_urls()
            logger.info(f"Collected {len(urls)} URLs")
            
            # Process URLs
            while self.should_continue():
                url = get_next_from_url_queue(self.name)
                if not url:
                    break
                    
                details = self.extract_listing_details(url)
                if details:
                    processed_count += 1
                    
            return processed_count
            
        except Exception as e:
            logger.error(f"Error in scraper: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return 0
            
        finally:
            self.stop()


# Legacy alias for backward compatibility
AvitoScraper = AvitoRentScraper 