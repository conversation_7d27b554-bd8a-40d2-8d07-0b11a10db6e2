#!/usr/bin/env python3
"""
Migration script to move all existing filesystem images to Digital Ocean Spaces (S3).

This script will:
1. Find all listings with filesystem image URLs
2. Upload existing images to S3
3. Update database records with new S3 URLs
4. Provide backup and rollback capabilities
5. Handle thumbnails automatically
6. Support dry-run mode for testing

Usage:
    python migrate_images_to_s3.py --dry-run          # Test run without changes
    python migrate_images_to_s3.py --migrate          # Perform actual migration
    python migrate_images_to_s3.py --rollback         # Rollback using backup
"""

import os
import sys
import json
import logging
import argparse
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional, Tuple

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from app.db.session import SessionLocal
    from app.models.listing import Listing
    from app.services.utils.s3_image_utils import S3ImageUploader, save_uploaded_image_to_s3
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this script from the backend directory with proper environment setup")
    sys.exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Migration configuration
MEDIA_DIR = Path('/app/media/property_images')
BACKUP_DIR = Path('/app/migration_backup')
BATCH_SIZE = 10  # Process images in batches


class ImageMigrator:
    """Main class for handling image migration from filesystem to S3."""
    
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.s3_uploader = None
        self.migration_stats = {
            'total_listings': 0,
            'listings_with_images': 0,
            'total_images': 0,
            'successfully_migrated': 0,
            'failed_migrations': 0,
            'skipped_images': 0
        }
        self.migration_backup = {}
        
        # Initialize S3 uploader if not in dry-run mode
        if not dry_run:
            try:
                self.s3_uploader = S3ImageUploader()
                # Test S3 connection
                if not self.s3_uploader.check_bucket_exists():
                    raise Exception("Cannot access S3 bucket. Check your credentials.")
                logger.info("✅ S3 connection verified")
            except Exception as e:
                logger.error(f"❌ S3 setup failed: {e}")
                raise
    
    def create_backup(self) -> str:
        """Create a backup of current database state."""
        backup_file = BACKUP_DIR / f"db_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        BACKUP_DIR.mkdir(exist_ok=True)
        
        logger.info("Creating database backup...")
        
        db = SessionLocal()
        try:
            all_listings = db.query(Listing).all()
            backup_data = {}
            
            for listing in all_listings:
                if listing.images and any('/media/property_images/' in str(img) for img in listing.images):
                    backup_data[listing.id] = {
                        'title': listing.title,
                        'images': listing.images.copy() if listing.images else []
                    }
            
            with open(backup_file, 'w') as f:
                json.dump(backup_data, f, indent=2)
            
            logger.info(f"✅ Backup created: {backup_file}")
            logger.info(f"   Backed up {len(backup_data)} listings with filesystem images")
            
            return str(backup_file)
            
        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")
            raise
        finally:
            db.close()
    
    def find_filesystem_images(self) -> List[Tuple[int, str, List[str]]]:
        """Find all listings with filesystem image URLs."""
        logger.info("Scanning database for filesystem images...")
        
        db = SessionLocal()
        try:
            all_listings = db.query(Listing).all()
            filesystem_listings = []
            
            for listing in all_listings:
                if not listing.images:
                    continue
                
                filesystem_images = []
                for img in listing.images:
                    if isinstance(img, str) and '/media/property_images/' in img:
                        filesystem_images.append(img)
                
                if filesystem_images:
                    filesystem_listings.append((listing.id, listing.title, filesystem_images))
            
            logger.info(f"Found {len(filesystem_listings)} listings with filesystem images")
            return filesystem_listings
            
        finally:
            db.close()
    
    def get_local_image_path(self, image_url: str) -> Optional[Path]:
        """Convert image URL to local filesystem path."""
        if '/media/property_images/' not in image_url:
            return None
        
        # Extract relative path from URL
        # URL format: http://localhost:8000/media/property_images/filename.jpg
        url_parts = image_url.split('/media/property_images/')
        if len(url_parts) != 2:
            return None
        
        relative_path = url_parts[1]
        local_path = MEDIA_DIR / relative_path
        
        return local_path if local_path.exists() else None
    
    def upload_image_to_s3(self, local_path: Path) -> Optional[Dict[str, str]]:
        """Upload a local image file to S3."""
        try:
            # Read the image file
            with open(local_path, 'rb') as f:
                image_content = f.read()
            
            # Use our existing S3 upload function
            # This will automatically create thumbnails
            result = save_uploaded_image_to_s3(
                image_content, 
                local_path.name, 
                user_id=None  # No user-specific directory for migrated images
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to upload {local_path}: {e}")
            return None
    
    def migrate_listing_images(self, listing_id: int, title: str, filesystem_images: List[str]) -> bool:
        """Migrate all images for a single listing."""
        logger.info(f"Migrating listing {listing_id}: {title[:50]}...")
        
        new_images = []
        migration_successful = True
        
        for img_url in filesystem_images:
            self.migration_stats['total_images'] += 1
            
            # Get local file path
            local_path = self.get_local_image_path(img_url)
            if not local_path:
                logger.warning(f"  ⚠️  Local file not found for: {img_url}")
                self.migration_stats['skipped_images'] += 1
                new_images.append(img_url)  # Keep original URL
                continue
            
            if self.dry_run:
                logger.info(f"  [DRY RUN] Would upload: {local_path}")
                # In dry run, simulate S3 URL
                new_images.append(f"https://your-bucket.region.digitaloceanspaces.com/property_images/{local_path.name}")
            else:
                # Upload to S3
                s3_result = self.upload_image_to_s3(local_path)
                
                if s3_result and s3_result.get('original'):
                    logger.info(f"  ✅ Uploaded: {local_path.name} -> {s3_result['original']}")
                    new_images.append(s3_result['original'])
                    self.migration_stats['successfully_migrated'] += 1
                else:
                    logger.error(f"  ❌ Failed to upload: {local_path}")
                    migration_successful = False
                    self.migration_stats['failed_migrations'] += 1
                    new_images.append(img_url)  # Keep original URL as fallback
        
        # Update database if not dry run and migration was successful
        if not self.dry_run and new_images:
            try:
                db = SessionLocal()
                listing = db.query(Listing).filter(Listing.id == listing_id).first()
                if listing:
                    # Store backup data
                    self.migration_backup[listing_id] = listing.images.copy() if listing.images else []
                    
                    # Update with new S3 URLs
                    listing.images = new_images
                    db.commit()
                    logger.info(f"  ✅ Database updated for listing {listing_id}")
                    # delete local image
                    os.remove(local_path)
                else:
                    logger.error(f"  ❌ Listing {listing_id} not found in database")
                    migration_successful = False
                db.close()
            except Exception as e:
                logger.error(f"  ❌ Database update failed for listing {listing_id}: {e}")
                migration_successful = False
        
        return migration_successful
    
    def run_migration(self) -> bool:
        """Run the complete migration process."""
        logger.info("🚀 Starting image migration to S3")
        
        if self.dry_run:
            logger.info("🔍 DRY RUN MODE - No actual changes will be made")
        
        try:
            # Create backup (even in dry run for testing)
            backup_file = self.create_backup()
            
            # Find all filesystem images
            filesystem_listings = self.find_filesystem_images()
            self.migration_stats['total_listings'] = len(filesystem_listings)
            self.migration_stats['listings_with_images'] = len(filesystem_listings)
            
            if not filesystem_listings:
                logger.info("✅ No filesystem images found. Migration not needed.")
                return True
            
            logger.info(f"Found {len(filesystem_listings)} listings to migrate")
            
            # Process listings in batches
            for i, (listing_id, title, images) in enumerate(filesystem_listings, 1):
                logger.info(f"\n--- Processing {i}/{len(filesystem_listings)} ---")
                
                success = self.migrate_listing_images(listing_id, title, images)
                
                if not success:
                    logger.warning(f"⚠️  Migration had issues for listing {listing_id}")
                
                # Progress update
                if i % BATCH_SIZE == 0:
                    self.print_progress()
            
            # Final statistics
            self.print_final_stats()
            
            if not self.dry_run:
                # Save migration backup data
                backup_data_file = BACKUP_DIR / f"migration_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(backup_data_file, 'w') as f:
                    json.dump(self.migration_backup, f, indent=2)
                logger.info(f"Migration data saved to: {backup_data_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")
            return False
    
    def print_progress(self):
        """Print current migration progress."""
        stats = self.migration_stats
        total = stats['total_images']
        if total > 0:
            success_rate = (stats['successfully_migrated'] / total) * 100
            logger.info(f"Progress: {stats['successfully_migrated']}/{total} images ({success_rate:.1f}% success)")
    
    def print_final_stats(self):
        """Print final migration statistics."""
        stats = self.migration_stats
        logger.info("\n" + "="*60)
        logger.info("MIGRATION SUMMARY")
        logger.info("="*60)
        logger.info(f"Total listings processed: {stats['total_listings']}")
        logger.info(f"Listings with images: {stats['listings_with_images']}")
        logger.info(f"Total images found: {stats['total_images']}")
        logger.info(f"Successfully migrated: {stats['successfully_migrated']}")
        logger.info(f"Failed migrations: {stats['failed_migrations']}")
        logger.info(f"Skipped images: {stats['skipped_images']}")
        
        if stats['total_images'] > 0:
            success_rate = (stats['successfully_migrated'] / stats['total_images']) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")
        
        logger.info("="*60)


def rollback_migration(backup_file: str) -> bool:
    """Rollback migration using backup file."""
    logger.info(f"🔄 Starting rollback from: {backup_file}")
    
    try:
        with open(backup_file, 'r') as f:
            backup_data = json.load(f)
        
        db = SessionLocal()
        rollback_count = 0
        
        for listing_id, data in backup_data.items():
            listing = db.query(Listing).filter(Listing.id == int(listing_id)).first()
            if listing:
                listing.images = data['images']
                rollback_count += 1
                logger.info(f"Rolled back listing {listing_id}: {data['title'][:50]}")
        
        db.commit()
        db.close()
        
        logger.info(f"✅ Rollback completed. Restored {rollback_count} listings")
        return True
        
    except Exception as e:
        logger.error(f"❌ Rollback failed: {e}")
        return False


def main():
    """Main function with command line argument handling."""
    parser = argparse.ArgumentParser(description='Migrate images from filesystem to S3')
    parser.add_argument('--dry-run', action='store_true', help='Test run without making changes')
    parser.add_argument('--migrate', action='store_true', help='Perform actual migration')
    parser.add_argument('--rollback', type=str, help='Rollback using backup file')
    parser.add_argument('--check-s3', action='store_true', help='Test S3 connection only')
    
    args = parser.parse_args()
    
    if args.check_s3:
        try:
            uploader = S3ImageUploader()
            if uploader.check_bucket_exists():
                logger.info("✅ S3 connection successful")
            else:
                logger.error("❌ Cannot access S3 bucket")
        except Exception as e:
            logger.error(f"❌ S3 connection failed: {e}")
        return
    
    if args.rollback:
        if not Path(args.rollback).exists():
            logger.error(f"Backup file not found: {args.rollback}")
            return
        rollback_migration(args.rollback)
        return
    
    if not args.dry_run and not args.migrate:
        logger.error("Please specify --dry-run or --migrate")
        parser.print_help()
        return
    
    # Environment check
    required_env_vars = [
        'DO_SPACES_ACCESS_KEY',
        'DO_SPACES_SECRET_KEY',
        'DO_SPACES_BUCKET_NAME'
    ]
    
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    if missing_vars and not args.dry_run:
        logger.error(f"Missing environment variables: {', '.join(missing_vars)}")
        return
    
    # Run migration
    migrator = ImageMigrator(dry_run=args.dry_run)
    success = migrator.run_migration()
    
    if success:
        if args.dry_run:
            logger.info("\n🎉 Dry run completed successfully!")
            logger.info("Run with --migrate to perform actual migration")
        else:
            logger.info("\n🎉 Migration completed successfully!")
            logger.info("Your images are now stored in Digital Ocean Spaces")
    else:
        logger.error("\n❌ Migration failed. Check logs for details")
        sys.exit(1)


if __name__ == "__main__":
    main() 