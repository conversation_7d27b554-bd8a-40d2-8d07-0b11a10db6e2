"""
API dependencies for dependency injection.

This module contains various dependencies that can be used across API endpoints.
"""
from typing import Generator, List, Dict, Any, Optional, Union
from fastapi import Depends, HTTPException, status, Header
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTT<PERSON><PERSON>earer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session
import requests
import json
import logging
import re

from app import crud, models, schemas
from app.core import security
from app.core.config import settings
from app.db.session import SessionLocal
from app.crud.crud_listing_view import listing_view

logger = logging.getLogger(__name__)

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)

# Clerk JWT Bearer for authentication
clerk_bearer = HTTPBearer()

def get_db() -> Generator[Session, None, None]:
    """
    Get database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def fetch_clerk_user_metadata(clerk_user_id: str) -> Dict[str, Any]:
    """
    Fetch user metadata from Clerk API.
    """
    try:
        if not hasattr(settings, 'CLERK_SECRET_KEY') or not settings.CLERK_SECRET_KEY:
            logger.warning("CLERK_SECRET_KEY not configured, skipping metadata fetch")
            return {}
        
        clerk_api_url = f"https://api.clerk.dev/v1/users/{clerk_user_id}"
        headers = {
            "Authorization": f"Bearer {settings.CLERK_SECRET_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(clerk_api_url, headers=headers, timeout=5)
        
        if response.status_code == 200:
            user_data = response.json()
            return user_data.get("public_metadata", {})
        else:
            logger.warning(f"Failed to fetch Clerk user metadata: {response.status_code}")
            return {}
            
    except requests.RequestException as e:
        logger.warning(f"Error fetching Clerk user metadata: {e}")
        return {}
    except Exception as e:
        logger.error(f"Unexpected error fetching Clerk metadata: {e}")
        return {}

async def sync_user_admin_status(user: models.User, clerk_metadata: Dict[str, Any], db: Session) -> None:
    """
    Sync admin status from Clerk metadata to database.
    """
    try:
        is_admin_clerk = clerk_metadata.get("isAdmin", False)
        
        # Update database admin status if it differs
        if user.is_superuser != is_admin_clerk:
            user.is_superuser = is_admin_clerk
            db.commit()
            logger.info(f"Updated admin status for user {user.email}: is_admin={is_admin_clerk}")
            
    except Exception as e:
        logger.error(f"Error syncing admin status for user {user.email}: {e}")
        db.rollback()

async def get_current_user_from_clerk(
    authorization: str = Depends(clerk_bearer),
    db: Session = Depends(get_db)
) -> models.User:
    """
    Get current user from Clerk JWT token and sync admin status.
    """
    try:
        # Extract token from authorization header
        token = authorization.credentials
        logger.info(f"Received token of length: {len(token) if token else 0}")
        
        if not token:
            logger.error("No token provided in authorization header")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No token provided"
            )
        
        # For development, we'll decode the JWT without verification
        # In production, you should verify the JWT with Clerk's public key
        payload = jwt.get_unverified_claims(token)
        logger.info(f"JWT payload keys: {list(payload.keys()) if payload else 'None'}")
        
        clerk_user_id = payload.get("sub")
        logger.info(f"Extracted clerk_user_id: {clerk_user_id}")
        
        if not clerk_user_id:
            logger.error("No user ID found in JWT token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token: no user ID found"
            )
        
        # Get user from database
        user = db.query(models.User).filter(models.User.clerk_user_id == clerk_user_id).first()
        logger.info(f"Found user in database: {user.email if user else 'None'}")
        
        if not user:
            # Auto-create user if they don't exist
            # Try to fetch metadata from Clerk to get admin status
            clerk_metadata = await fetch_clerk_user_metadata(clerk_user_id)
            is_admin = clerk_metadata.get("isAdmin", False)
            
            # Extract email from JWT payload
            email = payload.get("email")
            if not email:
                # Fallback email if not in JWT
                email = f"user-{clerk_user_id}@clerk.local"
            
            user = models.User(
                clerk_user_id=clerk_user_id,
                email=email,
                full_name=payload.get("name", "Clerk User"),
                is_active=True,
                is_superuser=is_admin  # Set admin status from Clerk
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info(f"Created new user {user.email} with admin status: {is_admin}")
        else:
            # For existing users, try to sync admin status from Clerk
            # Only if Clerk secret key is configured
            if hasattr(settings, 'CLERK_SECRET_KEY') and settings.CLERK_SECRET_KEY:
                clerk_metadata = await fetch_clerk_user_metadata(clerk_user_id)
                await sync_user_admin_status(user, clerk_metadata, db)
            else:
                logger.debug("Clerk secret key not configured, skipping admin status sync")
        
        if not user.is_active:
            logger.error(f"User {user.email} is inactive")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Inactive user"
            )
        
        logger.info(f"Authentication successful for user: {user.email}, admin: {user.is_superuser}")
        return user
        
    except jwt.JWTError as e:
        logger.error(f"JWT decode error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token format"
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Unexpected authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )

async def get_current_admin_user(
    current_user: models.User = Depends(get_current_user_from_clerk)
) -> models.User:
    """
    Get current user and verify they have admin privileges.
    """
    print(current_user)
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin privileges required"
        )
    return current_user

async def get_current_user_optional(
    authorization: Optional[str] = Header(None, alias="authorization"),
    db: Session = Depends(get_db)
) -> Optional[models.User]:
    """
    Get current user from Clerk JWT token, returns None if not authenticated.
    This is for endpoints that should work with or without authentication.
    """
    if not authorization or not authorization.startswith("Bearer "):
        return None
    
    try:
        # Extract token from authorization header
        token = authorization.replace("Bearer ", "")
        
        if not token:
            return None
        
        # Decode JWT without verification (for development)
        payload = jwt.get_unverified_claims(token)
        
        clerk_user_id = payload.get("sub")
        if not clerk_user_id:
            return None
        
        # Get user from database
        user = db.query(models.User).filter(models.User.clerk_user_id == clerk_user_id).first()
        
        if not user or not user.is_active:
            return None
        
        return user
        
    except Exception as e:
        logger.debug(f"Optional authentication failed: {e}")
        return None

def prepare_listing_for_response(listing: models.Listing, current_user: Optional[models.User] = None) -> Dict[str, Any]:
    """Clean up listing data for response and extract fields from additional_info
    
    Args:
        listing: SQL Alchemy Listing model
        
    Returns:
        Dict with cleaned up data and extracted fields from additional_info
    """
    # Handle SQLAlchemy model
    if hasattr(listing, '__dict__'):
        listing_dict = listing.__dict__.copy()
        
        # Filter out None values from images and extract URLs from image objects
        if 'images' in listing_dict and listing_dict['images']:
            normalized_images = []
            for img in listing_dict['images']:
                if img is None:
                    continue
                elif isinstance(img, dict):
                    # Handle S3 migration format with 'original' key
                    if 'original' in img:
                        normalized_images.append(img['original'])
                    # Handle old format with 'paths' key
                    elif 'paths' in img and 'standard' in img['paths']:
                        normalized_images.append(img['paths']['standard'])
                    # Skip if dict doesn't have expected keys
                elif isinstance(img, str):
                    # Already a URL string, keep as-is
                    normalized_images.append(img)
            listing_dict['images'] = normalized_images
        
        # Extract fields from additional_info using standardized names from LLM service
        if 'additional_info' in listing_dict and listing_dict['additional_info']:
            additional_info = listing_dict['additional_info']
            
            # Create a lookup dictionary for quick access to additional info by name
            info_lookup = {}
            if isinstance(additional_info, list):
                for info in additional_info:
                    if isinstance(info, dict) and 'name' in info and 'value' in info:
                        info_lookup[info['name']] = info['value']
            
            # Extract fields using exact standardized names from LLM service prompt
            standardized_mappings = {
                'rooms': 'Pièces',
                'bedrooms': 'Chambres', 
                'bathrooms': 'Salles de bain',
                'floor': 'Étage',
                'total_floors': 'Étages totaux',
                'building_age': 'Année de construction',
                'elevator': 'Ascenseur',
                'has_balcony': 'Balcon',
                'has_terrace': 'Terrasse',
                'has_garden': 'Jardin',
                'has_pool': 'Piscine',
                'orientation': 'Orientation',
                'is_furnished': 'Meublé',
                'surface_info': 'Surface'
            }
            
            # Extract values based on standardized mappings
            for field, standard_name in standardized_mappings.items():
                if standard_name in info_lookup:
                    value = info_lookup[standard_name]
                    
                    # Convert boolean strings to boolean for specific fields
                    if field in ['elevator', 'has_balcony', 'has_terrace', 'has_garden', 'has_pool', 'is_furnished']:
                        if isinstance(value, str):
                            if value.lower() in ['oui', 'yes', 'true', '1']:
                                listing_dict[field] = True
                            elif value.lower() in ['non', 'no', 'false', '0']:
                                listing_dict[field] = False
                            # If it's not a clear boolean, skip setting the field
                    
                    # Convert numeric strings to numbers for specific fields
                    elif field in ['rooms', 'bedrooms', 'bathrooms', 'floor', 'total_floors']:
                        if isinstance(value, (int, float)):
                            listing_dict[field] = int(value)
                        elif isinstance(value, str):
                            # Try to extract number from string, but only if it's clearly numeric
                            try:
                                # Check if the string contains only digits (possibly with spaces)
                                clean_value = value.strip().replace(' ', '')
                                if clean_value.isdigit():
                                    listing_dict[field] = int(clean_value)
                                # If it's not purely numeric, don't set the field
                                # This avoids setting notes like "Inclut un double salon" as room count
                            except (ValueError, TypeError):
                                pass  # Skip if conversion fails
                    
                    # For other fields, set the value as-is (strings, etc.)
                    elif field in ['building_age', 'orientation', 'surface_info']:
                        if isinstance(value, str) and value.strip():
                            listing_dict[field] = value.strip()
        
        # Ensure backward compatibility fields
        if 'size' not in listing_dict or listing_dict['size'] is None:
            listing_dict['size'] = listing_dict.get('size_sqm')
        
        if 'description' not in listing_dict or listing_dict['description'] is None:
            # Strip HTML tags from description_html for backward compatibility
            description_html = listing_dict.get('description_html', '')
            if description_html:
                listing_dict['description'] = re.sub('<[^<]+?>', '', description_html)
        
        # Ensure website_source is mapped from scraper if needed
        if 'website_source' not in listing_dict or listing_dict['website_source'] is None:
            listing_dict['website_source'] = listing_dict.get('source_website') or listing_dict.get('scraper')
        
        # Add user name for user-generated listings
        if listing_dict.get('is_user_generated') and hasattr(listing, 'owner') and listing.owner:
            listing_dict['owner_name'] = listing.owner.full_name or listing.owner.email or 'Utilisateur'
        
        return listing_dict
    
    # Handle dict
    if isinstance(listing, dict):
        listing_dict = listing.copy()
        if 'images' in listing_dict and listing_dict['images']:
            normalized_images = []
            for img in listing_dict['images']:
                if img is None:
                    continue
                elif isinstance(img, dict):
                    # Handle S3 migration format with 'original' key
                    if 'original' in img:
                        normalized_images.append(img['original'])
                    # Handle old format with 'paths' key
                    elif 'paths' in img and 'standard' in img['paths']:
                        normalized_images.append(img['paths']['standard'])
                    # Skip if dict doesn't have expected keys
                elif isinstance(img, str):
                    # Already a URL string, keep as-is
                    normalized_images.append(img)
            listing_dict['images'] = normalized_images
        return listing_dict
    
    # Handle other cases
    return listing

def prepare_listing_for_response_with_views(listing: models.Listing, db: Session) -> Dict[str, Any]:
    """Clean up listing data for response and include view counts
    
    Args:
        listing: SQL Alchemy Listing model
        db: Database session for view count queries
        
    Returns:
        Dict with cleaned up data, extracted fields from additional_info, and view counts
    """
    from app.crud.crud_listing_view import listing_view
    
    # Get the basic listing data
    listing_dict = prepare_listing_for_response(listing)
    
    # Add view count for the past 24 hours
    if hasattr(listing, 'id'):
        view_count = listing_view.get_view_count(db=db, listing_id=listing.id, hours=24)
        listing_dict['view_count_24h'] = view_count
    
    return listing_dict

def prepare_listing_for_response_with_bookmarks(
    listing: models.Listing, 
    db: Session, 
    current_user: Optional[models.User] = None
) -> Dict[str, Any]:
    """Clean up listing data for response and include bookmark status
    
    Args:
        listing: SQL Alchemy Listing model
        db: Database session for bookmark queries
        current_user: Current authenticated user (optional)
        
    Returns:
        Dict with cleaned up data, extracted fields from additional_info, and bookmark status
    """
    # Get the basic listing data
    listing_dict = prepare_listing_for_response(listing, current_user)
    
    # Add bookmark status if user is authenticated
    if current_user and hasattr(listing, 'id'):
        from app.crud.bookmark import bookmark
        try:
            is_bookmarked = bookmark.is_bookmarked(db=db, user_id=current_user.id, listing_id=listing.id)
            listing_dict['is_bookmarked'] = is_bookmarked
        except Exception as e:
            logger.debug(f"Could not determine bookmark status: {e}")
            listing_dict['is_bookmarked'] = False
    else:
        listing_dict['is_bookmarked'] = False
    
    return listing_dict

def prepare_listings_with_bookmarks_batch(
    listings: List[models.Listing], 
    db: Session, 
    current_user: Optional[models.User] = None
) -> List[Dict[str, Any]]:
    """Efficiently prepare multiple listings with bookmark status using batch queries
    
    Args:
        listings: List of SQL Alchemy Listing models
        db: Database session for bookmark queries
        current_user: Current authenticated user (optional)
        
    Returns:
        List of dicts with cleaned up data and bookmark status
    """
    # Prepare basic listing data
    prepared_listings = [prepare_listing_for_response(listing, current_user) for listing in listings]
    
    # Add bookmark status efficiently if user is authenticated
    if current_user and listings:
        from app.crud.bookmark import bookmark
        try:
            listing_ids = [listing.id for listing in listings if hasattr(listing, 'id')]
            if listing_ids:
                bookmark_statuses = bookmark.get_bookmark_statuses(
                    db=db, user_id=current_user.id, listing_ids=listing_ids
                )
                
                # Update prepared listings with bookmark status
                for i, listing in enumerate(listings):
                    if hasattr(listing, 'id') and listing.id in bookmark_statuses:
                        prepared_listings[i]['is_bookmarked'] = bookmark_statuses[listing.id]
                    else:
                        prepared_listings[i]['is_bookmarked'] = False
            else:
                # No valid listing IDs, set all to False
                for prepared_listing in prepared_listings:
                    prepared_listing['is_bookmarked'] = False
        except Exception as e:
            logger.debug(f"Could not determine bookmark statuses: {e}")
            # Set all to False on error
            for prepared_listing in prepared_listings:
                prepared_listing['is_bookmarked'] = False
    else:
        # No user or no listings, set all to False
        for prepared_listing in prepared_listings:
            prepared_listing['is_bookmarked'] = False
    
    return prepared_listings


def prepare_listings_with_views_batch(
    listings: List[models.Listing], 
    db: Session
) -> List[Dict[str, Any]]:
    """Efficiently prepare multiple listings with view counts using batch queries
    
    Args:
        listings: List of SQL Alchemy Listing models
        db: Database session for view count queries
        
    Returns:
        List of dicts with cleaned up data and view counts
    """
    from app.crud.crud_listing_view import listing_view
    
    # Prepare basic listing data
    prepared_listings = [prepare_listing_for_response(listing) for listing in listings]
    
    # Add view counts efficiently if we have listings
    if listings:
        try:
            listing_ids = [listing.id for listing in listings if hasattr(listing, 'id')]
            if listing_ids:
                view_counts = listing_view.get_view_counts_batch(
                    db=db, listing_ids=listing_ids, hours=24
                )
                
                # Update prepared listings with view counts
                for i, listing in enumerate(listings):
                    if hasattr(listing, 'id') and listing.id in view_counts:
                        prepared_listings[i]['view_count_24h'] = view_counts[listing.id]
                    else:
                        prepared_listings[i]['view_count_24h'] = 0
            else:
                # No valid listing IDs, set all to 0
                for prepared_listing in prepared_listings:
                    prepared_listing['view_count_24h'] = 0
        except Exception as e:
            logger.debug(f"Could not determine view counts: {e}")
            # Set all to 0 on error
            for prepared_listing in prepared_listings:
                prepared_listing['view_count_24h'] = 0
    else:
        # No listings, nothing to do
        pass
    
    return prepared_listings


def prepare_listings_with_views_and_bookmarks_batch(
    listings: List[models.Listing], 
    db: Session, 
    current_user: Optional[models.User] = None
) -> List[Dict[str, Any]]:
    """Efficiently prepare multiple listings with view counts and bookmark status using batch queries
    
    Args:
        listings: List of SQL Alchemy Listing models
        db: Database session for queries
        current_user: Current authenticated user (optional)
        
    Returns:
        List of dicts with cleaned up data, view counts, and bookmark status
    """
    from app.crud.crud_listing_view import listing_view
    
    # Prepare basic listing data
    prepared_listings = [prepare_listing_for_response(listing, current_user) for listing in listings]
    
    if listings:
        listing_ids = [listing.id for listing in listings if hasattr(listing, 'id')]
        
        if listing_ids:
            # Add view counts efficiently
            try:
                view_counts = listing_view.get_view_counts_batch(
                    db=db, listing_ids=listing_ids, hours=24
                )
                
                # Update prepared listings with view counts
                for i, listing in enumerate(listings):
                    if hasattr(listing, 'id') and listing.id in view_counts:
                        prepared_listings[i]['view_count_24h'] = view_counts[listing.id]
                    else:
                        prepared_listings[i]['view_count_24h'] = 0
            except Exception as e:
                logger.debug(f"Could not determine view counts: {e}")
                # Set all to 0 on error
                for prepared_listing in prepared_listings:
                    prepared_listing['view_count_24h'] = 0
            
            # Add bookmark status efficiently if user is authenticated
            if current_user:
                try:
                    from app.crud.bookmark import bookmark
                    bookmark_statuses = bookmark.get_bookmark_statuses(
                        db=db, user_id=current_user.id, listing_ids=listing_ids
                    )
                    
                    # Update prepared listings with bookmark status
                    for i, listing in enumerate(listings):
                        if hasattr(listing, 'id') and listing.id in bookmark_statuses:
                            prepared_listings[i]['is_bookmarked'] = bookmark_statuses[listing.id]
                        else:
                            prepared_listings[i]['is_bookmarked'] = False
                except Exception as e:
                    logger.debug(f"Could not determine bookmark statuses: {e}")
                    # Set all to False on error
                    for prepared_listing in prepared_listings:
                        prepared_listing['is_bookmarked'] = False
            else:
                # No user, set all to False
                for prepared_listing in prepared_listings:
                    prepared_listing['is_bookmarked'] = False
        else:
            # No valid listing IDs, set defaults
            for prepared_listing in prepared_listings:
                prepared_listing['view_count_24h'] = 0
                prepared_listing['is_bookmarked'] = False
    
    return prepared_listings 