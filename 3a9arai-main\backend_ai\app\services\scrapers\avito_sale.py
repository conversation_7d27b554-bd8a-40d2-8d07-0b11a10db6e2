"""Avito Sale scraper implementation for real estate listings."""
import logging
import json
import time
import requests
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from sqlalchemy.orm import Session
from app.services.scrapers.base_scraper import BaseScraper
from app.services.scrapers.redis_utils import (
    add_to_url_queue, add_to_details_queue, add_to_processed_queue,
    get_next_from_url_queue, get_queue_length, set_url_collection_completed,
    is_url_collection_completed, acquire_url_collection_lock, release_url_collection_lock,
    get_next_page_url, get_last_page, set_last_page, add_next_page_url
)

logger = logging.getLogger(__name__)


class AvitoSaleScraper(BaseScraper):
    """Scraper for Avito real estate sale listings."""
    
    BASE_URL = "https://www.avito.ma/fr/maroc/ventes_immobilieres-%C3%A0_vendre"
    HEADERS = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    def __init__(self, db: Session):
        """Initialize the Avito Sale scraper.
        
        Args:
            db: Database session
        """
        super().__init__(db, "avito_sale")
        self.current_page = 1
        
    def collect_listing_urls(self) -> List[str]:
        """Extract listing URLs from the search page.
        
        Returns:
            List of listing URLs
        """
        if self._is_collection_completed():
            return []
            
        if not self.should_continue():
            logger.info("Scraper is set to stop, aborting URL collection.")
            return []
            
        return self._collect_with_lock()
    
    def _is_collection_completed(self) -> bool:
        """Check if URL collection has been completed."""
        if is_url_collection_completed(self.name):
            logger.info("URL collection has been marked as completed. No more URLs to collect.")
            return True
        return False
    
    def _collect_with_lock(self) -> List[str]:
        """Collect URLs with distributed lock protection."""
        success, lock_value = acquire_url_collection_lock(self.name)
        if not success:
            logger.info("Another worker is already collecting URLs")
            return []
            
        try:
            return self._process_page()
        finally:
            release_url_collection_lock(self.name, lock_value)
    
    def _process_page(self) -> List[str]:
        """Process a single page and extract URLs."""
        page_url = self._get_page_url()
        current_page = get_last_page(self.name)
        
        try:
            # Refresh lock to prevent expiration
            acquire_url_collection_lock(self.name)
            
            logger.info(f"Processing page {current_page}: {page_url}")
            return self._extract_urls_from_page(page_url, current_page)
            
        except Exception as e:
            logger.error(f"Error collecting URLs from page {current_page}: {str(e)}")
            if "500" in str(e):
                #add the page to the queue
                add_to_url_queue(self.name, page_url)
                logger.info(f"Added page {page_url} to queue")
            else:
                logger.error(f"Error collecting URLs from page {current_page}: {str(e)}")
                self._mark_collection_completed(current_page, "Error occurred during collection")
            return []
    
    def _get_page_url(self) -> str:
        """Get the URL for the current page to process."""
        next_page_url = get_next_page_url(self.name)
        current_page = get_last_page(self.name)
        
        if next_page_url:
            logger.info(f"Processing next page URL from queue: {next_page_url}")
            return next_page_url
        else:
            page_url = self.BASE_URL if current_page == 1 else f"{self.BASE_URL}?o={current_page}"
            logger.info(f"No next page URL in queue, using constructed URL: {page_url}")
            return page_url
    
    def _extract_urls_from_page(self, page_url: str, current_page: int) -> List[str]:
        """Extract URLs from a specific page."""
        response = requests.get(page_url, headers=self.HEADERS)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        json_data = self._extract_json_data(soup)
        
        if not json_data:
            return []
        
        ads = self._get_ads_from_json(json_data)
        
        if not ads:
            logger.info(f"No ads found on page {current_page}")
            self._mark_collection_completed(current_page, f"No ads found on page {current_page}")
            return []
        
        return self._process_ads(ads, current_page, page_url)
    
    def _extract_json_data(self, soup: BeautifulSoup) -> Optional[Dict]:
        """Extract JSON data from the page."""
        script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
        if not script_tag:
            logger.error("Could not find __NEXT_DATA__ script tag")
            return None
        
        try:
            return json.loads(script_tag.string)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON data: {e}")
            return None
    
    def _get_ads_from_json(self, json_data: Dict) -> List[Dict]:
        """Extract ads array from JSON data."""
        return (json_data.get('props', {})
                .get('pageProps', {})
                .get('initialReduxState', {})
                .get('ad', {})
                .get('search', {})
                .get('ads', []))
    
    def _process_ads(self, ads: List[Dict], current_page: int, page_url: str) -> List[str]:
        """Process ads and add URLs to queue."""
        new_urls_count = 0
        urls = []
        
        for ad in ads:
            if 'href' in ad:
                url = ad['href']
                if self._is_valid_new_url(url):
                    logger.info(f"Adding URL to queue: {url}")
                    add_to_url_queue(self.name, url)
                    self.processed_urls.add(url)
                    new_urls_count += 1
                    urls.append(url)
        
        logger.info(f"Collected {new_urls_count} new URLs from page {current_page}")
        
        # Update page tracking and queue next page
        set_last_page(self.name, current_page)
        self._queue_next_page_url(page_url, current_page)
        
        return urls
    
    def _is_valid_new_url(self, url: str) -> bool:
        """Check if URL is valid and not already processed."""
        return (url not in self.processed_urls and 
                url and 
                len(url.strip()) > 0)
    
    def _mark_collection_completed(self, page: int, message: str):
        """Mark URL collection as completed and update status."""
        set_url_collection_completed(self.name)
        from app.api.endpoints.scrapers import set_scraper_status
        set_scraper_status(self.name, "url_collection_completed", 
                          message=message, last_page=page)
    
    def _queue_next_page_url(self, page_url: str, current_page: int) -> bool:
        """Find and queue the next page URL.
        
        Args:
            page_url: Current page URL
            current_page: Current page number
            
        Returns:
            True if next page URL was queued, False otherwise
        """
        if not self.should_continue():
            logger.info("Scraper is set to stop, not queueing next page URL")
            return False
        
        try:
            next_page_url = f"{self.BASE_URL}?o={current_page + 1}"
            next_page = current_page + 1
            
            logger.info(f"Found next page URL: {next_page_url}")
            add_next_page_url(self.name, next_page_url)
            set_last_page(self.name, next_page)
            logger.info(f"Queued next page URL {next_page_url} for page {next_page}")
            return True
            
        except Exception as e:
            logger.info(f"No Next button found on page {current_page} - reached end of pagination")
            self._mark_collection_completed(current_page, 
                                          f"URL collection completed at page {current_page} (reached end of pagination)")
            return False
    
    def extract_listing_html(self, url: str) -> Optional[str]:
        """Extract the HTML content of a listing page.
        
        Args:
            url: URL of the listing
            
        Returns:
            HTML content as string or None if extraction failed
        """
        if not self.should_continue():
            return None
            
        try:
            logger.info(f"Extracting HTML from listing: {url}")
            
            response = requests.get(url, headers=self.HEADERS)
            response.raise_for_status()
            
            page_source = response.text
            
            # Validate HTML structure
            if not self._validate_html_structure(page_source):
                return None
                
            return page_source
            
        except Exception as e:
            logger.error(f"Error extracting HTML from listing: {str(e)}")
            return None
    
    def _validate_html_structure(self, html: str) -> bool:
        """Validate that the HTML contains required structure."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
            
            if not script_tag:
                logger.error("Could not find __NEXT_DATA__ script tag")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error validating HTML structure: {e}")
            return False
    
    def extract_listing_details(self, url: str) -> Optional[Dict[str, Any]]:
        """Extract listing details from a listing page.
        
        Args:
            url: URL of the listing
            
        Returns:
            Dictionary containing listing details or None if extraction failed
        """
        if not self.should_continue():
            return None
            
        try:
            logger.info(f"Processing listing: {url}")
            
            page_source = self._fetch_page_content(url)
            if not page_source:
                return None
            
            json_data = self._extract_json_data_from_html(page_source)
            if not json_data:
                return None
            
            ad_info, is_immo_neuf = self._extract_ad_info(json_data)
            if not ad_info:
                return None
            
            images = self._extract_images(json_data, is_immo_neuf)
            
            details = self._build_listing_details(url, page_source, images, ad_info)
            
            # Add to details queue
            add_to_details_queue(self.name, url, page_source, images, ad_info)
            
            return details
            
        except Exception as e:
            logger.error(f"Error extracting listing details: {str(e)}")
            return None
    
    def _fetch_page_content(self, url: str) -> Optional[str]:
        """Fetch page content from URL."""
        try:
            response = requests.get(url, headers=self.HEADERS)
            response.raise_for_status()
            return response.text
        except Exception as e:
            logger.error(f"Error fetching page content: {e}")
            return None
    
    def _extract_json_data_from_html(self, html: str) -> Optional[Dict]:
        """Extract JSON data from HTML content."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            return self._extract_json_data(soup)
        except Exception as e:
            logger.error(f"Error extracting JSON from HTML: {e}")
            return None
    
    def _extract_ad_info(self, json_data: Dict) -> Optional[Dict]:
        """Extract ad information from JSON data."""
        ad_info = (json_data.get('props', {})
                  .get('pageProps', {})
                  .get('componentProps', {})
                  .get('adInfo', {})
                  .get('ad', {}))
        if ad_info:
            return ad_info, False
        else:
            logger.info("Could not find ad info in JSON data")
            logger.info("Trying to find ad info in another location")
            ad_info = (json_data.get('props', {})
                        .get('pageProps', {})
                        .get('dehydratedState', {})
                        .get('queries', {}))

            ad_info = ad_info[0].get('state', {}).get('data', {}).get('unit_details', {})
            if ad_info:
                return ad_info, True
            else:
                logger.info("Could not find ad info in another location")
                return None, False
    
    def _extract_images(self, json_data: Dict, is_immo_neuf: bool) -> List[str]:
        """Extract image URLs from JSON data."""
        try:
            if is_immo_neuf:
                images = (json_data.get('project_media', {})
                          .get('photos', []))
                return [img_dict['src'] for img_dict in images]
            else:
                images = (json_data.get('props', {})
                        .get('pageProps', {})
                        .get('componentProps', {})
                        .get('adInfo', {})
                        .get('ad', {})
                        .get('images', []))
            
                return [img_dict['paths']['fullHd'] for img_dict in images]
            
        except Exception as e:
            logger.error(f"Error extracting images: {e}")
            return []
    
    def _build_listing_details(self, url: str, html: str, images: List[str], ad_info: Dict) -> Dict[str, Any]:
        """Build the listing details dictionary."""
        return {
            "url": url,
            "html": ad_info,
            "images": images,
            "property_details": ad_info
        }
    
    def scrape(self) -> int:
        """Run the scraper.
        
        Returns:
            Number of listings processed
        """
        try:
            if not self.start():
                return 0
                
            processed_count = 0
            
            # First collect URLs
            urls = self.collect_listing_urls()
            logger.info(f"Collected {len(urls)} URLs")
            
            # Process URLs from queue
            processed_count = self._process_url_queue()
            
            return processed_count
            
        except Exception as e:
            logger.error(f"Error in scraper: {str(e)}")
            return 0
            
        finally:
            self.stop()
    
    def _process_url_queue(self) -> int:
        """Process URLs from the queue."""
        processed_count = 0
        
        while self.should_continue():
            url = get_next_from_url_queue(self.name)
            if not url:
                break
                
            details = self.extract_listing_details(url)
            if details:
                processed_count += 1
                
        return processed_count


# Legacy alias for backward compatibility
class AvitoScraper(AvitoSaleScraper):
    """Legacy alias for AvitoSaleScraper."""
    
    def __init__(self, db: Session):
        super().__init__(db)
        # Override name to maintain compatibility
        self.name = "avito"