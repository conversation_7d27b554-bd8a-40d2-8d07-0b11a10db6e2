CATEGORY_OPTIONS = [
    "appartements",
    "maisons",
    "villas_riad",
    "bureaux_et_plateaux",
    "magasins_et_commerces",
    "terrains_et_fermes",
    "autres_immobilier",
]
EXCLUDED_LISTINGS_KEYWORDS = [
    "&has_image=true",
    "&has_price=true",
    "magazine.avito.ma",
    "immoneuf.avito.ma",
    "avito.ma/sp/immobilier",
]
EXCLUDED_LISTINGS_EXACT_MATCH = [
    "https://www.avito.ma/fr/maroc/appartements",
    "https://www.avito.ma/sp/immobilier/appartements-a-vendre-casablanca",
]


def validate_search_params(search_params):
    # Category validation:
    # parameter value should be one of the values in CATEGORY_OPTIONS
    if search_params.get("category") not in CATEGORY_OPTIONS:
        raise ValueError("Invalid category")

    # Filter Validation:
    # allowed values are integers, numeric strings or empty strings
    for key, value in search_params["filters"].items():
        if value:
            try:
                int(value)
            except ValueError:
                raise ValueError(f"Invalid filters: {key} must be an integer")

    return search_params


def filter_listings(listings: list) -> list:
    listings = [
        listing
        for listing in listings
        if not any(excluded == listing for excluded in EXCLUDED_LISTINGS_EXACT_MATCH)
    ]
    return [
        listing
        for listing in listings
        if not any(excluded in listing for excluded in EXCLUDED_LISTINGS_KEYWORDS)
    ]


def safe_string(value: str) -> str:
    return value if value else ""


import undetected_chromedriver as uc
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException as NSEE


class AvitoScraper:
    def __init__(self, search_options, headless=False):
        self.driver = uc.Chrome(headless=headless, use_subprocess=False)
        self.webdriverwait = WebDriverWait(self.driver, 60)
        self.search_options = {
            "city": search_options.get("city", "casablanca"),
            "category": search_options.get(
                "category", "appartements"
            ),  # options: appartements, maisons, villas_riad, bureaux_et_plateaux, magasins_et_commerces, terrains_et_fermes, autres_immobilier
            "filters": {
                "min_price": safe_string(search_options.get("min_price", None)),
                "max_price": safe_string(search_options.get("max_price", None)),
                "min_surface": safe_string(search_options.get("min_surface", None)),
                "max_surface": safe_string(search_options.get("max_surface", None)),
            },
        }
        self.search_options = validate_search_params(self.search_options)
        self.search_url = self.generate_url()

    def generate_url(self):
        initial_url = f"https://www.avito.ma/fr/{self.search_options['city']}/{self.search_options['category']}-à_vendre"

        if all(value == "" for value in self.search_options["filters"].values()):
            return initial_url
        else:
            url = initial_url + "?"
            params = [
                f"size={self.search_options['filters']['min_surface']}-{self.search_options['filters']['max_surface']}",
                f"price={self.search_options['filters']['min_price']}-{self.search_options['filters']['max_price']}",
                "has_price=true",
                "has_image=true",
            ]

            return url + "&".join(params)

    def get(self, url):
        self.driver.get(url)
        try:
            if self.driver.find_element(
                By.XPATH, "//h3[contains(text(), 'HTTP null')]"
            ):
                self.get(url)
        except NSEE:
            pass

    def get_number_of_pages(self):
        pagination_links = self.webdriverwait.until(
            EC.presence_of_all_elements_located(
                (By.XPATH, "//a[contains(@href, '?o=')]")
            )
        )
        last_page = pagination_links[-2].text
        return int(last_page)

    def get_listings(self):
        listings_container = self.webdriverwait.until(
            EC.presence_of_element_located((By.CLASS_NAME, "listing"))
        )
        listings = listings_container.find_elements(
            By.XPATH, f"//a[contains(@href, '{self.search_options['category']}')]"
        )  # Excludes immoneuf listings
        listings = [listing.get_attribute("href") for listing in listings]
        return listings

    def scrape_listing(self, url):
        print(url)
        self.get(url)
        listing = {}

        listing["avito_id"] = url.split("_")[-1].split(".")[0]
        # TODO: Calculate datePoste
        listing["datePoste"] = datetime.now().strftime("%Y-%m-%d")

        try:
            listing["titre"] = self.webdriverwait.until(
                EC.presence_of_element_located((By.TAG_NAME, "h1"))
            ).text
        except NSEE:
            listing["titre"] = None

        listing["type"] = self.search_options["category"]

        try:
            listing["price"] = (
                self.webdriverwait.until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//p[@font-weight='bold']")
                    )
                )
                .text.replace("DH", "")
                .strip()
                .encode("ascii", "ignore")
            )
            listing["price"] = int(listing["price"])
        except NSEE:
            listing["price"] = None

        listing["ville"] = self.search_options["city"]

        try:
            listing["secteur"] = self.driver.find_element(
                By.XPATH, "//span[contains(text(), 'Secteur')]/following-sibling::span"
            ).text
        except NSEE:
            listing["secteur"] = None

        try:
            listing["surfaceHabitable"] = self.driver.find_element(
                By.XPATH,
                "//span[contains(text(), 'Surface habitable')]/following-sibling::span",
            ).text
        except NSEE:
            listing["surfaceHabitable"] = None

        try:
            listing["etage"] = self.driver.find_element(
                By.XPATH, "//span[contains(text(), 'Étage')]/following-sibling::span"
            ).text
        except NSEE:
            listing["etage"] = None

        try:
            listing["chambre"] = self.driver.find_element(
                By.XPATH,
                "//*[@aria-labelledby='ChambresTitleID']/parent::div/following-sibling::span",
            ).text
        except NSEE:
            listing["chambre"] = None

        try:
            listing["salon"] = self.driver.find_element(
                By.XPATH, "//span[contains(text(), 'Salons')]/following-sibling::span"
            ).text
        except NSEE:
            listing["salon"] = None

        try:
            listing["salleDeBain"] = self.driver.find_element(
                By.XPATH,
                "//*[@aria-labelledby='SalleDeBainTitleID']/parent::div/following-sibling::span",
            ).text
        except NSEE:
            listing["salleDeBain"] = None

        try:
            listing["age"] = self.driver.find_element(
                By.XPATH,
                "//span[contains(text(), 'Âge du bien')]/following-sibling::span",
            ).text
        except NSEE:
            listing["age"] = None

        try:
            listing["equipment"] = self.driver.find_element(
                By.XPATH, "//h2[contains(text(), 'équipements')]/following-sibling::div"
            ).text.replace("\n", ",")
        except NSEE:
            listing["equipment"] = None

        try:
            listing["fraisSyndic"] = self.driver.find_element(
                By.XPATH,
                "//h2[contains(text(), 'Frais de syndic')]/following-sibling::div",
            ).text
        except NSEE:
            listing["fraisSyndic"] = None
        try:
            listing["description"] = self.driver.find_element(
                By.XPATH, "//h2[contains(text(), 'Description')]/following-sibling::p"
            ).text.replace("\n", ",")
        except NSEE:
            listing["description"] = None

        try:
            listing["nomVendeur"] = self.driver.find_element(
                By.XPATH, "//p[@data-testid='SellerName']"
            ).text
        except NSEE:
            listing["nomVendeur"] = None

        try:
            listing["telephone"] = self.driver.find_element(
                By.XPATH, "//a[contains(@href, 'tel:')]"
            )
        except NSEE:
            listing["telephone"] = None

        listing["lien"] = url
        listing["dateCollecte"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return listing


import sqlite3


def setup_database(db_name="avito_listings.db"):
    conn = sqlite3.connect(db_name, check_same_thread=False)
    cursor = conn.cursor()
    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS listings (
            avito_id INTEGER,
            datePoste TEXT,
            titre TEXT,
            type TEXT,
            price INTEGER,
            ville TEXT,
            secteur TEXT,
            surfaceHabitable INTEGER,
            etage INTEGER,
            chambre INTEGER,
            salon INTEGER,
            salleDeBain INTEGER,
            age TEXT,
            equipement TEXT,
            fraisSyndic INTEGER,
            description TEXT,
            nomVendeur TEXT,
            telephone TEXT,
            lien TEXT,
            dateCollecte TEXT
        )
        """
    )
    conn.commit()
    return conn


def insert_listing(conn, listing):
    if listing is None:
        return None
    cursor = conn.cursor()
    cursor.execute(
        """
        INSERT INTO listings VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
        """,
        tuple(listing.values()),
    )
    conn.commit()


from time import sleep


def main():
    conn = setup_database()

    search_options = {
        "city": "",
        "category": "appartements",
        "min_price": "",
        "max_price": "",
        "min_surface": "",
        "max_surface": "",
    }
    scraper = AvitoScraper(search_options=search_options)
    scraper.get(scraper.search_url)

    num_pages = scraper.get_number_of_pages()
    listings = scraper.get_listings()

    for i in range(2, num_pages + 1):
        print(f"Scraping page {i} of {num_pages}")
        scraper.get(f"{scraper.search_url}&o={i}")
        listings.extend(scraper.get_listings())

    listings = filter_listings(listings)

    for listing in listings:
        result = scraper.scrape_listing(listing)
        print(result)
        insert_listing(conn, result)
        sleep(1)  # avoid rate limiting

    conn.close()
    scraper.driver.quit()


if __name__ == "__main__":
    main()
