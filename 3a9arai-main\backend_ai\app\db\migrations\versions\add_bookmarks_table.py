"""Add bookmarks table

Revision ID: add_bookmarks_table
Revises: init_db_tables
Create Date: 2023-10-08 12:34:56.789012

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_bookmarks_table'
down_revision = 'init_db_tables'  # This should match your most recent migration
branch_labels = None
depends_on = None


def upgrade():
    # Create bookmarks table
    op.create_table(
        'bookmarks',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('listing_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('now()')),
        sa.ForeignKeyConstraint(['listing_id'], ['listings.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'listing_id', name='uq_user_listing_bookmark')
    )
    
    # Add index for faster lookup
    op.create_index(
        op.f('ix_bookmarks_user_id'),
        'bookmarks',
        ['user_id'],
        unique=False
    )
    op.create_index(
        op.f('ix_bookmarks_listing_id'),
        'bookmarks',
        ['listing_id'],
        unique=False
    )


def downgrade():
    # Drop the bookmarks table
    op.drop_index(op.f('ix_bookmarks_listing_id'), table_name='bookmarks')
    op.drop_index(op.f('ix_bookmarks_user_id'), table_name='bookmarks')
    op.drop_table('bookmarks') 