from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_, text
from datetime import datetime, timedelta

from app.crud.base import CRUDBase
from app.models.listing_view import ListingView
from app.models.listing import Listing, ApprovalStatus
from app.schemas.listing_view import ListingViewCreate, ListingViewUpdate
from app.services.view_cache import view_cache


class CRUDListingView(CRUDBase[ListingView, ListingViewCreate, ListingViewUpdate]):
    def track_view(
        self, db: Session, *, listing_id: int, ip_address: str, user_agent: Optional[str] = None
    ) -> Optional[ListingView]:
        """
        Track a listing view with deduplication based on IP address and listing.
        Only creates a new view if the same IP hasn't viewed this listing in the last hour.
        Uses Redis cache for deduplication.
        """
        # Check Redis cache for deduplication first
        should_track = view_cache.should_track_view(listing_id, ip_address)
        
        if not should_track:
            # View already tracked recently, return None to indicate no new tracking
            return None
        
        # Check database for existing view in the last hour (fallback)
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        existing_view = db.query(ListingView).filter(
            ListingView.listing_id == listing_id,
            ListingView.ip_address == ip_address,
            ListingView.viewed_at > one_hour_ago
        ).first()
        
        if existing_view:
            # Update the timestamp of existing view
            existing_view.viewed_at = datetime.utcnow()
            db.commit()
            db.refresh(existing_view)
            
            # Increment view count in cache
            view_cache.increment_view_count(listing_id, 24)
            # Invalidate popular listings cache since view counts changed
            view_cache.invalidate_popular_listings_cache()
            
            return existing_view
        else:
            # Create new view
            obj_in = ListingViewCreate(
                listing_id=listing_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            new_view = self.create(db=db, obj_in=obj_in)
            
            # Increment view count in cache
            view_cache.increment_view_count(listing_id, 24)
            # Invalidate popular listings cache since view counts changed
            view_cache.invalidate_popular_listings_cache()
            
            return new_view
    
    def get_view_count(self, db: Session, *, listing_id: int, hours: int = 24) -> int:
        """
        Get the number of unique views for a listing in the specified time period.
        Uses Redis cache for performance.
        """
        # Check cache first
        cached_count = view_cache.get_view_count(listing_id, hours)
        if cached_count is not None:
            return cached_count
        
        # Cache miss - get from database
        time_ago = datetime.utcnow() - timedelta(hours=hours)
        
        # Count unique IP addresses that viewed this listing in the time period
        count = db.query(func.count(func.distinct(ListingView.ip_address))).filter(
            ListingView.listing_id == listing_id,
            ListingView.viewed_at > time_ago
        ).scalar()
        
        count = count or 0
        
        # Cache the result
        view_cache.set_view_count(listing_id, count, hours)
        
        return count
    
    def get_most_viewed_listings(
        self, db: Session, *, hours: int = 24, limit: int = 10
    ) -> List[tuple]:
        """
        Get the most viewed listings in the specified time period.
        Returns list of tuples: (listing_id, view_count)
        """
        time_ago = datetime.utcnow() - timedelta(hours=hours)
        
        # Query to get listing IDs with their view counts
        results = db.query(
            ListingView.listing_id,
            func.count(func.distinct(ListingView.ip_address)).label('view_count')
        ).filter(
            ListingView.viewed_at > time_ago
        ).group_by(
            ListingView.listing_id
        ).order_by(
            desc('view_count')
        ).limit(limit).all()
        
        return [(listing_id, view_count) for listing_id, view_count in results]
    
    def get_popular_listings(
        self, db: Session, *, hours: int = 24, limit: int = 10
    ) -> List[Listing]:
        """
        Get the most popular listings (with actual Listing objects) in the specified time period.
        Uses Redis cache for performance.
        """
        # Check cache first
        cached_listing_ids = view_cache.get_popular_listings(hours, limit)
        if cached_listing_ids is not None:
            # Get listings by IDs preserving order (only approved listings)
            if cached_listing_ids:
                listings_dict = {listing.id: listing for listing in 
                               db.query(Listing).filter(
                                   Listing.id.in_(cached_listing_ids),
                                   Listing.approval_status == ApprovalStatus.approved
                               ).all()}
                return [listings_dict[listing_id] for listing_id in cached_listing_ids 
                       if listing_id in listings_dict]
            return []
        
        # Cache miss - get from database
        time_ago = datetime.utcnow() - timedelta(hours=hours)
        
        # Subquery to get view counts
        view_counts = db.query(
            ListingView.listing_id,
            func.count(func.distinct(ListingView.ip_address)).label('view_count')
        ).filter(
            ListingView.viewed_at > time_ago
        ).group_by(
            ListingView.listing_id
        ).subquery()
        
        # Join with listings and order by view count (only approved listings)
        results = db.query(Listing).join(
            view_counts, Listing.id == view_counts.c.listing_id
        ).filter(
            Listing.approval_status == ApprovalStatus.approved
        ).order_by(
            desc(view_counts.c.view_count)
        ).limit(limit).all()
        
        # Cache the listing IDs
        listing_ids = [listing.id for listing in results]
        view_cache.set_popular_listings(listing_ids, hours, limit)
        
        return results
    
    def cleanup_old_views(self, db: Session, *, days: int = 30) -> int:
        """
        Clean up old view records to maintain database performance.
        Removes views older than specified days.
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        deleted_count = db.query(ListingView).filter(
            ListingView.viewed_at < cutoff_date
        ).count()
        
        db.query(ListingView).filter(
            ListingView.viewed_at < cutoff_date
        ).delete()
        
        db.commit()
        return deleted_count
    
    def get_view_counts_batch(
        self, db: Session, *, listing_ids: List[int], hours: int = 24
    ) -> Dict[int, int]:
        """
        Get view counts for multiple listings efficiently using Redis batch operations.
        
        Args:
            db: Database session
            listing_ids: List of listing IDs to get counts for
            hours: Time period for counting views
            
        Returns:
            Dictionary mapping listing_id -> view_count
        """
        if not listing_ids:
            return {}
        
        # Get cached view counts first
        cached_counts = view_cache.get_view_counts_batch(listing_ids, hours)
        
        # Find which listings need database queries
        missing_ids = [listing_id for listing_id in listing_ids 
                      if listing_id not in cached_counts]
        
        if missing_ids:
            # Get missing counts from database
            time_ago = datetime.utcnow() - timedelta(hours=hours)
            
            db_results = db.query(
                ListingView.listing_id,
                func.count(func.distinct(ListingView.ip_address)).label('view_count')
            ).filter(
                ListingView.listing_id.in_(missing_ids),
                ListingView.viewed_at > time_ago
            ).group_by(
                ListingView.listing_id
            ).all()
            
            # Convert to dictionary and cache the results
            db_counts = {listing_id: count for listing_id, count in db_results}
            
            # Add zero counts for listings with no views
            for listing_id in missing_ids:
                if listing_id not in db_counts:
                    db_counts[listing_id] = 0
            
            # Cache the database results
            view_cache.set_view_counts_batch(db_counts, hours)
            
            # Merge with cached results
            cached_counts.update(db_counts)
        
        # Ensure all requested listing IDs are in the result with default 0
        result = {}
        for listing_id in listing_ids:
            result[listing_id] = cached_counts.get(listing_id, 0)
        
        return result


listing_view = CRUDListingView(ListingView) 