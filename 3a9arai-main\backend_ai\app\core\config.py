import os
from typing import List
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def get_cors_origins():
    """Get CORS origins from environment variable."""
    origins_str = os.getenv("CORS_ORIGINS", "http://localhost:5173,http://frontend:5173")
    return origins_str.split(",")

class Settings(BaseSettings):
    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "WebScraperTracker"
    
    # CORS settings
    CORS_ORIGINS: str = os.getenv("CORS_ORIGINS", "http://localhost:5173,http://frontend:5173")
    
    # Database settings
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "db")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "webscraper")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5432")
    
    # SQLAlchemy database URL
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "supersecretkey")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30  # 30 days
    
    # Clerk settings
    CLERK_SECRET_KEY: str = os.getenv("CLERK_SECRET_KEY", "")
    CLERK_WEBHOOK_SECRET: str = os.getenv("CLERK_WEBHOOK_SECRET", "")
    
    # Admin settings
    ADMIN_USERNAME: str = os.getenv("ADMIN_USERNAME", "admin")
    ADMIN_PASSWORD: str = os.getenv("ADMIN_PASSWORD", "admin123")
    
    # User registration settings
    USERS_OPEN_REGISTRATION: bool = os.getenv("USERS_OPEN_REGISTRATION", "True").lower() == "true"
    
    # ScrapegraphAI settings
    SCRAPEGRAPH_API_KEY: str = os.getenv("SCRAPEGRAPH_API_KEY", "KDwRcIkjb8H9a8KbpcZcvDUniCvYlcW1")
    
    # Default roles and permissions
    DEFAULT_USER_ROLE: str = "user"
    DEFAULT_USER_PERMISSIONS: List[str] = [
        "user:read",
        "listings:read",
        "scrapers:read"
    ]
    
    # Admin permissions include all permissions
    ADMIN_PERMISSIONS: List[str] = [
        "user:read",
        "user:write",
        "admin",
        "listings:read",
        "listings:write",
        "scrapers:read",
        "scrapers:execute",
    ]
    
    class Config:
        case_sensitive = True

# Create settings instance
settings = Settings() 