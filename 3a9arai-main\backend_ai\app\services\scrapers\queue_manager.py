"""Queue management service for automatic scraper pause/resume based on LLM queue load."""
import logging
import redis
import os
import time
from typing import Dict, List, Optional, Tuple
from app.api.endpoints.scrapers import LLM_QUEUE_REGISTRY, get_registered_llm_queue_stats

logger = logging.getLogger(__name__)

# Initialize Redis client
redis_client = redis.Redis.from_url(
    os.environ.get('REDIS_URL', 'redis://redis:6379/0'),
    max_connections=20,
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True
)

# Constants for queue management
MAX_QUEUE_SIZE = 3000  # Increased for production with 3200MB Redis - Maximum total items in LLM queues
RESUME_QUEUE_SIZE = 1500  # Resume threshold when queue size drops below this
CRITICAL_QUEUE_SIZE = 3500  # Emergency stop threshold - stop all scrapers immediately
MEMORY_CHECK_THRESHOLD = 2800  # MB - Start aggressive cleanup when Redis memory usage exceeds this

# Redis keys for queue management
SCRAPERS_PAUSED_FOR_QUEUE_KEY = "scrapers_paused_for_queue"
QUEUE_MANAGEMENT_STATUS_KEY = "queue_management_status"
LAST_QUEUE_CHECK_KEY = "last_queue_check"
REDIS_MEMORY_STATUS_KEY = "redis_memory_status"
SCRAPERS_AUTO_RESUME_KEY = "scrapers_auto_resume"  # Track scrapers that should auto-resume

class QueueLimitManager:
    """Manages automatic scraper pause/resume based on LLM queue load."""
    
    def __init__(self):
        self.active_scrapers = ["agenz", "sarouty", "avito_sale", "avito_rent"]
        
    def get_total_llm_queue_size(self) -> int:
        """Get the total number of items across all LLM queues."""
        try:
            _, total_items = get_registered_llm_queue_stats()
            return total_items
        except Exception as e:
            logger.error(f"Error getting LLM queue stats: {str(e)}")
            return 0
    
    def get_paused_scrapers(self) -> List[str]:
        """Get list of scrapers currently paused due to queue limits."""
        try:
            paused_data = redis_client.get(SCRAPERS_PAUSED_FOR_QUEUE_KEY)
            if paused_data:
                import json
                return json.loads(paused_data.decode('utf-8'))
            return []
        except Exception as e:
            logger.error(f"Error getting paused scrapers list: {str(e)}")
            return []
    
    def set_paused_scrapers(self, scrapers: List[str]) -> None:
        """Set list of scrapers paused due to queue limits."""
        try:
            import json
            redis_client.set(SCRAPERS_PAUSED_FOR_QUEUE_KEY, json.dumps(scrapers))
        except Exception as e:
            logger.error(f"Error setting paused scrapers list: {str(e)}")
    
    def is_scraper_running(self, scraper_name: str) -> bool:
        """Check if a scraper is currently running."""
        try:
            status_key = f"{scraper_name}_status"
            status = redis_client.get(status_key)
            return status and status.decode('utf-8') == "running"
        except Exception as e:
            logger.error(f"Error checking scraper status for {scraper_name}: {str(e)}")
            return False
    
    def pause_scraper_for_queue_limit(self, scraper_name: str) -> bool:
        """Pause a scraper due to queue limits."""
        try:
            from app.services.scrapers.redis_utils import send_control_command
            
            # Only pause if the scraper is currently running
            if not self.is_scraper_running(scraper_name):
                logger.info(f"Scraper {scraper_name} is not running, cannot pause")
                return False
            
            # Check if already paused
            paused_scrapers = self.get_paused_scrapers()
            if scraper_name in paused_scrapers:
                logger.info(f"Scraper {scraper_name} is already paused")
                return False
                
            # Send pause command
            logger.info(f"Sending pause command to scraper {scraper_name}")
            send_control_command(scraper_name, "pause")
            
            # Add to paused list
            paused_scrapers.append(scraper_name)
            self.set_paused_scrapers(paused_scrapers)
            
            # Add to auto-resume list so it will be restarted when conditions improve
            self.add_to_auto_resume(scraper_name)
            
            logger.info(f"Paused scraper {scraper_name} due to queue limit (queue size > {MAX_QUEUE_SIZE})")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing scraper {scraper_name} for queue limit: {str(e)}")
            return False
    
    def resume_scraper_from_queue_limit(self, scraper_name: str) -> bool:
        """Resume a scraper that was paused due to queue limits."""
        try:
            from app.services.scrapers.redis_utils import send_control_command
            
            # Check if the scraper was manually stopped vs paused for queue limits
            # We should resume scrapers that are in the paused list, but not those that were manually stopped
            paused_scrapers = self.get_paused_scrapers()
            auto_resume_scrapers = self.get_auto_resume_scrapers()
            
            # Check if scraper should be resumed (either in paused list or auto-resume list)
            should_resume = scraper_name in paused_scrapers or scraper_name in auto_resume_scrapers
            
            if not should_resume:
                logger.debug(f"Scraper {scraper_name} not in paused or auto-resume list, not resuming")
                return False
            
            # Check if scraper was manually stopped (status = "stopped")
            # If manually stopped, remove from both lists but don't resume
            status_key = f"{scraper_name}_status"
            status = redis_client.get(status_key)
            current_status = status.decode('utf-8') if status else "idle"
            
            logger.info(f"Attempting to resume scraper {scraper_name}, current status: {current_status}")
            
            if current_status == "stopped":
                # Scraper was manually stopped, remove from both lists but don't resume
                logger.info(f"Scraper {scraper_name} was manually stopped, removing from paused/auto-resume lists")
                if scraper_name in paused_scrapers:
                    paused_scrapers.remove(scraper_name)
                    self.set_paused_scrapers(paused_scrapers)
                self.remove_from_auto_resume(scraper_name)
                return False
            
            # Resume the scraper - send resume command and set status
            logger.info(f"Resuming scraper {scraper_name}")
            send_control_command(scraper_name, "resume")
            
            # Set status to running
            redis_client.set(status_key, "running")
            
            # Remove from both paused and auto-resume lists
            if scraper_name in paused_scrapers:
                paused_scrapers.remove(scraper_name)
                self.set_paused_scrapers(paused_scrapers)
            
            self.remove_from_auto_resume(scraper_name)
            
            logger.info(f"Resumed scraper {scraper_name} after queue size dropped below {RESUME_QUEUE_SIZE}")
            return True
            
        except Exception as e:
            logger.error(f"Error resuming scraper {scraper_name} from queue limit: {str(e)}")
            return False
    
    def check_and_manage_queue_limits(self) -> Dict[str, any]:
        """Check queue sizes and pause/resume scrapers as needed."""
        try:
            total_queue_size = self.get_total_llm_queue_size()
            paused_scrapers = self.get_paused_scrapers()
            actions_taken = []
            
            # Check Redis memory usage
            memory_info = self.get_redis_memory_usage()
            is_memory_pressure = self.is_memory_pressure()
            
            # Update last check time
            redis_client.set(LAST_QUEUE_CHECK_KEY, str(int(time.time())))
            
            # Store memory status for monitoring
            redis_client.set(REDIS_MEMORY_STATUS_KEY, 
                           str(memory_info).replace("'", '"'), ex=300)  # Expire after 5 minutes
            
            logger.debug(f"Queue check: total_size={total_queue_size}, paused_scrapers={paused_scrapers}, "
                        f"memory_usage={memory_info['used_memory_mb']}MB ({memory_info['usage_percent']}%)")
            
            # CRITICAL: Emergency stop all scrapers if queue is critically large OR memory is critically low
            if total_queue_size >= CRITICAL_QUEUE_SIZE or memory_info['usage_percent'] >= 90:
                logger.error(f"CRITICAL: Queue size {total_queue_size} or memory usage {memory_info['usage_percent']}% "
                           f"exceeds emergency threshold. Stopping all scrapers.")
                stopped_scrapers = self.emergency_stop_all_scrapers()
                actions_taken.extend([f"emergency_stopped_{scraper}" for scraper in stopped_scrapers])
            
            # High priority: Pause scrapers if queue is over limit OR memory pressure detected
            elif total_queue_size >= MAX_QUEUE_SIZE or is_memory_pressure:
                reason = "queue_limit" if total_queue_size >= MAX_QUEUE_SIZE else "memory_pressure"
                logger.warning(f"High load detected: queue_size={total_queue_size}, memory_pressure={is_memory_pressure}. "
                             f"Pausing scrapers due to {reason}.")
                
                # Pause all running scrapers
                for scraper_name in self.active_scrapers:
                    if scraper_name not in paused_scrapers and self.is_scraper_running(scraper_name):
                        if self.pause_scraper_for_queue_limit(scraper_name):
                            actions_taken.append(f"paused_{scraper_name}_{reason}")
            
            # Resume scrapers only if both queue size AND memory usage are below thresholds
            elif total_queue_size < RESUME_QUEUE_SIZE and not is_memory_pressure:
                logger.info(f"Load decreased: queue_size={total_queue_size}, memory_usage={memory_info['used_memory_mb']}MB. "
                          f"Resuming paused scrapers.")
                
                # Get both paused and auto-resume scrapers
                auto_resume_scrapers = self.get_auto_resume_scrapers()
                all_scrapers_to_resume = list(set(paused_scrapers + auto_resume_scrapers))
                
                logger.info(f"Scrapers to resume: paused={paused_scrapers}, auto_resume={auto_resume_scrapers}")
                
                # Resume paused scrapers first
                for scraper_name in paused_scrapers.copy():  # Use copy to avoid modification during iteration
                    if self.resume_scraper_from_queue_limit(scraper_name):
                        actions_taken.append(f"resumed_{scraper_name}")
                
                # Auto-start scrapers that should be running but aren't
                for scraper_name in auto_resume_scrapers.copy():
                    if scraper_name not in paused_scrapers:  # Don't double-process
                        if not self.is_scraper_running(scraper_name):
                            if self.auto_start_scraper(scraper_name):
                                self.remove_from_auto_resume(scraper_name)
                                actions_taken.append(f"auto_started_{scraper_name}")
                        else:
                            # Scraper is already running, remove from auto-resume list
                            self.remove_from_auto_resume(scraper_name)
                            actions_taken.append(f"auto_resume_cleaned_{scraper_name}")
            
            # Log status periodically
            if total_queue_size >= MAX_QUEUE_SIZE or is_memory_pressure:
                logger.warning(f"System under load: queue_size={total_queue_size}/{MAX_QUEUE_SIZE}, "
                             f"memory={memory_info['used_memory_mb']}MB ({memory_info['usage_percent']}%), "
                             f"paused_scrapers={paused_scrapers}")
            
            # Update status
            status = {
                "total_queue_size": total_queue_size,
                "paused_scrapers": self.get_paused_scrapers(),  # Get updated list
                "auto_resume_scrapers": self.get_auto_resume_scrapers(),  # Include auto-resume list
                "actions_taken": actions_taken,
                "max_limit": MAX_QUEUE_SIZE,
                "resume_threshold": RESUME_QUEUE_SIZE,
                "critical_limit": CRITICAL_QUEUE_SIZE,
                "memory_info": memory_info,
                "memory_pressure": is_memory_pressure,
                "last_check": int(time.time())
            }
            
            redis_client.set(QUEUE_MANAGEMENT_STATUS_KEY, 
                           str(status).replace("'", '"'), ex=300)  # Expire after 5 minutes
            
            return status
            
        except Exception as e:
            logger.error(f"Error in queue limit management: {str(e)}")
            return {
                "error": str(e),
                "total_queue_size": 0,
                "paused_scrapers": [],
                "actions_taken": [],
                "last_check": int(time.time())
            }
    
    def should_allow_queue_addition(self, scraper_name: str) -> bool:
        """Check if a scraper should be allowed to add items to LLM queues."""
        try:
            total_queue_size = self.get_total_llm_queue_size()
            paused_scrapers = self.get_paused_scrapers()
            
            # If scraper is in paused list due to queue limits, don't allow addition
            if scraper_name in paused_scrapers:
                return False
            
            # Check memory pressure
            is_memory_pressure = self.is_memory_pressure()
            
            # If queue is at/over limit, don't allow new additions
            if total_queue_size >= MAX_QUEUE_SIZE:
                return False
            
            # If memory pressure is detected, don't allow new additions
            if is_memory_pressure:
                logger.warning(f"Memory pressure detected, not allowing queue addition for {scraper_name}")
                return False
                
            # If approaching limits, be more conservative
            if total_queue_size >= (MAX_QUEUE_SIZE * 0.9):  # 90% of limit
                logger.warning(f"Queue approaching limit ({total_queue_size}/{MAX_QUEUE_SIZE}), "
                             f"not allowing addition for {scraper_name}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error checking queue addition permission for {scraper_name}: {str(e)}")
            return True  # Allow by default on error to prevent blocking
    
    def get_redis_memory_usage(self) -> Dict[str, int]:
        """Get current Redis memory usage."""
        try:
            info = redis_client.info('memory')
            used_memory = info.get('used_memory', 0)
            used_memory_mb = used_memory / 1024 / 1024
            max_memory = info.get('maxmemory', 0)
            max_memory_mb = max_memory / 1024 / 1024 if max_memory > 0 else 1800
            
            return {
                'used_memory_mb': int(used_memory_mb),
                'max_memory_mb': int(max_memory_mb),
                'usage_percent': int((used_memory_mb / max_memory_mb) * 100) if max_memory_mb > 0 else 0
            }
        except Exception as e:
            logger.error(f"Error getting Redis memory usage: {str(e)}")
            return {'used_memory_mb': 0, 'max_memory_mb': 1800, 'usage_percent': 0}
    
    def is_memory_pressure(self) -> bool:
        """Check if Redis is under memory pressure."""
        memory_info = self.get_redis_memory_usage()
        return memory_info['used_memory_mb'] > MEMORY_CHECK_THRESHOLD or memory_info['usage_percent'] > 80
    
    def emergency_stop_all_scrapers(self) -> List[str]:
        """Emergency stop all scrapers when Redis memory is critically low."""
        try:
            from app.services.scrapers.redis_utils import send_control_command
            
            stopped_scrapers = []
            for scraper_name in self.active_scrapers:
                if self.is_scraper_running(scraper_name):
                    # Send stop command
                    send_control_command(scraper_name, "stop")
                    # Set status to stopped
                    status_key = f"{scraper_name}_status"
                    redis_client.set(status_key, "stopped")
                    stopped_scrapers.append(scraper_name)
                    logger.warning(f"Emergency stopped scraper {scraper_name} due to critical memory pressure")
            
            # Clear both paused scrapers list and auto-resume list since we're stopping everything
            self.set_paused_scrapers([])
            self.set_auto_resume_scrapers([])
            
            return stopped_scrapers
            
        except Exception as e:
            logger.error(f"Error in emergency stop all scrapers: {str(e)}")
            return []
    
    def cleanup_stale_queues(self) -> Dict[str, int]:
        """Clean up stale or old items from queues to free memory."""
        try:
            from app.api.endpoints.scrapers import LLM_QUEUE_REGISTRY
            
            cleanup_stats = {}
            total_removed = 0
            
            # Get all registered LLM queues
            for queue_name in LLM_QUEUE_REGISTRY:
                try:
                    queue_length = redis_client.llen(queue_name)
                    if queue_length > 100:  # Only cleanup large queues
                        # Remove oldest items if queue is too large
                        max_allowed = 500  # Maximum items per queue
                        if queue_length > max_allowed:
                            items_to_remove = queue_length - max_allowed
                            # Remove from the left (oldest items)
                            removed = 0
                            for _ in range(items_to_remove):
                                if redis_client.lpop(queue_name):
                                    removed += 1
                                else:
                                    break
                            
                            cleanup_stats[queue_name] = removed
                            total_removed += removed
                            
                            if removed > 0:
                                logger.warning(f"Cleaned up {removed} old items from queue {queue_name} "
                                             f"(was {queue_length}, now {queue_length - removed})")
                
                except Exception as e:
                    logger.error(f"Error cleaning up queue {queue_name}: {str(e)}")
                    continue
            
            if total_removed > 0:
                logger.info(f"Queue cleanup completed: removed {total_removed} items total")
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error in queue cleanup: {str(e)}")
            return {}
    
    def force_memory_cleanup(self) -> bool:
        """Force cleanup of Redis memory by removing non-critical data."""
        try:
            actions_taken = []
            
            # 1. Clean up stale queues
            cleanup_stats = self.cleanup_stale_queues()
            if cleanup_stats:
                actions_taken.append(f"cleaned_queues_{sum(cleanup_stats.values())}_items")
            
            # 2. Remove old status entries (keep only recent ones)
            try:
                # Find all keys matching patterns and remove old ones
                keys_to_check = [
                    "queue_management_status",
                    "redis_memory_status",
                    "*_url_collection_completed",
                    "*_last_activity"
                ]
                
                for pattern in keys_to_check:
                    keys = redis_client.keys(pattern)
                    for key in keys:
                        # Check if key is old (TTL expired or very old)
                        ttl = redis_client.ttl(key)
                        if ttl == -1:  # No expiration set
                            redis_client.expire(key, 300)  # Set 5 minute expiration
                            
            except Exception as e:
                logger.error(f"Error cleaning up old status entries: {str(e)}")
            
            # 3. Force Redis to defragment memory
            try:
                redis_client.memory_purge()
                actions_taken.append("redis_memory_purge")
            except Exception as e:
                logger.debug(f"Redis memory purge not available: {str(e)}")
            
            logger.info(f"Force memory cleanup completed: {actions_taken}")
            return len(actions_taken) > 0
            
        except Exception as e:
            logger.error(f"Error in force memory cleanup: {str(e)}")
            return False
    
    def get_auto_resume_scrapers(self) -> List[str]:
        """Get list of scrapers that should be auto-resumed when conditions improve."""
        try:
            auto_resume_data = redis_client.get(SCRAPERS_AUTO_RESUME_KEY)
            if auto_resume_data:
                import json
                return json.loads(auto_resume_data.decode('utf-8'))
            return []
        except Exception as e:
            logger.error(f"Error getting auto-resume scrapers list: {str(e)}")
            return []
    
    def set_auto_resume_scrapers(self, scrapers: List[str]) -> None:
        """Set list of scrapers that should be auto-resumed."""
        try:
            import json
            redis_client.set(SCRAPERS_AUTO_RESUME_KEY, json.dumps(scrapers), ex=3600)  # Expire after 1 hour
        except Exception as e:
            logger.error(f"Error setting auto-resume scrapers list: {str(e)}")
    
    def add_to_auto_resume(self, scraper_name: str) -> None:
        """Add a scraper to the auto-resume list."""
        try:
            auto_resume_scrapers = self.get_auto_resume_scrapers()
            if scraper_name not in auto_resume_scrapers:
                auto_resume_scrapers.append(scraper_name)
                self.set_auto_resume_scrapers(auto_resume_scrapers)
                logger.info(f"Added {scraper_name} to auto-resume list")
        except Exception as e:
            logger.error(f"Error adding {scraper_name} to auto-resume list: {str(e)}")
    
    def remove_from_auto_resume(self, scraper_name: str) -> None:
        """Remove a scraper from the auto-resume list."""
        try:
            auto_resume_scrapers = self.get_auto_resume_scrapers()
            if scraper_name in auto_resume_scrapers:
                auto_resume_scrapers.remove(scraper_name)
                self.set_auto_resume_scrapers(auto_resume_scrapers)
                logger.info(f"Removed {scraper_name} from auto-resume list")
        except Exception as e:
            logger.error(f"Error removing {scraper_name} from auto-resume list: {str(e)}")
    
    def auto_start_scraper(self, scraper_name: str) -> bool:
        """Auto-start a scraper that should be running."""
        try:
            from app.services.scrapers.redis_utils import send_control_command
            
            # Set status to running
            status_key = f"{scraper_name}_status"
            redis_client.set(status_key, "running")
            
            # Send resume command just in case
            send_control_command(scraper_name, "resume")
            
            logger.info(f"Auto-started scraper {scraper_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error auto-starting scraper {scraper_name}: {str(e)}")
            return False
    
    def trigger_auto_resume_all(self) -> Dict[str, List[str]]:
        """Manually trigger auto-resume for all eligible scrapers."""
        try:
            paused_scrapers = self.get_paused_scrapers()
            auto_resume_scrapers = self.get_auto_resume_scrapers()
            
            resumed = []
            auto_started = []
            errors = []
            
            logger.info(f"Manual auto-resume triggered: paused={paused_scrapers}, auto_resume={auto_resume_scrapers}")
            
            # Resume paused scrapers
            for scraper_name in paused_scrapers.copy():
                try:
                    if self.resume_scraper_from_queue_limit(scraper_name):
                        resumed.append(scraper_name)
                except Exception as e:
                    logger.error(f"Error resuming {scraper_name}: {str(e)}")
                    errors.append(f"{scraper_name}: {str(e)}")
            
            # Auto-start scrapers that should be running
            for scraper_name in auto_resume_scrapers.copy():
                if scraper_name not in paused_scrapers:
                    try:
                        if not self.is_scraper_running(scraper_name):
                            if self.auto_start_scraper(scraper_name):
                                self.remove_from_auto_resume(scraper_name)
                                auto_started.append(scraper_name)
                        else:
                            # Already running, just clean up
                            self.remove_from_auto_resume(scraper_name)
                    except Exception as e:
                        logger.error(f"Error auto-starting {scraper_name}: {str(e)}")
                        errors.append(f"{scraper_name}: {str(e)}")
            
            return {
                "resumed": resumed,
                "auto_started": auto_started,
                "errors": errors
            }
            
        except Exception as e:
            logger.error(f"Error in trigger_auto_resume_all: {str(e)}")
            return {"resumed": [], "auto_started": [], "errors": [str(e)]}

def get_queue_management_status() -> Dict[str, any]:
    """Get the current queue management status."""
    try:
        status_data = redis_client.get(QUEUE_MANAGEMENT_STATUS_KEY)
        if status_data:
            # Parse the status (simple eval for now, could use json)
            import ast
            return ast.literal_eval(status_data.decode('utf-8'))
        return {
            "total_queue_size": 0,
            "paused_scrapers": [],
            "auto_resume_scrapers": [],
            "actions_taken": [],
            "max_limit": MAX_QUEUE_SIZE,
            "resume_threshold": RESUME_QUEUE_SIZE,
            "critical_limit": CRITICAL_QUEUE_SIZE,
            "memory_info": {"used_memory_mb": 0, "max_memory_mb": 1800, "usage_percent": 0},
            "memory_pressure": False,
            "last_check": None
        }
    except Exception as e:
        logger.error(f"Error getting queue management status: {str(e)}")
        return {"error": str(e)}

# Global instance for easy access
queue_limit_manager = QueueLimitManager() 