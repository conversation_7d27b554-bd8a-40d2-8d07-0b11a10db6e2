#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to manually run a specific scraper.
Usage: python scripts/run_scraper.py agenz
"""
import sys
import os
import logging
from sqlalchemy.orm import Session

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.db.session import SessionLocal
from app.services.scrapers_deprecated import run_scraper

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to run a scraper."""
    if len(sys.argv) < 2:
        logger.error("Usage: python scripts/run_scraper.py <scraper_name>")
        return
    
    scraper_name = sys.argv[1].lower()
    logger.info(f"Running scraper: {scraper_name}")
    
    db = SessionLocal()
    try:
        # Run the scraper
        new_listings = run_scraper(db, scraper_name)
        if new_listings >= 0:
            logger.info(f"Scraper '{scraper_name}' added {new_listings} new listings")
        else:
            logger.error(f"Scraper '{scraper_name}' not found")
    except Exception as e:
        logger.error(f"Error running scraper '{scraper_name}': {e}")
    finally:
        db.close()

if __name__ == "__main__":
    main() 