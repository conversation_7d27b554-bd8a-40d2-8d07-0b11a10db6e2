import os
import logging
import requests
import hashlib
from pathlib import Path
from typing import Optional, List, Tuple
from urllib.parse import urlparse
import random
import time
from PIL import Image, ImageOps

logger = logging.getLogger(__name__)

# Create media directory if it doesn't exist
MEDIA_DIR = Path("./media/property_images")
MEDIA_DIR.mkdir(parents=True, exist_ok=True)

# Base URL for serving images
BASE_URL = os.environ.get("API_BASE_URL", "http://localhost:8000")

# Thumbnail settings - these are maximum dimensions, aspect ratio will be preserved
THUMBNAIL_SIZES = {
    'small': (300, 300),   # For property cards and city cards - max 300px on either dimension
}

# List of common user agents to rotate through
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]

# Session object to maintain cookies across requests
session = requests.Session()

def create_thumbnail(image_path: Path, max_size: Tuple[int, int], suffix: str) -> Optional[str]:
    """
    Create a thumbnail version of an image while preserving aspect ratio.
    
    Args:
        image_path: Path to the original image
        max_size: Tuple of (max_width, max_height) - thumbnail will fit within these bounds
        suffix: Suffix to add to the filename (e.g., '_small', '_medium')
        
    Returns:
        URL of the thumbnail or None if creation failed
    """
    try:
        with Image.open(image_path) as img:
            # Store original dimensions for logging
            original_width, original_height = img.size
            
            # Convert to RGB if necessary (handles transparency)
            if img.mode in ('RGBA', 'LA', 'P'):
                # Create a white background
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Calculate thumbnail size while preserving aspect ratio
            # thumbnail() method automatically preserves aspect ratio and fits within max_size
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # Log the size reduction for debugging
            new_width, new_height = img.size
            logger.debug(f"Resized {image_path.name} from {original_width}x{original_height} to {new_width}x{new_height}")
            
            # Generate thumbnail filename
            stem = image_path.stem
            extension = image_path.suffix
            thumbnail_filename = f"{stem}{suffix}{extension}"
            thumbnail_path = image_path.parent / thumbnail_filename
            
            # Save thumbnail with optimization
            img.save(thumbnail_path, optimize=True, quality=85)
            
            return f"{BASE_URL}/media/property_images/{thumbnail_filename}"
            
    except Exception as e:
        logger.error(f"Error creating thumbnail for {image_path}: {str(e)}")
        return None

def download_image_to_filesystem(image_url: str, retry_count: int = 0) -> Optional[dict]:
    """
    Download an image from a URL and save it locally with thumbnails, returning URLs.
    
    Args:
        image_url: URL of the image to download
        retry_count: Number of retry attempts made
        
    Returns:
        Dictionary with original and thumbnail URLs or None if download failed
    """
    try:
        # Generate a unique filename based on the URL
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        
        # Extract file extension from URL
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        
        # If no valid extension found, default to .jpg
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
            
        # Create filename with hash and extension
        filename = f"{url_hash}{extension}"
        filepath = MEDIA_DIR / filename
        
        # Check if file already exists
        if filepath.exists():
            return get_image_urls(filename)
        
        # Extract domain for referer header
        domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        
        # Set browser-like headers to avoid bot detection
        headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Referer": domain,
            "Origin": domain,
            "sec-ch-ua": '"Not.A/Brand";v="8", "Chromium";v="123", "Google Chrome";v="123"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "Sec-Fetch-Dest": "image",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Site": "same-origin",
            "DNT": "1",
            "Connection": "keep-alive",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        }
        
        # Download the image using the session with headers
        response = session.get(image_url, stream=True, timeout=15, headers=headers)
        response.raise_for_status()
        
        # Save the original image
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Generate thumbnails
        generate_thumbnails_for_image(filepath)
        
        # Return URLs
        return get_image_urls(filename)
        
    except requests.exceptions.HTTPError as http_err:
        status_code = http_err.response.status_code if hasattr(http_err, 'response') else "unknown"
        logger.error(f"HTTP error ({status_code}) downloading image {image_url}: {str(http_err)}")
        
        # If we encounter a 403 or 429 status code, try alternative approaches
        if (status_code == 403 or status_code == 429) and retry_count < 2:
            # Try alternative approach (with delay to avoid rate limiting)
            time.sleep(2 + random.random() * 3)  # Random delay between 2-5 seconds
            return try_alternative_download_filesystem(image_url, retry_count + 1)
        
        return None
        
    except requests.exceptions.ConnectionError as conn_err:
        logger.error(f"Connection error downloading image {image_url}: {str(conn_err)}")
        
        # Try alternative approach if we haven't reached max retries
        if retry_count < 2:
            time.sleep(2)  # Wait before retry
            return try_alternative_download_filesystem(image_url, retry_count + 1)
            
        return None
        
    except Exception as e:
        logger.error(f"Error downloading image {image_url}: {str(e)}")
        return None

def generate_thumbnails_for_image(image_path: Path) -> None:
    """
    Generate all thumbnail sizes for an image.
    
    Args:
        image_path: Path to the original image
    """
    for suffix, size in THUMBNAIL_SIZES.items():
        create_thumbnail(image_path, size, f"_{suffix}")

def get_image_urls(filename: str) -> dict:
    """
    Get all URLs for an image (original and small thumbnail).
    
    Args:
        filename: Base filename of the image
        
    Returns:
        Dictionary with original and thumbnail URLs
    """
    stem = Path(filename).stem
    extension = Path(filename).suffix
    
    urls = {
        'original': f"{BASE_URL}/media/property_images/{filename}",
        'small': f"{BASE_URL}/media/property_images/{stem}_small{extension}",
    }
    
    return urls

def try_alternative_download_filesystem(image_url: str, retry_count: int = 0) -> Optional[dict]:
    """
    Try alternative methods to download an image to filesystem.
    
    Args:
        image_url: URL of the image to download
        retry_count: Number of retry attempts made
        
    Returns:
        Dictionary with original and thumbnail URLs or None if download failed
    """
    methods = [
        download_with_minimal_headers_filesystem,
        download_with_referer_chain_filesystem
    ]
    
    for method in methods:
        try:
            result = method(image_url)
            if result:
                return result
        except Exception as e:
            logger.error(f"Alternative download method failed: {str(e)}")
            continue
    
    return None

def download_with_minimal_headers_filesystem(image_url: str) -> Optional[dict]:
    """Download with minimal headers to avoid triggering anti-bot measures."""
    try:
        # Generate filename
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
        filename = f"{url_hash}{extension}"
        filepath = MEDIA_DIR / filename
        
        # Check if file already exists
        if filepath.exists():
            return get_image_urls(filename)
        
        # Minimal headers
        headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "image/*,*/*;q=0.8"
        }
        
        # Download with minimal headers
        response = session.get(image_url, stream=True, timeout=15, headers=headers)
        response.raise_for_status()
        
        # Save the image
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Generate thumbnails
        generate_thumbnails_for_image(filepath)
        
        return get_image_urls(filename)
        
    except Exception as e:
        logger.error(f"Minimal headers download failed: {str(e)}")
        return None

def download_with_referer_chain_filesystem(image_url: str) -> Optional[dict]:
    """
    Try to download by first visiting the referring page then the image.
    This can help bypass referer checks.
    """
    try:
        # Generate filename
        url_hash = hashlib.md5(image_url.encode()).hexdigest()
        parsed_url = urlparse(image_url)
        path = parsed_url.path
        extension = os.path.splitext(path)[1]
        if not extension or extension.lower() not in ['.jpg', '.jpeg', '.png', '.webp', '.gif']:
            extension = '.jpg'
        filename = f"{url_hash}{extension}"
        filepath = MEDIA_DIR / filename
        
        # Check if file already exists
        if filepath.exists():
            return get_image_urls(filename)
        
        # Create a brand new session
        new_session = requests.Session()
        user_agent = random.choice(USER_AGENTS)
        
        # Extract domain and potential referer URL
        domain = f"{parsed_url.scheme}://{parsed_url.netloc}"
        base_path = "/".join(parsed_url.path.split("/")[:-1])
        potential_referer = f"{domain}{base_path}"
        
        # First, visit the potential referring page
        try:
            new_session.get(
                potential_referer,
                headers={"User-Agent": user_agent, "Accept": "text/html"},
                timeout=10
            )
        except:
            # If referer page fails, continue anyway
            pass
        
        # Then try to download the image
        response = new_session.get(
            image_url,
            stream=True,
            timeout=15,
            headers={
                "User-Agent": user_agent,
                "Referer": potential_referer,
                "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8"
            }
        )
        response.raise_for_status()
        
        # Save the image
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Generate thumbnails
        generate_thumbnails_for_image(filepath)
        
        return get_image_urls(filename)
        
    except Exception as e:
        logger.error(f"Referer chain download failed: {str(e)}")
        return None

def save_uploaded_image_to_filesystem(file_content: bytes, filename: str, user_id: Optional[int] = None) -> dict:
    """
    Save uploaded image content to filesystem with thumbnails and return URLs.
    
    Args:
        file_content: Binary content of the image file
        filename: Original filename
        user_id: Optional user ID for user-specific directory
        
    Returns:
        Dictionary with original and thumbnail URLs
    """
    # Generate unique filename to avoid conflicts
    import uuid
    file_extension = os.path.splitext(filename)[1].lower()
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    
    # Create user-specific directory if user_id provided
    if user_id:
        user_dir = MEDIA_DIR / str(user_id)
        user_dir.mkdir(parents=True, exist_ok=True)
        filepath = user_dir / unique_filename
        relative_path = f"property_images/{user_id}/{unique_filename}"
    else:
        filepath = MEDIA_DIR / unique_filename
        relative_path = f"property_images/{unique_filename}"
    
    # Save the file
    with open(filepath, 'wb') as f:
        f.write(file_content)
    
    # Generate thumbnails
    generate_thumbnails_for_image(filepath)
    
    # Return URLs
    if user_id:
        stem = Path(unique_filename).stem
        extension = Path(unique_filename).suffix
        return {
            'original': f"{BASE_URL}/media/property_images/{user_id}/{unique_filename}",
            'small': f"{BASE_URL}/media/property_images/{user_id}/{stem}_small{extension}"
        }
    else:
        return get_image_urls(unique_filename)

class FilesystemImageProcessor:
    """Utility class for processing images with direct filesystem URLs."""
    
    @staticmethod
    def process_images_for_filesystem(image_urls: List[str]) -> List[dict]:
        """Process images by downloading them and returning filesystem URLs with thumbnails.
        
        Args:
            image_urls: List of original image URLs
            
        Returns:
            List of dictionaries with original and thumbnail URLs
        """
        filesystem_urls = []
        
        for image_url in image_urls:
            if not image_url:
                continue
                
            try:
                # Check if it's already a filesystem URL
                if "/media/property_images/" in image_url:
                    # Extract filename and generate URLs
                    filename = image_url.split("/")[-1]
                    # Remove any suffix to get base filename
                    if "_small" in filename:
                        # Extract base filename
                        filename = filename.replace("_small", "")
                    filesystem_urls.append(get_image_urls(filename))
                    continue
                    
                # Download the image to local storage
                image_data = download_image_to_filesystem(image_url)
                
                if image_data:
                    filesystem_urls.append(image_data)
                else:
                    # Fallback: create a dict with the original URL for both sizes
                    filesystem_urls.append({
                        'original': image_url,
                        'small': image_url
                    })
                    
            except Exception as e:
                logger.error(f"Error processing image: {str(e)}")
                # Fallback: create a dict with the original URL for both sizes
                filesystem_urls.append({
                    'original': image_url,
                    'small': image_url
                })
        
        return filesystem_urls

# Legacy function alias for backward compatibility
def process_images_for_filesystem(image_urls: List[str]) -> List[dict]:
    """Legacy alias for FilesystemImageProcessor.process_images_for_filesystem."""
    return FilesystemImageProcessor.process_images_for_filesystem(image_urls) 