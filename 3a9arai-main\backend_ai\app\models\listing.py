from sqlalchemy import Column, Integer, String, Float, Text, DateTime, func, Boolean, JSON, ForeignKey, Enum
from sqlalchemy.sql import expression
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.db.session import Base

class ApprovalStatus(enum.Enum):
    pending = "pending"
    approved = "approved"
    rejected = "rejected"

class Listing(Base):
    __tablename__ = "listings"

    id = Column(Integer, primary_key=True, index=True)
    
    # User ownership
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # Approval system
    approval_status = Column(Enum(ApprovalStatus), nullable=False, default=ApprovalStatus.approved, index=True)
    is_user_generated = Column(Boolean, nullable=False, default=False, index=True)
    approved_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    approval_date = Column(DateTime, nullable=True)
    rejection_reason = Column(Text, nullable=True)
    
    # Core Vitals (URL optional for user-generated listings)
    url = Column(String(500), nullable=True)  # Nullable for user-generated listings
    title = Column(String(500), nullable=False)
    price = Column(Float, nullable=False)
    size_sqm = Column(Float, nullable=False)
    note_about_size_sqm = Column(String(200), nullable=True, default="Surface habitable")
    city = Column(String(100), nullable=False)
    property_type = Column(String(50), nullable=False)  # appartement, maison, terrain, bureau, local commercial, autre, residence
    property_category = Column(String(50), nullable=False)  # residential, commercial, land
    transaction_type = Column(String(50), nullable=False, default='sell')  # sell, rent
    description_html = Column(Text, nullable=False)
    contact_name = Column(String(100), nullable=False)
    contact_phone = Column(String(100), nullable=False)
    contact_email = Column(String(100), nullable=True)
    images = Column(JSON, nullable=False)  # List of image URLs
    
    # Flexible, Structured Information
    additional_info = Column(JSON, nullable=True, default=list)  # List of AdditionalInfo objects
    
    # Location, Contact & Metadata
    neighborhood = Column(String(100), nullable=True)
    scraper = Column(String(50), nullable=True)  # Nullable for user-generated listings
    price_per_sqm = Column(Float, nullable=True)
    
    # Legacy fields for backward compatibility (keeping existing functionality)
    card_title = Column(String(100), nullable=True)  # Shorter title for card display
    gps_coordinates = Column(JSON, nullable=True)  # Store as {latitude: float, longitude: float}
    image_url = Column(String(500), nullable=True)  # Single image URL for backward compatibility
    date_posted = Column(String(100), nullable=True)
    contact_info = Column(String(100), nullable=True)  # General contact info
    source_website = Column(String(50), nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=True, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Legacy property detail fields that will be moved to additional_info
    size = Column(Float, nullable=True)  # For backward compatibility, will map to size_sqm
    rooms = Column(Integer, nullable=True)
    bathrooms = Column(Integer, nullable=True)
    
    # Add relationship to owner user
    owner = relationship("User", foreign_keys=[user_id], back_populates="listings")
    
    # Add relationship to users who bookmarked this listing
    bookmarked_by = relationship("Bookmark", back_populates="listing", cascade="all, delete-orphan")
    
    # Add relationship to users who viewed this listing
    viewed_by = relationship("ViewHistory", back_populates="listing", cascade="all, delete-orphan")
    
    # Add relationship to approver user
    approver = relationship("User", foreign_keys=[approved_by])
    
    # Add relationship to public views (including anonymous)
    public_views = relationship("ListingView", back_populates="listing", cascade="all, delete-orphan") 