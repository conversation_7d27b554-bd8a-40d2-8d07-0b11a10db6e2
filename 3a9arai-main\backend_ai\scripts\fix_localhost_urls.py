#!/usr/bin/env python3
"""
Script to fix localhost:8000 URLs in image fields with the actual production URL.

This script will:
1. Connect to the database
2. Find all listings with localhost:8000 URLs in the images column
3. Replace localhost:8000 with the actual production URL
4. Update the database records

Usage:
    python fix_localhost_urls.py --production-url https://yourdomain.com [--dry-run]
"""

import sys
import os
import re
import json
import argparse
from typing import List, Optional

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal
from app.models.listing import Listing

# Localhost patterns to replace
LOCALHOST_PATTERNS = [
    "http://localhost:8000",
    "https://localhost:8000",
    "localhost:8000"
]

def fix_image_url(image_url: str, production_url: str) -> str:
    """
    Replace localhost:8000 with the production URL in an image URL.
    
    Args:
        image_url: The image URL to fix
        production_url: The production URL to use as replacement
        
    Returns:
        Fixed image URL
    """
    if not image_url:
        return image_url
    
    fixed_url = image_url
    
    # Replace each localhost pattern
    for pattern in LOCALHOST_PATTERNS:
        if pattern in fixed_url:
            fixed_url = fixed_url.replace(pattern, production_url.rstrip('/'))
            break
    
    return fixed_url

def fix_images_list(images: List[str], production_url: str) -> List[str]:
    """
    Fix a list of image URLs by replacing localhost:8000 with production URL.
    
    Args:
        images: List of image URLs
        production_url: The production URL to use as replacement
        
    Returns:
        List of fixed URLs
    """
    fixed = []
    
    for image_url in images:
        if not image_url:
            fixed.append(image_url)
            continue
            
        fixed_url = fix_image_url(image_url, production_url)
        fixed.append(fixed_url)
            
    return fixed

def has_localhost_urls(images: List[str]) -> bool:
    """
    Check if any image URL contains localhost:8000.
    
    Args:
        images: List of image URLs
        
    Returns:
        True if any URL contains localhost patterns
    """
    for image_url in images:
        if not image_url:
            continue
        for pattern in LOCALHOST_PATTERNS:
            if pattern in image_url:
                return True
    return False

def fix_listing_images(db: Session, production_url: str, dry_run: bool = False) -> dict:
    """
    Fix all listing images by replacing localhost:8000 URLs with production URL.
    
    Args:
        db: Database session
        production_url: The production URL to use as replacement
        dry_run: If True, don't actually update the database
        
    Returns:
        Dictionary with fix statistics
    """
    stats = {
        "total_listings": 0,
        "listings_with_images": 0,
        "listings_with_localhost": 0,
        "listings_updated": 0,
        "images_fixed": 0,
        "errors": []
    }
    
    try:
        # Get all listings
        listings = db.query(Listing).all()
        stats["total_listings"] = len(listings)
        
        print(f"Processing {len(listings)} listings...")
        
        for listing in listings:
            try:
                if not listing.images or len(listing.images) == 0:
                    continue
                    
                stats["listings_with_images"] += 1
                
                # Check if this listing has localhost URLs
                if not has_localhost_urls(listing.images):
                    continue
                
                stats["listings_with_localhost"] += 1
                
                # Fix images
                original_images = listing.images
                fixed_images = fix_images_list(original_images, production_url)
                
                # Check if any images were actually changed
                images_changed = original_images != fixed_images
                
                if images_changed:
                    stats["listings_updated"] += 1
                    
                    # Count fixed images
                    for orig, fixed in zip(original_images, fixed_images):
                        if orig != fixed:
                            stats["images_fixed"] += 1
                    
                    print(f"Listing {listing.id}: Fixing {stats['images_fixed']} localhost URLs")
                    
                    if not dry_run:
                        listing.images = fixed_images
                        db.add(listing)
                    else:
                        print(f"  DRY RUN: Would update images:")
                        for orig, fixed in zip(original_images, fixed_images):
                            if orig != fixed:
                                print(f"    {orig}")
                                print(f"    -> {fixed}")
                                
            except Exception as e:
                error_msg = f"Error processing listing {listing.id}: {e}"
                print(error_msg)
                stats["errors"].append(error_msg)
                
        if not dry_run:
            db.commit()
            print("Database changes committed.")
        else:
            print("DRY RUN: No changes made to database.")
            
    except Exception as e:
        error_msg = f"Database error: {e}"
        print(error_msg)
        stats["errors"].append(error_msg)
        if not dry_run:
            db.rollback()
            
    return stats

def validate_production_url(url: str) -> str:
    """
    Validate and normalize the production URL.
    
    Args:
        url: The production URL to validate
        
    Returns:
        Normalized URL
        
    Raises:
        ValueError: If URL is invalid
    """
    if not url:
        raise ValueError("Production URL cannot be empty")
    
    # Add https:// if no scheme provided
    if not url.startswith(('http://', 'https://')):
        url = f"https://{url}"
    
    # Remove trailing slash
    url = url.rstrip('/')
    
    return url

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Fix localhost:8000 URLs in image fields")
    parser.add_argument("--production-url", required=True, help="Production URL to replace localhost:8000")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    
    args = parser.parse_args()
    
    try:
        production_url = validate_production_url(args.production_url)
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    print("=" * 60)
    print("Localhost URL Fix Script")
    print("=" * 60)
    print(f"Production URL: {production_url}")
    print(f"Dry run: {args.dry_run}")
    print()
    
    # Create database session
    db = SessionLocal()
    
    try:
        # Run the fix
        stats = fix_listing_images(db, production_url, dry_run=args.dry_run)
        
        # Print results
        print()
        print("=" * 60)
        print("Fix Results")
        print("=" * 60)
        print(f"Total listings: {stats['total_listings']}")
        print(f"Listings with images: {stats['listings_with_images']}")
        print(f"Listings with localhost URLs: {stats['listings_with_localhost']}")
        print(f"Listings updated: {stats['listings_updated']}")
        print(f"Images fixed: {stats['images_fixed']}")
        
        if stats["errors"]:
            print(f"Errors: {len(stats['errors'])}")
            for error in stats["errors"]:
                print(f"  - {error}")
        else:
            print("No errors occurred.")
            
        if args.dry_run:
            print("\nThis was a dry run. No changes were made to the database.")
            print("Run without --dry-run to apply changes.")
        else:
            print("\nFix completed successfully!")
            
    finally:
        db.close()

if __name__ == "__main__":
    main() 