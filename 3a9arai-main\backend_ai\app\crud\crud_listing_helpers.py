"""
Helper functions for filtering listings based on additional_info JSON field.
This module provides functions to create SQL conditions for filtering listings
where property details are stored in the additional_info JSON field.

Note: These functions are designed for PostgreSQL JSON operations.
"""
from typing import List, Dict, Any, Optional
from sqlalchemy import func, and_, or_, text, cast, Numeric
from sqlalchemy.orm import Session
from app.models.listing import Listing
import json



def create_rooms_filter(rooms_min: Optional[int] = None, rooms_max: Optional[int] = None):
    """
    Create filter condition for rooms ('Pièces') in additional_info.
    Uses PostgreSQL JSON operators to search within the JSON array.
    """
    if rooms_min is None and rooms_max is None:
        return None
    
    conditions = []
    
    # PostgreSQL JSON condition: find objects where name='<PERSON>è<PERSON>' and extract the value
    # Only try to cast to numeric if the value looks like a number
    if rooms_min is not None:
        conditions.append(
            text("""
                EXISTS (
                    SELECT 1 FROM json_array_elements(additional_info) AS elem
                    WHERE elem->>'name' = '<PERSON>è<PERSON>' 
                    AND elem->>'value' ~ '^[0-9]+(\.[0-9]+)?$'
                    AND (elem->>'value')::numeric >= :rooms_min
                )
            """).bindparams(rooms_min=rooms_min)
        )
    
    if rooms_max is not None:
        conditions.append(
            text("""
                EXISTS (
                    SELECT 1 FROM json_array_elements(additional_info) AS elem
                    WHERE elem->>'name' = 'Pièces' 
                    AND elem->>'value' ~ '^[0-9]+(\.[0-9]+)?$'
                    AND (elem->>'value')::numeric <= :rooms_max
                )
            """).bindparams(rooms_max=rooms_max)
        )
    
    return and_(*conditions) if len(conditions) > 1 else conditions[0]


def create_bathrooms_filter(bathrooms_min: Optional[int] = None, bathrooms_max: Optional[int] = None):
    """
    Create filter condition for bathrooms ('Salles de bain') in additional_info.
    Uses PostgreSQL JSON operators to search within the JSON array.
    """
    if bathrooms_min is None and bathrooms_max is None:
        return None
    
    conditions = []
    
    if bathrooms_min is not None:
        conditions.append(
            text("""
                EXISTS (
                    SELECT 1 FROM json_array_elements(additional_info) AS elem
                    WHERE elem->>'name' = 'Salles de bain' 
                    AND elem->>'value' ~ '^[0-9]+(\.[0-9]+)?$'
                    AND (elem->>'value')::numeric >= :bathrooms_min
                )
            """).bindparams(bathrooms_min=bathrooms_min)
        )
    
    if bathrooms_max is not None:
        conditions.append(
            text("""
                EXISTS (
                    SELECT 1 FROM json_array_elements(additional_info) AS elem
                    WHERE elem->>'name' = 'Salles de bain' 
                    AND elem->>'value' ~ '^[0-9]+(\.[0-9]+)?$'
                    AND (elem->>'value')::numeric <= :bathrooms_max
                )
            """).bindparams(bathrooms_max=bathrooms_max)
        )
    
    return and_(*conditions) if len(conditions) > 1 else conditions[0]


def create_boolean_filter(json_key: str, filter_value: bool):
    """
    Create filter condition for boolean values in additional_info.
    Uses PostgreSQL JSON operators to search within the JSON array.
    """
    json_bool_value = 'Oui' if filter_value else 'Non'
    
    return text("""
        EXISTS (
            SELECT 1 FROM json_array_elements(additional_info) AS elem
            WHERE elem->>'name' = :json_key 
            AND elem->>'value' = :json_value
        )
    """).bindparams(json_key=json_key, json_value=json_bool_value)


def create_elevator_filter(has_elevator: bool):
    """Create filter condition for elevator ('Ascenseur') in additional_info."""
    return create_boolean_filter('Ascenseur', has_elevator)


def create_garden_filter(has_garden: bool):
    """Create filter condition for garden ('Jardin') in additional_info."""
    return create_boolean_filter('Jardin', has_garden)


def create_balcony_filter(has_balcony: bool):
    """Create filter condition for balcony ('Balcon') in additional_info."""
    return create_boolean_filter('Balcon', has_balcony)


def create_terrace_filter(has_terrace: bool):
    """Create filter condition for terrace ('Terrasse') in additional_info."""
    return create_boolean_filter('Terrasse', has_terrace)


def create_furnished_filter(is_furnished: bool):
    """Create filter condition for furnished ('Meublé') in additional_info."""
    return create_boolean_filter('Meublé', is_furnished)


def create_parking_filter(has_parking: bool):
    """Create filter condition for parking ('Parking') in additional_info."""
    return create_boolean_filter('Parking', has_parking)


# Mapping of standard filter names to their JSON keys
FILTER_KEY_MAPPING = {
    'rooms': 'Pièces',
    'bedrooms': 'Chambres', 
    'bathrooms': 'Salles de bain',
    'floor': 'Étage',
    'total_floors': 'Étages totaux',
    'construction_year': 'Année de construction',
    'parking': 'Parking',
    'elevator': 'Ascenseur',
    'balcony': 'Balcon',
    'terrace': 'Terrasse',
    'garden': 'Jardin',
    'pool': 'Piscine',
    'orientation': 'Orientation',
    'furnished': 'Meublé',
    'surface': 'Surface'
} 