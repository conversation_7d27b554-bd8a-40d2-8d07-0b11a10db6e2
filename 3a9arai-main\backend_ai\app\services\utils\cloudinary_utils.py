import cloudinary
import cloudinary.uploader
from cloudinary.utils import cloudinary_url
import logging
import os
from typing import Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.environ.get("CLOUDINARY_CLOUD_NAME", "dxpoygqpz"),
    api_key=os.environ.get("CLOUDINARY_API_KEY", "567454988364888"),
    api_secret=os.environ.get("CLOUDINARY_API_SECRET", ""),
    secure=True
)

def upload_image_to_cloudinary(image_url: str) -> Optional[Dict[str, Any]]:
    """
    Upload an image from a URL to Cloudinary.
    
    Args:
        image_url: URL of the image to upload
        
    Returns:
        Dictionary containing the upload result or None if upload failed
    """
    try:
        logger.info(f"Uploading image to Cloudinary: {image_url}")
        
        # Generate a unique public_id based on part of the URL
        # This helps avoid duplicate uploads of the same image
        
        # Upload the image
        upload_result = cloudinary.uploader.upload(
            image_url,
        )
        
        logger.info(f"Image uploaded successfully to Cloudinary")
        return upload_result
        
    except Exception as e:
        logger.error(f"Error uploading image to Cloudinary: {str(e)}")
        return None
        
def get_optimized_image_url(public_id: str) -> str:
    """
    Get an optimized image URL from Cloudinary.
    
    Args:
        public_id: Public ID of the image
        
    Returns:
        Optimized image URL with auto-format and quality
    """
    try:
        # Create optimized URL with auto-format and quality
        optimize_url, _ = cloudinary_url(
            public_id, 
            fetch_format="auto", 
            quality="auto",
            secure=True
        )
        
        return optimize_url
        
    except Exception as e:
        logger.error(f"Error getting optimized URL: {str(e)}")
        return "" 