#!/usr/bin/env python3
"""
Helper script to check existing filesystem images in the database.
This script helps you understand what needs to be migrated before running the migration.

Usage:
    python check_filesystem_images.py
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, <PERSON><PERSON>

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

try:
    from app.db.session import SessionLocal
    from app.models.listing import Listing
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this script from the backend directory with proper environment setup")
    sys.exit(1)

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def check_image_file(image_url: str) -> <PERSON>ple[bool, int]:
    """Check if image file exists and return its size."""
    if '/media/property_images/' not in image_url:
        return False, 0
    
    # Extract relative path from URL
    url_parts = image_url.split('/media/property_images/')
    if len(url_parts) != 2:
        return False, 0
    
    relative_path = url_parts[1]
    local_path = Path('/app/media/property_images') / relative_path
    
    if local_path.exists() and local_path.is_file():
        return True, local_path.stat().st_size
    else:
        return False, 0

def analyze_filesystem_images():
    """Analyze all filesystem images in the database."""
    print("🔍 Analyzing filesystem images in database...")
    print("=" * 60)
    
    db = SessionLocal()
    try:
        # Get all listings
        all_listings = db.query(Listing).all()
        
        # Statistics
        stats = {
            'total_listings': len(all_listings),
            'listings_with_images': 0,
            'listings_with_filesystem_images': 0,
            'total_images': 0,
            'filesystem_images': 0,
            's3_images': 0,
            'existing_files': 0,
            'missing_files': 0,
            'total_file_size': 0
        }
        
        # Detailed results
        filesystem_listings = []
        missing_files = []
        
        for listing in all_listings:
            if not listing.images:
                continue
            
            stats['listings_with_images'] += 1
            
            listing_filesystem_images = []
            listing_missing_files = []
            
            for img in listing.images:
                stats['total_images'] += 1
                
                if isinstance(img, str):
                    if '/media/property_images/' in img:
                        # This is a filesystem image
                        stats['filesystem_images'] += 1
                        listing_filesystem_images.append(img)
                        
                        # Check if file exists
                        exists, size = check_image_file(img)
                        if exists:
                            stats['existing_files'] += 1
                            stats['total_file_size'] += size
                        else:
                            stats['missing_files'] += 1
                            listing_missing_files.append(img)
                            
                    elif 'digitaloceanspaces.com' in img or 's3' in img.lower():
                        # This is likely an S3 image
                        stats['s3_images'] += 1
            
            if listing_filesystem_images:
                stats['listings_with_filesystem_images'] += 1
                filesystem_listings.append({
                    'id': listing.id,
                    'title': listing.title,
                    'filesystem_images': listing_filesystem_images,
                    'missing_files': listing_missing_files
                })
        
        # Print summary
        print("📊 SUMMARY")
        print("-" * 30)
        print(f"Total listings: {stats['total_listings']}")
        print(f"Listings with images: {stats['listings_with_images']}")
        print(f"Listings with filesystem images: {stats['listings_with_filesystem_images']}")
        print(f"Total images: {stats['total_images']}")
        print(f"  - Filesystem images: {stats['filesystem_images']}")
        print(f"  - S3 images: {stats['s3_images']}")
        print(f"  - Other images: {stats['total_images'] - stats['filesystem_images'] - stats['s3_images']}")
        print()
        print(f"Filesystem image files:")
        print(f"  - Existing files: {stats['existing_files']}")
        print(f"  - Missing files: {stats['missing_files']}")
        print(f"  - Total size: {format_file_size(stats['total_file_size'])}")
        
        # Migration estimate
        if stats['filesystem_images'] > 0:
            print()
            print("⏱️  MIGRATION ESTIMATE")
            print("-" * 30)
            avg_size = stats['total_file_size'] / stats['existing_files'] if stats['existing_files'] > 0 else 0
            print(f"Average image size: {format_file_size(avg_size)}")
            
            # Rough estimate: 1MB per second upload speed
            estimated_time_seconds = stats['total_file_size'] / (1024 * 1024)  # Assume 1MB/s
            estimated_minutes = estimated_time_seconds / 60
            
            if estimated_minutes < 1:
                print(f"Estimated migration time: < 1 minute")
            elif estimated_minutes < 60:
                print(f"Estimated migration time: ~{estimated_minutes:.1f} minutes")
            else:
                print(f"Estimated migration time: ~{estimated_minutes/60:.1f} hours")
            
            print(f"Note: Actual time depends on your internet speed and S3 upload speed")
        
        # Detailed listing information
        if filesystem_listings:
            print()
            print("📋 LISTINGS TO MIGRATE")
            print("-" * 30)
            
            for i, listing_info in enumerate(filesystem_listings[:10], 1):  # Show first 10
                title = listing_info['title'][:50] + "..." if len(listing_info['title']) > 50 else listing_info['title']
                print(f"{i:2d}. ID {listing_info['id']:3d}: {title}")
                print(f"    Filesystem images: {len(listing_info['filesystem_images'])}")
                if listing_info['missing_files']:
                    print(f"    Missing files: {len(listing_info['missing_files'])}")
            
            if len(filesystem_listings) > 10:
                print(f"    ... and {len(filesystem_listings) - 10} more listings")
        
        # Missing files details
        if stats['missing_files'] > 0:
            print()
            print("⚠️  MISSING FILES")
            print("-" * 30)
            print(f"Found {stats['missing_files']} filesystem URLs pointing to missing files:")
            
            all_missing = []
            for listing_info in filesystem_listings:
                all_missing.extend(listing_info['missing_files'])
            
            for missing_file in all_missing[:5]:  # Show first 5
                print(f"  - {missing_file}")
            
            if len(all_missing) > 5:
                print(f"  ... and {len(all_missing) - 5} more missing files")
            
            print("\nThese URLs will be skipped during migration.")
        
        # Recommendations
        print()
        print("💡 RECOMMENDATIONS")
        print("-" * 30)
        
        if stats['filesystem_images'] == 0:
            print("✅ No filesystem images found. Migration not needed!")
        else:
            print("📝 Before migration:")
            print("   1. Ensure your Digital Ocean Spaces credentials are configured")
            print("   2. Run the S3 connection test: python migrate_images_to_s3.py --check-s3")
            print("   3. Always run a dry-run first: python migrate_images_to_s3.py --dry-run")
            print("   4. Consider running during low-traffic hours")
            
            if stats['missing_files'] > 0:
                print()
                print("⚠️  Some filesystem URLs point to missing files.")
                print("   These will be skipped during migration (URLs will remain unchanged).")
            
            if stats['total_file_size'] > 100 * 1024 * 1024:  # > 100MB
                print()
                print("📊 Large migration detected:")
                print("   Consider running in batches if you have many images")
                print("   Monitor your Digital Ocean Spaces usage and costs")
        
        print()
        print("=" * 60)
        
        return stats
        
    except Exception as e:
        print(f"❌ Error analyzing images: {e}")
        return None
    finally:
        db.close()

def main():
    """Main function."""
    print("🔍 Filesystem Image Analysis Tool")
    print("This tool helps you understand what images need to be migrated to S3.\n")
    
    # Check if we're in the right environment
    if not Path('/app/media/property_images').exists():
        print("⚠️  Warning: /app/media/property_images directory not found")
        print("   Are you running this inside the Docker container?")
        print("   Try: docker compose exec backend python check_filesystem_images.py")
    
    stats = analyze_filesystem_images()
    
    if stats and stats['filesystem_images'] > 0:
        print("\n🚀 Next Steps:")
        print("1. Review the analysis above")
        print("2. Set up your Digital Ocean Spaces credentials")
        print("3. Test S3 connection: python migrate_images_to_s3.py --check-s3")
        print("4. Run dry migration: python migrate_images_to_s3.py --dry-run")
        print("5. Perform migration: python migrate_images_to_s3.py --migrate")
        print("\nSee MIGRATION_GUIDE.md for detailed instructions!")

if __name__ == "__main__":
    main() 