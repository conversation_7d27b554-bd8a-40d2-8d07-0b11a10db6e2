#!/usr/bin/env python3
"""
Test script for user listing endpoints.
Run this to verify the user listing functionality is working properly.
"""

import requests
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
TEST_LISTING = {
    "title": "Beautiful Apartment in City Center",
    "price": 350000,
    "size_sqm": 85.5,
    "city": "Casablanca",
    "property_type": "Appartement",
    "property_category": "residential",
    "description_html": "<p>Beautiful 3-bedroom apartment with modern amenities and great city views.</p>",
    "contact_name": "<PERSON>",
    "contact_phone": "+212 6 12 34 56 78",
    "contact_email": "<EMAIL>",
    "images": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg"
    ],
    "neighborhood": "Maarif",
    "rooms": 3,
    "bathrooms": 2,
    "has_parking": True,
    "has_balcony": True,
    "is_furnished": False
}

def test_endpoints(auth_token: str = None):
    """Test all user listing endpoints."""
    headers = {}
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    
    print("🧪 Testing User Listing Endpoints")
    print("=" * 50)
    
    # Test 1: Create a user listing
    print("\n1️⃣ Testing POST /listings/user-listing")
    try:
        response = requests.post(
            f"{BASE_URL}/listings/user-listing",
            json=TEST_LISTING,
            headers=headers
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 201:
            listing_data = response.json()
            listing_id = listing_data["id"]
            print(f"✅ Created listing with ID: {listing_id}")
            print(f"   URL: {listing_data.get('url')}")
            print(f"   Title: {listing_data.get('title')}")
        else:
            print(f"❌ Failed to create listing: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error creating listing: {e}")
        return None
    
    # Test 2: Get user's listings
    print("\n2️⃣ Testing GET /listings/my-listings")
    try:
        response = requests.get(
            f"{BASE_URL}/listings/my-listings",
            headers=headers
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {data.get('total', 0)} user listings")
            print(f"   Page: {data.get('page')}/{data.get('pages')}")
        else:
            print(f"❌ Failed to get user listings: {response.text}")
    except Exception as e:
        print(f"❌ Error getting user listings: {e}")
    
    # Test 3: Get user listing stats
    print("\n3️⃣ Testing GET /listings/my-listings/stats")
    try:
        response = requests.get(
            f"{BASE_URL}/listings/my-listings/stats",
            headers=headers
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ User listing statistics:")
            print(f"   Total listings: {stats.get('total_listings')}")
            print(f"   Total views: {stats.get('total_views')}")
            print(f"   Total bookmarks: {stats.get('total_bookmarks')}")
            print(f"   Average price: {stats.get('average_price'):.2f}")
        else:
            print(f"❌ Failed to get stats: {response.text}")
    except Exception as e:
        print(f"❌ Error getting stats: {e}")
    
    # Test 4: Update the listing
    if 'listing_id' in locals():
        print(f"\n4️⃣ Testing PUT /listings/{listing_id}")
        try:
            update_data = {
                "title": "Updated Beautiful Apartment in City Center",
                "price": 375000
            }
            response = requests.put(
                f"{BASE_URL}/listings/{listing_id}",
                json=update_data,
                headers=headers
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                updated_listing = response.json()
                print(f"✅ Updated listing successfully")
                print(f"   New title: {updated_listing.get('title')}")
                print(f"   New price: {updated_listing.get('price')}")
            else:
                print(f"❌ Failed to update listing: {response.text}")
        except Exception as e:
            print(f"❌ Error updating listing: {e}")
    
    # Test 5: Get specific listing
    if 'listing_id' in locals():
        print(f"\n5️⃣ Testing GET /listings/{listing_id}")
        try:
            response = requests.get(
                f"{BASE_URL}/listings/{listing_id}",
                headers=headers
            )
            print(f"Status: {response.status_code}")
            if response.status_code == 200:
                listing = response.json()
                print(f"✅ Retrieved listing successfully")
                print(f"   Title: {listing.get('title')}")
                print(f"   Views (24h): {listing.get('view_count_24h', 0)}")
                print(f"   Is bookmarked: {listing.get('is_bookmarked', False)}")
            else:
                print(f"❌ Failed to get listing: {response.text}")
        except Exception as e:
            print(f"❌ Error getting listing: {e}")
    
    print(f"\n🎯 Testing completed!")
    print(f"Note: Some tests may fail if authentication is not properly configured.")
    
    return listing_id if 'listing_id' in locals() else None

def test_without_auth():
    """Test endpoints without authentication to see error handling."""
    print("\n🔒 Testing without authentication")
    print("=" * 40)
    
    response = requests.post(f"{BASE_URL}/listings/user-listing", json=TEST_LISTING)
    print(f"POST /listings/user-listing without auth: {response.status_code}")
    if response.status_code == 401:
        print("✅ Correctly requires authentication")
    else:
        print(f"❌ Expected 401, got {response.status_code}")

if __name__ == "__main__":
    print("🚀 User Listing API Test Suite")
    print("=" * 60)
    
    # Test without authentication first
    test_without_auth()
    
    # Test with authentication (you'll need to provide a valid token)
    print("\n" + "=" * 60)
    print("To test with authentication, you need a valid Clerk JWT token.")
    print("You can get one by:")
    print("1. Logging into your frontend application")
    print("2. Opening browser dev tools")
    print("3. Checking the Authorization header in network requests")
    print("4. Copy the token and set it as AUTH_TOKEN environment variable")
    print("=" * 60)
    
    import os
    auth_token = os.getenv("AUTH_TOKEN")
    if auth_token:
        print(f"\n🔐 Found auth token, testing with authentication...")
        test_endpoints(auth_token)
    else:
        print(f"\n⚠️ No AUTH_TOKEN environment variable found.")
        print(f"Testing without authentication (will show auth errors)...")
        test_endpoints() 