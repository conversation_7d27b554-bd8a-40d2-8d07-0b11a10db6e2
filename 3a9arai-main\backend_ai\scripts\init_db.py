#!/usr/bin/env python3
"""
Database initialization script.
This script creates all tables and initializes the database with default data.
"""

import os
import sys
import logging

# Add the parent directory to the path so we can import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.exc import SQLAlchemyError
from app.db.session import Base, engine, get_db
from app.db.init_db import init_db

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """
    Main function to initialize the database.
    """
    try:
        # Create all tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully.")
        
        # Initialize database with default data
        logger.info("Initializing database with default data...")
        db = next(get_db())
        init_db(db)
        logger.info("Database initialized successfully.")
        
    except SQLAlchemyError as e:
        logger.error(f"Error initializing database: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 