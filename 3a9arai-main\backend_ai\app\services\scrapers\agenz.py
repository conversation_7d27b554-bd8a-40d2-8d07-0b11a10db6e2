"""Agenz real estate scraper implementation."""
import logging
import json
import time
import os
import requests
from typing import List, Dict, Any, Optional
from lxml import etree
from bs4 import BeautifulSoup
from app.services.scrapers.base_scraper import BaseScraper, redis_client
from app.services.scrapers.llm_service import process_listing_text
from app.models.listing import Listing
from app.db.session import SessionLocal
from app.services.scrapers.selectors import AGENZ_SELECTORS, COMMON_SELECTORS
from app.services.scrapers.redis_utils import (
    get_next_page_url, get_last_page, set_last_page, add_next_page_url,
    acquire_url_collection_lock, release_url_collection_lock, set_url_collection_completed,
    is_url_collection_completed, add_to_url_queue, get_next_from_url_queue,
    add_to_details_queue, get_next_from_details_queue,
    add_to_processed_queue, get_next_from_processed_queue,
    check_control_queue
)
import re
logger = logging.getLogger(__name__)

class AgenzScraper(BaseScraper):
    """Scraper for Agenz real estate website."""
    
    def __init__(self, db):
        """Initialize the Agenz scraper.
        
        Args:
            db: Database session
        """
        super().__init__(db, "agenz")
        self.base_url = "https://agenz.ma"
        self.search_url = f"{self.base_url}/fr/acheter"
        self.selectors = AGENZ_SELECTORS
        self.wait_timeout = COMMON_SELECTORS["wait_timeout"]
        
        # Request headers to mimic a browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.5'
        }
    
    def _get_soup_and_dom(self, url: str) -> tuple:
        """Get BeautifulSoup and lxml DOM from a URL.
        
        Args:
            url: URL to fetch
            
        Returns:
            Tuple of (BeautifulSoup object, lxml DOM)
        """
        try:
            response = requests.get(url, headers=self.headers, timeout=self.wait_timeout)
            if response.status_code != 200:
                logger.error(f"Failed to get HTML from {url}: Status code {response.status_code}")
                return None, None
                
            soup = BeautifulSoup(response.content, "html.parser")
            dom = etree.HTML(str(soup))
            return soup, dom
        except Exception as e:
            logger.error(f"Error fetching {url}: {str(e)}")
            return None, None
    
    def collect_listing_urls(self) -> List[str]:
        """Collect listing URLs from a single page of Agenz website.
        
        Returns:
            List of listing URLs from the current page
        """
        # Check if we've marked URL collection as completed
        if is_url_collection_completed("agenz"):
            logger.info("URL collection has been marked as completed. No more URLs to collect.")
            return []
            
        # First check if the scraper should continue running
        if not self.should_continue():
            logger.info("Scraper is set to stop, aborting URL collection.")
            return []
            
        # Try to acquire lock
        success, lock_value = acquire_url_collection_lock("agenz")
        if not success:
            logger.info("Another worker is already collecting URLs")
            return []
            
        try:
            # Get the next page URL from the queue or use the default starting URL
            next_page_url = get_next_page_url("agenz")
            
            # Get the last processed page from Redis
            current_page = get_last_page("agenz")
            
            # If we have a next page URL from the queue, use it
            if next_page_url:
                page_url = next_page_url
                logger.info(f"Processing next page URL from queue: {page_url}")
            else:
                # Otherwise use the default URL with the current page number
                page_url = f"{self.search_url}?page={current_page}"
                logger.info(f"No next page URL in queue, using constructed URL: {page_url}")
            
            try:
                # Refresh lock to prevent expiration during long-running tasks
                success, _ = acquire_url_collection_lock("agenz")
                
                logger.info(f"Processing page {current_page}: {page_url}")
                
                # Get URLs from the current page
                page_urls = self._get_listings_urls(page_url)
                
                if not page_urls:
                    logger.info(f"No URLs found on page {current_page}")
                    # Mark URL collection as completed since no URLs were found
                    set_url_collection_completed("agenz")
                    # Update scraper status
                    from app.api.endpoints.scrapers import set_scraper_status
                    set_scraper_status("agenz", "url_collection_completed", 
                                      message=f"URL collection completed at page {current_page}",
                                      last_page=current_page)
                    return []
                
                # Add new URLs to the queue
                new_urls_count = 0
                for url in page_urls:
                    if url not in self.processed_urls and url and len(url.strip()) > 0:
                        # Log the URL being added
                        logger.info(f"Adding URL to queue: {url}")
                        add_to_url_queue("agenz", url)
                        self.processed_urls.add(url)
                        new_urls_count += 1
                
                logger.info(f"Collected {new_urls_count} new URLs from page {current_page}")
                
                # Store the current page in Redis
                set_last_page("agenz", current_page)
                
                # Find and queue the next page URL
                self._queue_next_page_url(page_url, current_page)
                
                return page_urls
                
            except Exception as e:
                logger.error(f"Error collecting URLs from page {current_page}: {str(e)}")
                return []
                
        finally:
            # Release the lock we acquired
            release_url_collection_lock("agenz", lock_value)
    
    def _queue_next_page_url(self, page_url: str, current_page: int) -> bool:
        """Find and queue the next page URL.
        
        Args:
            page_url: Current page URL
            current_page: Current page number
            
        Returns:
            True if next page URL was queued, False otherwise
        """
        try:
            # Check if the scraper should continue running
            if not self.should_continue():
                logger.info("Scraper is set to stop, not queueing next page URL")
                return False
            
            # Get soup and dom
            _, dom = self._get_soup_and_dom(page_url)
            if dom is None:
                logger.error(f"Failed to get DOM for {page_url}")
                return False
                
            # Check if the Next button exists
            next_button = dom.xpath(self.selectors["next_button"])
            if not next_button:
                logger.info(f"No Next button found on page {current_page} - reached end of pagination")
                # Mark URL collection as completed
                set_url_collection_completed("agenz")
                # Update scraper status
                from app.api.endpoints.scrapers import set_scraper_status
                set_scraper_status("agenz", "url_collection_completed", 
                                  message=f"URL collection completed at page {current_page} (reached end of pagination)",
                                  last_page=current_page)
                return False
            
            # Get the parent element that contains the href for the next page
            next_link = next_button[0].xpath("./ancestor::a")
            if next_link:
                next_page_url = f"{self.base_url}{next_link[0].get('href')}"
                if next_page_url:
                    logger.info(f"Found next page URL: {next_page_url}")
                    # Add the next page URL to a dedicated queue
                    add_next_page_url("agenz", next_page_url)
                    # Update the current page counter
                    next_page = current_page + 1
                    set_last_page("agenz", next_page)
                    logger.info(f"Queued next page URL {next_page_url} for page {next_page}")
                    return True
                else:
                    logger.warning("Next button found but no href attribute")
            else:
                logger.warning("Next button found but no parent link element")
            
            return False
                
        except Exception as e:
            logger.error(f"Error queueing next page URL: {str(e)}")
            return False
    
    def extract_listing_html(self, url: str) -> str:
        """Extract HTML content from a listing URL.
        
        Args:
            url: URL of the listing
            
        Returns:
            HTML content of the listing page
        """
        try:
            logger.info(f"Getting HTML from {url}")
            soup, dom = self._get_soup_and_dom(url)
            if dom is None:
                return ""
            
            # Find listing details using XPath
            listing_details = dom.xpath(self.selectors["listing_details"])
            if not listing_details:
                logger.error(f"No listing details found at {url}")
                return ""
                
            # Convert the element back to HTML string
            listing_html = etree.tostring(listing_details[0], encoding='unicode')
            #remove all script tags and their content
            listing_html = re.sub(r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>', '', listing_html)
            return listing_html
        except Exception as e:
            logger.error(f"Error getting HTML from {url}: {str(e)}")
            return ""

    def extract_listing_details(self, url: str) -> Dict[str, Any]:
        """Extract details and images from a listing URL.
        
        Args:
            url: URL of the listing
            
        Returns:
            Dictionary containing HTML content and image URLs
        """
        try:
            url = f"{url}"
            logger.info(f"Getting details from {url}")
            
            # Get soup and dom for extracting both content and images
            soup, dom = self._get_soup_and_dom(url)
            if dom is None or soup is None:
                return {}
            
            # Find listing details using XPath
            listing_details_xpath = dom.xpath(self.selectors["listing_details"])
            if not listing_details_xpath:
                logger.error(f"No listing details found at {url}")
                return {}
            
            # Get the HTML and create a BeautifulSoup object for cleaning
            listing_html = etree.tostring(listing_details_xpath[0], encoding='unicode')
            listing_details = BeautifulSoup(listing_html, 'html.parser')
            
            # Clean up the HTML content
            for script in listing_details.find_all('script'):
                script.decompose()
            
            # Remove all SVG elements
            for svg in listing_details.find_all('svg'):
                svg.decompose()
            
            # Remove all img elements
            for img in listing_details.find_all('img'):
                img.decompose()
            
            
            # Remove all elements with tag names starting with 'astro'
            for astro_tag in listing_details.find_all(lambda tag: tag.name and tag.name.startswith('astro')):
                astro_tag.decompose()
            
            # Remove all style attributes
            for tag in listing_details.find_all():
                if tag.has_attr('style'):
                    del tag['style']
            
            # Remove all class attributes
            for tag in listing_details.find_all():
                if tag.has_attr('class'):
                    del tag['class']
            
            # Remove all href attributes
            for tag in listing_details.find_all():
                if tag.has_attr('href'):
                    del tag['href']
            
            # Compress tag names to their first letter only
            for tag in listing_details.find_all():
                if tag.name:
                    first_letter = tag.name[0] if tag.name else ''
                    if first_letter.isalpha():
                        tag.name = first_letter.lower()
            
            # Remove all a tags
            for a_tag in listing_details.find_all('a'):
                a_tag.decompose()
            
            # Convert back to string
            cleaned_html = str(listing_details)
            
            # Get image URLs
            image_urls = self._get_listing_images(dom)
            # Remove None values
            image_urls = [url for url in image_urls if url is not None]
            logger.info(f"Image URLs: {image_urls}")
            
            return {
                "url": url,
                "property_details": cleaned_html,
                "images": image_urls,
                "html": cleaned_html
            }
        except Exception as e:
            logger.error(f"Error extracting details from {url}: {str(e)}")
            return {}
    
    def _get_listing_details_html(self, url: str) -> str:
        """Get HTML content of a listing page.
        
        Args:
            url: URL of the listing
            
        Returns:
            HTML content of the listing page
        """
        # This is now just an alias for extract_listing_html for compatibility
        return self.extract_listing_html(url)
    
    def _get_listing_images(self, dom) -> List[str]:
        """Get image URLs from a listing page.
        
        Args:
            dom: lxml DOM of the listing page
            
        Returns:
            List of image URLs
        """
        try:
            # Find all image elements
            image_elements = dom.xpath(self.selectors["image_item"])
            logger.info(f"Found {len(image_elements)} image elements")
            
            image_urls = []
            
            for element in image_elements:
                try:
                    # Find the img tag inside the image element and get its src attribute
                    img_tags = element.xpath(".//img")
                    if img_tags:
                        image_url = img_tags[0].get("src")
                        # Handle various image types (jpg, jpeg, png, webp)
                        for ext in ['.jpg', '.jpeg', '.png', '.webp']:
                            if ext in image_url:
                                image_url = image_url.split(ext)[0] + ext
                                break
                        logger.info(f"Found image URL: {self.base_url}{image_url}")
                        
                        if image_url is not None:  # Only add non-None URLs
                            image_urls.append(f"{self.base_url}{image_url}")
                except Exception as e:
                    logger.error(f"Error getting image URL: {str(e)}")
                    continue
            
            logger.info(f"Found {len(image_urls)} images")
            logger.info(f"Image URLs: {image_urls}")
            
            return image_urls
            
        except Exception as e:
            logger.error(f"Error getting images: {str(e)}")
            return []
    
    def _get_listings_urls(self, page_url: str) -> List[str]:
        """Get listing URLs from a search page.
        
        Args:
            page_url: URL of the search page
            
        Returns:
            List of listing URLs
        """
        try:
            # Get soup and dom
            soup, dom = self._get_soup_and_dom(page_url)
            if dom is None:
                return []
            
            try:
                # Check if scroll container exists
                scroll_container = dom.xpath(self.selectors["scroll_container"])
                if not scroll_container:
                    logger.info("Could not find scroll container - likely reached end of listings")
                    return []
                logger.info("Scroll container found")
            except Exception as e:
                logger.info("Could not find scroll container - likely reached end of listings")
                return []
            
            # Find all listing cards with the appropriate XPath
            listing_cards = dom.xpath(self.selectors["listing_card"])
            logger.info(f"Found {len(listing_cards)} listing cards")
            
            if not listing_cards:
                logger.info("No listing cards found - likely reached end of listings")
                return []
            
            urls = []
            for card in listing_cards:
                try:
                    # Get the data_url attribute
                    url = card.get(self.selectors["listing_card_data_url"].split('@')[-1])
                    if url:
                        # Ensure the URL is absolute
                        if not url.startswith("http"):
                            url = f"{self.base_url}{url}"
                        urls.append(url)
                        logger.debug(f"Found URL: {url}")
                except Exception as e:
                    logger.error(f"Error getting URL from listing card: {str(e)}")
                    continue
            
            if not urls:
                logger.info("No valid URLs found - likely reached end of listings")
                return []
            
            logger.info(f"Found {len(urls)} URLs on page {page_url}")
            return urls
            
        except Exception as e:
            logger.error(f"Error getting URLs from {page_url}: {str(e)}")
            return []
    
    def process_listing_with_llm(self, url: str, html: str, scraper_name: str, images: List[str]) -> Optional[Dict[str, Any]]:
        """Process listing HTML with LLM.
        
        Args:
            url: URL of the listing
            html: HTML content of the listing
            scraper_name: Name of the scraper
            images: List of image URLs
            
        Returns:
            Processed listing data or None if processing failed
        """
        listing_data = process_listing_text(html, scraper_name, url, images)                
        return listing_data
    
    def save_listing(self, listing_data) -> bool:
        """Save a listing to the database.
        
        Args:
            listing_data: Listing data to save
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            db = SessionLocal()
            
            try:
                logger.info(f"Saving listing: {listing_data}")
                url = listing_data.url
                if not url:
                    logger.error("Invalid listing data: missing URL")
                    return False
                
                # Check if listing already exists
                existing_listing = db.query(Listing).filter(Listing.url == url).first()
                
                # Clean the data
                clean_data = {}
                for key, value in listing_data.__dict__.items():
                    if key in ['id', 'scrape_date']:
                        continue
                    if key == 'source_website':
                        clean_data['website_source'] = value
                    else:
                        clean_data[key] = value
                
                # Scraper-generated listings are auto-approved
                clean_data['approval_status'] = 'approved'
                clean_data['is_user_generated'] = False
                
                if existing_listing:
                    logger.info(f"Updating existing listing with URL: {url}")
                    for key, value in clean_data.items():
                        if hasattr(existing_listing, key):
                            setattr(existing_listing, key, value)
                else:
                    logger.info(f"Creating new listing with URL: {url}")
                    db_listing = Listing(**clean_data)
                    logger.info(f"Adding listing to database: {db_listing}")
                    db.add(db_listing)
                
                db.commit()
                logger.info(f"Successfully saved listing for {url}")
                return True
                
            except Exception as e:
                logger.error(f"Error saving listing: {str(e)}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                db.rollback()
                return False
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error in save_listing: {str(e)}")
            return False
    
    def scrape(self) -> int:
        """Main scraping method for Agenz.
        
        Returns:
            Number of listings processed
        """
        try:
            if not self.start():
                logger.error("Failed to start Agenz scraper")
                return 0
            
            processed_count = 0
            
            # Collect initial URLs
            self.collect_listing_urls()
            
            while self.is_running and not self.should_stop:
                # Check for control commands
                command = check_control_queue("agenz")
                if command:
                    if command.get("command") == "stop":
                        self.should_stop = True
                        self.is_running = False
                        break
                
                # Process URLs from the queue
                url = get_next_from_url_queue("agenz")
                if url and url not in self.processed_urls:
                    try:
                        # Extract listing details including HTML and images
                        listing_details = self.extract_listing_details(url)
                        if listing_details and 'html' in listing_details:
                            html = listing_details['html']
                            images = listing_details.get('images', [])
                            
                            # Filter out None values from images
                            images = [img for img in images if img is not None]
                            
                            # Process with LLM
                            listing_data = self.process_listing_with_llm(url, html, "agenz", images)
                            if listing_data:
                                # Add to processed queue
                                add_to_processed_queue("agenz", listing_data)
                                
                                # Save to database
                                if self.save_listing(listing_data):
                                    processed_count += 1
                                    self.log_activity("running", f"Processed listing {url}", processed_count)
                            else:
                                # If LLM processing failed, requeue the detail data for later processing
                                logger.warning(f"LLM processing failed for {url}, requeueing for later processing")
                                # Add to details queue
                                add_to_details_queue("agenz", url, html, images)
                        else:
                            # If extracting details failed, requeue the URL 
                            logger.warning(f"Failed to extract details for {url}, requeueing for later processing")
                            add_to_url_queue("agenz", url)
                        
                    except Exception as e:
                        logger.error(f"Error processing URL {url}: {str(e)}")
                        # Requeue the URL
                        add_to_url_queue("agenz", url)
                
                # Process details from the queue
                details_data = get_next_from_details_queue("agenz")
                if details_data:
                    try:
                        url = details_data.get("url")
                        html = details_data.get("html")
                        images = details_data.get("images", [])
                        
                        # Filter out None values from images
                        images = [img for img in images if img is not None]
                        
                        if url and html:
                            # Process with LLM
                            listing_data = self.process_listing_with_llm(url, html, "agenz", images)
                            if listing_data:
                                # Add to processed queue
                                add_to_processed_queue("agenz", listing_data)
                                
                                # Save to database
                                if self.save_listing(listing_data):
                                    processed_count += 1
                                    self.log_activity("running", f"Processed listing {url}", processed_count)
                            else:
                                # If LLM processing failed, requeue it
                                logger.warning(f"LLM processing failed for {url}, requeueing for later processing")
                                add_to_details_queue("agenz", url, html, images)
                        else:
                            # Missing required data, log and discard
                            logger.error("Details missing required data (url or html), discarding")
                    except Exception as e:
                        logger.error(f"Error processing details: {str(e)}")
                        # Requeue on any exception
                        if details_data and "url" in details_data and "html" in details_data:
                            add_to_details_queue(
                                "agenz", 
                                details_data["url"], 
                                details_data["html"], 
                                details_data.get("images", []),
                                details_data["html"]
                            )
                
                # Process items from the processed queue
                processed_data = get_next_from_processed_queue("agenz")
                if processed_data:
                    try:
                        # Convert dict to object with attributes for save_listing
                        class ListingObject:
                            def __init__(self, **kwargs):
                                for key, value in kwargs.items():
                                    setattr(self, key, value)
                        
                        listing_obj = ListingObject(**processed_data)
                        if self.save_listing(listing_obj):
                            processed_count += 1
                            self.log_activity("running", f"Saved listing {processed_data.get('url')}", processed_count)
                        else:
                            # If saving failed, add back to the processed queue
                            logger.warning("Failed to save processed listing, requeueing for later")
                            add_to_processed_queue("agenz", processed_data)
                    except Exception as e:
                        logger.error(f"Error saving processed listing: {str(e)}")
                        # Requeue on any exception
                        if processed_data:
                            add_to_processed_queue("agenz", processed_data)
                
                # Sleep briefly to prevent CPU overload
                time.sleep(1)
            
            # Log completion
            self.log_activity("success", f"Completed scraping {processed_count} listings", processed_count)
            return processed_count
            
        except Exception as e:
            logger.error(f"Error in Agenz scraper: {str(e)}")
            self.log_activity("error", str(e))
            return processed_count
            
        finally:
            self.stop()