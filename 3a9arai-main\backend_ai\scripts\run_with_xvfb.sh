#!/bin/bash
# Start Xvfb with a virtual display
echo "Starting Xvfb..."
Xvfb :99 -screen 0 1280x1024x24 &
xvfb_pid=$!

# Wait for Xvfb to initialize
sleep 2
export DISPLAY=:99

# Check if Xvfb is running
if ! ps -p $xvfb_pid > /dev/null; then
    echo "Error: Xvfb failed to start"
    exit 1
else
    echo "Xvfb started successfully with PID $xvfb_pid"
    echo "DISPLAY set to $DISPLAY"
fi

# Execute the command passed to this script
echo "Running command: $@"
exec "$@" 