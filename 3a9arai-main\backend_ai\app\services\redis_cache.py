"""Redis cache service for optimizing database operations."""
import json
import logging
import os
from typing import List, Optional, Set, Tuple
import redis
from app.services.scrapers.redis_utils import redis_client

logger = logging.getLogger(__name__)

class BookmarkCacheService:
    """Redis cache service for bookmark operations to improve performance."""
    
    def __init__(self):
        """Initialize the bookmark cache service."""
        self.redis = redis_client
        self.cache_ttl = 3600  # 1 hour TTL for bookmark cache
        self.user_bookmarks_ttl = 1800  # 30 minutes TTL for user bookmark lists
        
    def _get_bookmark_key(self, user_id: int, listing_id: int) -> str:
        """Generate cache key for a specific bookmark."""
        return f"bookmark:{user_id}:{listing_id}"
    
    def _get_user_bookmarks_key(self, user_id: int) -> str:
        """Generate cache key for user's bookmark list."""
        return f"user_bookmarks:{user_id}"
    
    def _get_user_bookmarks_count_key(self, user_id: int) -> str:
        """Generate cache key for user's bookmark count."""
        return f"user_bookmarks_count:{user_id}"
    
    def is_bookmarked(self, user_id: int, listing_id: int) -> Optional[bool]:
        """Check if a listing is bookmarked by a user (from cache).
        
        Args:
            user_id: ID of the user
            listing_id: ID of the listing
            
        Returns:
            True if bookmarked, False if not bookmarked, None if not in cache
        """
        try:
            key = self._get_bookmark_key(user_id, listing_id)
            cached_value = self.redis.get(key)
            
            if cached_value is not None:
                # Cache hit - return boolean value
                return cached_value.decode('utf-8') == 'true'
            
            # Cache miss
            return None
            
        except Exception as e:
            logger.error(f"Error checking bookmark cache for user {user_id}, listing {listing_id}: {str(e)}")
            return None
    
    def set_bookmark_status(self, user_id: int, listing_id: int, is_bookmarked: bool):
        """Set bookmark status in cache.
        
        Args:
            user_id: ID of the user
            listing_id: ID of the listing
            is_bookmarked: Whether the listing is bookmarked
        """
        try:
            key = self._get_bookmark_key(user_id, listing_id)
            value = 'true' if is_bookmarked else 'false'
            self.redis.setex(key, self.cache_ttl, value)
            logger.debug(f"Set bookmark cache for user {user_id}, listing {listing_id}: {is_bookmarked}")
            
        except Exception as e:
            logger.error(f"Error setting bookmark cache for user {user_id}, listing {listing_id}: {str(e)}")
    
    def add_bookmark_to_cache(self, user_id: int, listing_id: int):
        """Add a bookmark to cache and invalidate user's bookmark list.
        
        Args:
            user_id: ID of the user
            listing_id: ID of the listing
        """
        try:
            # Set individual bookmark cache
            self.set_bookmark_status(user_id, listing_id, True)
            
            # Invalidate user's bookmark list cache since it changed
            self.invalidate_user_bookmark_list(user_id)
            
            logger.debug(f"Added bookmark to cache for user {user_id}, listing {listing_id}")
            
        except Exception as e:
            logger.error(f"Error adding bookmark to cache for user {user_id}, listing {listing_id}: {str(e)}")
    
    def remove_bookmark_from_cache(self, user_id: int, listing_id: int):
        """Remove a bookmark from cache and invalidate user's bookmark list.
        
        Args:
            user_id: ID of the user
            listing_id: ID of the listing
        """
        try:
            # Set individual bookmark cache to false
            self.set_bookmark_status(user_id, listing_id, False)
            
            # Invalidate user's bookmark list cache since it changed
            self.invalidate_user_bookmark_list(user_id)
            
            logger.debug(f"Removed bookmark from cache for user {user_id}, listing {listing_id}")
            
        except Exception as e:
            logger.error(f"Error removing bookmark from cache for user {user_id}, listing {listing_id}: {str(e)}")
    
    def get_user_bookmarked_listing_ids(self, user_id: int) -> Optional[Set[int]]:
        """Get cached set of listing IDs bookmarked by a user.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Set of listing IDs if cached, None if not in cache
        """
        try:
            key = self._get_user_bookmarks_key(user_id)
            cached_data = self.redis.get(key)
            
            if cached_data is not None:
                listing_ids = json.loads(cached_data.decode('utf-8'))
                return set(listing_ids)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user bookmark list from cache for user {user_id}: {str(e)}")
            return None
    
    def set_user_bookmarked_listing_ids(self, user_id: int, listing_ids: List[int]):
        """Cache user's bookmarked listing IDs.
        
        Args:
            user_id: ID of the user
            listing_ids: List of bookmarked listing IDs
        """
        try:
            key = self._get_user_bookmarks_key(user_id)
            data = json.dumps(listing_ids)
            self.redis.setex(key, self.user_bookmarks_ttl, data)
            
            # Also cache the count
            count_key = self._get_user_bookmarks_count_key(user_id)
            self.redis.setex(count_key, self.user_bookmarks_ttl, len(listing_ids))
            
            logger.debug(f"Cached {len(listing_ids)} bookmark IDs for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error caching user bookmark list for user {user_id}: {str(e)}")
    
    def get_user_bookmarks_count(self, user_id: int) -> Optional[int]:
        """Get cached count of user's bookmarks.
        
        Args:
            user_id: ID of the user
            
        Returns:
            Count of bookmarks if cached, None if not in cache
        """
        try:
            key = self._get_user_bookmarks_count_key(user_id)
            cached_count = self.redis.get(key)
            
            if cached_count is not None:
                return int(cached_count.decode('utf-8'))
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user bookmark count from cache for user {user_id}: {str(e)}")
            return None
    
    def invalidate_user_bookmark_list(self, user_id: int):
        """Invalidate user's bookmark list cache.
        
        Args:
            user_id: ID of the user
        """
        try:
            # Delete both the list and count cache
            list_key = self._get_user_bookmarks_key(user_id)
            count_key = self._get_user_bookmarks_count_key(user_id)
            
            self.redis.delete(list_key, count_key)
            logger.debug(f"Invalidated bookmark list cache for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error invalidating bookmark list cache for user {user_id}: {str(e)}")
    
    def invalidate_bookmark_cache(self, user_id: int, listing_id: int):
        """Invalidate specific bookmark cache.
        
        Args:
            user_id: ID of the user
            listing_id: ID of the listing
        """
        try:
            key = self._get_bookmark_key(user_id, listing_id)
            self.redis.delete(key)
            logger.debug(f"Invalidated bookmark cache for user {user_id}, listing {listing_id}")
            
        except Exception as e:
            logger.error(f"Error invalidating bookmark cache for user {user_id}, listing {listing_id}: {str(e)}")
    
    def invalidate_all_user_caches(self, user_id: int):
        """Invalidate all bookmark caches for a user.
        
        Args:
            user_id: ID of the user
        """
        try:
            # Pattern to match all bookmark keys for the user
            pattern = f"bookmark:{user_id}:*"
            keys = self.redis.keys(pattern)
            
            # Also get the user bookmark list keys
            list_key = self._get_user_bookmarks_key(user_id)
            count_key = self._get_user_bookmarks_count_key(user_id)
            
            all_keys = keys + [list_key, count_key]
            
            if all_keys:
                self.redis.delete(*all_keys)
                logger.debug(f"Invalidated {len(all_keys)} bookmark cache keys for user {user_id}")
            
        except Exception as e:
            logger.error(f"Error invalidating all bookmark caches for user {user_id}: {str(e)}")
    
    def warm_cache_for_user(self, user_id: int, bookmarks_data: List[Tuple[int, bool]]):
        """Warm up the cache with bookmark data for a user.
        
        Args:
            user_id: ID of the user  
            bookmarks_data: List of (listing_id, is_bookmarked) tuples
        """
        try:
            bookmarked_ids = []
            
            # Cache individual bookmark statuses
            for listing_id, is_bookmarked in bookmarks_data:
                self.set_bookmark_status(user_id, listing_id, is_bookmarked)
                if is_bookmarked:
                    bookmarked_ids.append(listing_id)
            
            # Cache the user's bookmark list
            self.set_user_bookmarked_listing_ids(user_id, bookmarked_ids)
            
            logger.debug(f"Warmed cache for user {user_id} with {len(bookmarks_data)} bookmark entries")
            
        except Exception as e:
            logger.error(f"Error warming cache for user {user_id}: {str(e)}")


# Global instance
bookmark_cache = BookmarkCacheService() 