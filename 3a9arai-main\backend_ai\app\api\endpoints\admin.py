from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import requests
import logging

from app.api.deps import get_db, get_current_user_from_clerk, get_current_admin_user
from app.models.user import User
from app.models.listing import Listing
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/me")
async def get_admin_status(
    current_user: User = Depends(get_current_user_from_clerk),
) -> Dict[str, Any]:
    """
    Get current user's admin status (for testing the sync functionality).
    """
    return {
        "user_id": current_user.id,
        "email": current_user.email,
        "clerk_user_id": current_user.clerk_user_id,
        "is_admin": current_user.is_superuser,
        "is_active": current_user.is_active
    }

@router.post("/users/{clerk_user_id}/make-admin")
async def make_user_admin(
    clerk_user_id: str,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    Make a user admin by updating their Clerk metadata.
    Only existing admins can perform this action.
    """
    try:
        # Find the user in our database
        user = db.query(User).filter(User.clerk_user_id == clerk_user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user metadata in Clerk
        if settings.CLERK_SECRET_KEY:
            clerk_api_url = f"https://api.clerk.dev/v1/users/{clerk_user_id}"
            headers = {
                "Authorization": f"Bearer {settings.CLERK_SECRET_KEY}",
                "Content-Type": "application/json"
            }
            data = {"public_metadata": {"isAdmin": True}}
            
            response = requests.patch(clerk_api_url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # Update database immediately
                user.is_superuser = True
                db.commit()
                logger.info(f"Admin {current_admin.email} made {user.email} an admin")
                
                return {
                    "message": f"User {user.email} is now an admin",
                    "success": True
                }
            else:
                logger.error(f"Failed to update Clerk metadata: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update admin status in Clerk"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Clerk secret key not configured"
            )
        
    except requests.RequestException as e:
        logger.error(f"Error calling Clerk API: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to communicate with Clerk API"
        )
    except Exception as e:
        logger.error(f"Error making user admin: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update admin status"
        )

@router.post("/users/{clerk_user_id}/remove-admin")
async def remove_user_admin(
    clerk_user_id: str,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    Remove admin status from a user by updating their Clerk metadata.
    Only existing admins can perform this action.
    """
    try:
        # Find the user in our database
        user = db.query(User).filter(User.clerk_user_id == clerk_user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Prevent removing admin from self
        if user.id == current_admin.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove admin status from yourself"
            )
        
        # Update user metadata in Clerk
        if settings.CLERK_SECRET_KEY:
            clerk_api_url = f"https://api.clerk.dev/v1/users/{clerk_user_id}"
            headers = {
                "Authorization": f"Bearer {settings.CLERK_SECRET_KEY}",
                "Content-Type": "application/json"
            }
            data = {"public_metadata": {"isAdmin": False}}
            
            response = requests.patch(clerk_api_url, json=data, headers=headers, timeout=10)
            
            if response.status_code == 200:
                # Update database immediately
                user.is_superuser = False
                db.commit()
                logger.info(f"Admin {current_admin.email} removed admin status from {user.email}")
                
                return {
                    "message": f"Admin status removed from {user.email}",
                    "success": True
                }
            else:
                logger.error(f"Failed to update Clerk metadata: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update admin status in Clerk"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Clerk secret key not configured"
            )
        
    except requests.RequestException as e:
        logger.error(f"Error calling Clerk API: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to communicate with Clerk API"
        )
    except Exception as e:
        logger.error(f"Error removing user admin: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update admin status"
        )

@router.get("/users")
async def list_users(
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
):
    """List all users (admin only)."""
    users = db.query(User).all()
    return [
        {
            "id": user.id,
            "email": user.email,
            "clerk_user_id": user.clerk_user_id,
            "full_name": user.full_name,
            "is_active": user.is_active,
            "is_admin": user.is_superuser,
            "created_at": user.created_at
        }
        for user in users
    ]

@router.post("/fix-property-categories")
async def fix_property_categories(
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db),
) -> Dict[str, Any]:
    """
    Fix property categories based on property types.
    Maps property_type values to appropriate property_category values.
    Only admins can perform this action.
    """
    try:
        # Define the mapping from property_type to property_category
        property_type_to_category = {
            "appartement": "residential",
            "maison": "residential", 
            "residence": "residential",
            "terrain": "land",
            "bureau": "commercial",
            "local commercial": "commercial",
            # "autre" will keep its existing category or default to residential if none set
        }
        
        updated_count = 0
        total_listings = db.query(Listing).count()
        
        # Process listings in batches to avoid memory issues
        batch_size = 1000
        offset = 0
        
        while True:
            listings = db.query(Listing).offset(offset).limit(batch_size).all()
            if not listings:
                break
                
            for listing in listings:
                old_category = listing.property_category
                property_type = listing.property_type
                
                # Determine new category based on property type
                if property_type in property_type_to_category:
                    new_category = property_type_to_category[property_type]
                elif property_type == "autre":
                    # For "autre", keep existing category if valid, otherwise default to residential
                    if old_category not in ["residential", "commercial", "land"]:
                        new_category = "residential"
                    else:
                        new_category = old_category
                else:
                    # Unknown property type, default to residential
                    new_category = "residential"
                
                # Update only if category changed
                if old_category != new_category:
                    listing.property_category = new_category
                    updated_count += 1
            
            db.commit()
            offset += batch_size
        
        logger.info(f"Admin {current_admin.email} fixed property categories for {updated_count} listings")
        
        return {
            "message": f"Successfully updated property categories for {updated_count} out of {total_listings} listings",
            "updated_count": updated_count,
            "total_listings": total_listings,
            "success": True
        }
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error fixing property categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fix property categories"
        ) 