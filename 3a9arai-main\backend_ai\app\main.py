"""Main application file for the FastAPI backend."""
import logging
import os
from typing import Dict, Any, List, Optional, Union
import asyncio
import contextlib
from pathlib import Path
from fastapi import Fast<PERSON><PERSON>, Request, Depends, BackgroundTasks, Response
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2<PERSON>emplates
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from fastapi_utils.tasks import repeat_every

from app.api.api import api_router
from app.core.config import settings
from app.db.session import engine, Base  # Import for migrations
from app.db.session import SessionLocal
from app.worker.tasks.location_tasks import update_location_mappings_task

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="API for WebScraperTracker",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=f"{settings.API_V1_STR}/docs",
    redoc_url=f"{settings.API_V1_STR}/redoc",
    redirect_slashes=False,
)

# Set all CORS enabled origins
if settings.CORS_ORIGINS:
    origins = settings.CORS_ORIGINS.split(",") if settings.CORS_ORIGINS else ["http://localhost:5173"]
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
else:
    # Allow all origins in development
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.on_event("startup")
async def startup_event():
    """Run startup tasks."""
    logger.info("Starting application...")
    
    # Create database tables if they don't exist
    create_tables()
    
    # Initialize location cache in background
    asyncio.create_task(initialize_location_cache())

@app.on_event("startup")
@repeat_every(seconds=60 * 60)  # 1 hour
def refresh_location_cache_hourly() -> None:
    """
    Refresh location cache every hour to keep it up to date.
    This runs as a background task using the repeat_every decorator.
    """
    logger.info("Running hourly location cache refresh...")
    try:
        update_location_mappings_task()
        logger.info("Hourly location cache refresh completed successfully")
    except Exception as e:
        logger.error(f"Error in hourly location cache refresh: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """Run shutdown tasks."""
    logger.info("Shutting down application...")

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to WebScraperTracker API. Visit /api/v1/docs for documentation."}

@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {"status": "ok"}

def create_tables():
    """Create database tables."""
    try:
        # Create database tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created")
    except SQLAlchemyError as e:
        logger.error(f"Error creating database tables: {e}")
        raise

async def initialize_location_cache():
    """Initialize location cache on startup."""
    try:
        logger.info("Initializing location cache...")
        await asyncio.to_thread(update_location_mappings_task)
        logger.info("Location cache initialized")
    except Exception as e:
        logger.error(f"Error initializing location cache: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True) 