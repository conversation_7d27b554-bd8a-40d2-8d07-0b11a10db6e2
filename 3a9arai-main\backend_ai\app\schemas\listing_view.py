from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ListingViewBase(BaseModel):
    listing_id: int
    ip_address: str
    user_agent: Optional[str] = None

class ListingViewCreate(ListingViewBase):
    pass

class ListingViewUpdate(BaseModel):
    viewed_at: Optional[datetime] = None

class ListingViewInDBBase(ListingViewBase):
    id: int
    viewed_at: datetime

    class Config:
        from_attributes = True

class ListingView(ListingViewInDBBase):
    pass

class ListingViewInDB(ListingViewInDBBase):
    pass

class ListingViewResponse(ListingViewInDBBase):
    pass 