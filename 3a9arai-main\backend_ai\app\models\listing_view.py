from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, Foreign<PERSON>ey, DateTime, String, Index
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.session import Base

class ListingView(Base):
    __tablename__ = "listing_views"
    
    id = Column(Integer, primary_key=True, index=True)
    listing_id = Column(Integer, ForeignKey("listings.id"), nullable=False)
    ip_address = Column(String(45), nullable=False)  # IPv6 can be up to 45 characters
    user_agent = Column(String(500), nullable=True)  # Store user agent for better uniqueness
    viewed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Relationships
    listing = relationship("Listing", back_populates="public_views")
    
    # Indexes for efficient querying
    __table_args__ = (
        # Index for querying recent views by listing
        Index('ix_listing_views_listing_viewed_at', 'listing_id', 'viewed_at'),
        # Index for checking unique views (IP + listing combination)
        Index('ix_listing_views_ip_listing', 'ip_address', 'listing_id'),
        # Index for time-based queries
        Index('ix_listing_views_viewed_at', 'viewed_at'),
    ) 