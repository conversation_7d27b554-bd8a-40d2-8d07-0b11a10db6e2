"""Add structured listing fields

Revision ID: 9a144ba4dc17
Revises: 
Create Date: 2025-03-10 15:08:15.897213

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9a144ba4dc17'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('listings', sa.Column('neighborhood', sa.String(length=100), nullable=True))
    op.add_column('listings', sa.Column('gps_coordinates', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('rooms', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('bedrooms', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('bathrooms', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('floor', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('total_floors', sa.Integer(), nullable=True))
    op.add_column('listings', sa.Column('building_age', sa.String(length=50), nullable=True))
    op.add_column('listings', sa.Column('elevator', sa.Boolean(), nullable=True))
    op.add_column('listings', sa.Column('parking', sa.Boolean(), nullable=True))
    op.add_column('listings', sa.Column('parking_type', sa.String(length=50), nullable=True))
    op.add_column('listings', sa.Column('orientation', sa.String(length=50), nullable=True))
    op.add_column('listings', sa.Column('has_balcony', sa.Boolean(), nullable=True))
    op.add_column('listings', sa.Column('balcony_size', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('has_terrace', sa.Boolean(), nullable=True))
    op.add_column('listings', sa.Column('terrace_size', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('has_garden', sa.Boolean(), nullable=True))
    op.add_column('listings', sa.Column('garden_size', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('indoor_features', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('outdoor_features', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('security_features', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('nearby_amenities', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('features', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('images', sa.JSON(), nullable=True))
    op.add_column('listings', sa.Column('contact_info', sa.String(length=100), nullable=True))
    op.add_column('listings', sa.Column('price_per_sqm', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('monthly_fees', sa.Float(), nullable=True))
    op.add_column('listings', sa.Column('scraper', sa.String(length=50), nullable=True))
    op.alter_column('listings', 'price',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('listings', 'price',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.drop_column('listings', 'scraper')
    op.drop_column('listings', 'monthly_fees')
    op.drop_column('listings', 'price_per_sqm')
    op.drop_column('listings', 'contact_info')
    op.drop_column('listings', 'images')
    op.drop_column('listings', 'features')
    op.drop_column('listings', 'nearby_amenities')
    op.drop_column('listings', 'security_features')
    op.drop_column('listings', 'outdoor_features')
    op.drop_column('listings', 'indoor_features')
    op.drop_column('listings', 'garden_size')
    op.drop_column('listings', 'has_garden')
    op.drop_column('listings', 'terrace_size')
    op.drop_column('listings', 'has_terrace')
    op.drop_column('listings', 'balcony_size')
    op.drop_column('listings', 'has_balcony')
    op.drop_column('listings', 'orientation')
    op.drop_column('listings', 'parking_type')
    op.drop_column('listings', 'parking')
    op.drop_column('listings', 'elevator')
    op.drop_column('listings', 'building_age')
    op.drop_column('listings', 'total_floors')
    op.drop_column('listings', 'floor')
    op.drop_column('listings', 'bathrooms')
    op.drop_column('listings', 'bedrooms')
    op.drop_column('listings', 'rooms')
    op.drop_column('listings', 'gps_coordinates')
    op.drop_column('listings', 'neighborhood')
    # ### end Alembic commands ###
