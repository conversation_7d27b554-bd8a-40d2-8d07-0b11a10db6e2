import logging
import traceback
import json
from sqlalchemy.orm import Session

from app import crud, schemas
from app.db.session import Base, engine
from app.core.config import settings
from app.core.auth import get_password_hash
from app.models.user import User, Role

# Import all models to ensure they are registered with SQLAlchemy
from app.models import Listing, ScraperActivity

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    """
    Initialize the database with default data.
    """
    try:
        # Create tables
        logger.info("Creating database tables...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # Create default roles if they don't exist
        create_default_roles(db)
        
        # Create admin user if it doesn't exist
        create_admin_user(db)
        
        logger.info("Database initialization complete!")
    except Exception as e:
        logger.error(f"An error occurred during database initialization: {e}")
        traceback.print_exc()
        raise


def create_default_roles(db: Session) -> None:
    """
    Create default roles.
    """
    # User role
    user_role = db.query(Role).filter(Role.name == settings.DEFAULT_USER_ROLE).first()
    if not user_role:
        user_role = Role(
            name=settings.DEFAULT_USER_ROLE,
            description="Default user role with basic permissions",
            permissions=json.dumps(settings.DEFAULT_USER_PERMISSIONS)
        )
        db.add(user_role)
        logger.info(f"Created default '{settings.DEFAULT_USER_ROLE}' role")
    
    # Admin role
    admin_role = db.query(Role).filter(Role.name == "admin").first()
    if not admin_role:
        admin_role = Role(
            name="admin",
            description="Administrator role with all permissions",
            permissions=json.dumps(settings.ADMIN_PERMISSIONS)
        )
        db.add(admin_role)
        logger.info("Created 'admin' role")
    
    db.commit()


def create_admin_user(db: Session) -> None:
    """
    Create admin user if it doesn't exist.
    """
    admin_user = db.query(User).filter(User.username == settings.ADMIN_USERNAME).first()
    if not admin_user:
        # Get admin role
        admin_role = db.query(Role).filter(Role.name == "admin").first()
        if not admin_role:
            logger.error("Admin role not found. Cannot create admin user.")
            return
        
        # Create admin user
        admin_user = User(
            username=settings.ADMIN_USERNAME,
            email=f"{settings.ADMIN_USERNAME}@example.com",
            hashed_password=get_password_hash(settings.ADMIN_PASSWORD),
            full_name="Administrator",
            is_superuser=True,
        )
        
        # Add admin role to user
        admin_user.roles.append(admin_role)
        
        db.add(admin_user)
        db.commit()
        logger.info(f"Created admin user: {settings.ADMIN_USERNAME}")
    else:
        logger.info(f"Admin user already exists: {settings.ADMIN_USERNAME}")


def seed_sample_data(db: Session) -> None:
    """Seed the database with sample data for development."""
    try:
        # Check if we already have listings
        existing_listings = db.query(Listing).count()
        if existing_listings > 0:
            logger.info(f"Database already contains {existing_listings} listings. Skipping seed.")
            return

        # Sample listings
        sample_listings = [
            {
                "title": "Appartement moderne avec vue sur mer",
                "description": "Bel appartement avec vue panoramique sur la mer, entièrement rénové.",
                "price": 1200000,
                "city": "Casablanca",
                "location": "Ain Diab",
                "property_type": "Appartement",
                "size": 120,
                "image_url": "https://placehold.co/600x400?text=Appartement+1",
                "website_source": "Sarouty",
                "url": "https://example.com/listing/1",
                "date_posted": "31/01/2025",
            },
            {
                "title": "Villa de luxe avec piscine",
                "description": "Magnifique villa avec piscine privée et jardin spacieux.",
                "price": 4500000,
                "city": "Marrakech",
                "location": "Palmeraie",
                "property_type": "Villa",
                "size": 350,
                "image_url": "https://placehold.co/600x400?text=Villa+2",
                "website_source": "Mubawab",
                "url": "https://example.com/listing/2",
                "date_posted": "30/01/2025",
            },
            {
                "title": "Appartement neuf au centre-ville",
                "description": "Appartement neuf avec finitions haut de gamme au cœur de la ville.",
                "price": 950000,
                "city": "Rabat",
                "location": "Agdal",
                "property_type": "Appartement",
                "size": 85,
                "image_url": "https://placehold.co/600x400?text=Appartement+3",
                "website_source": "Agenz",
                "url": "https://example.com/listing/3",
                "date_posted": "29/01/2025",
            },
        ]

        for listing_data in sample_listings:
            listing_in = schemas.ListingCreate(**listing_data)
            crud.listing.create(db=db, obj_in=listing_in)

        logger.info(f"Added {len(sample_listings)} sample listings")

        # Sample scraper activities
        sample_activities = [
            {
                "scraper_name": "Sarouty",
                "status": "success",
                "message": "Scraping completed successfully. Added 15 new listings.",
                "listings_added": 15,
                "start_time": "2025-01-31T10:00:00",
                "end_time": "2025-01-31T10:05:23",
            },
            {
                "scraper_name": "Mubawab",
                "status": "success",
                "message": "Scraping completed successfully. Added 8 new listings.",
                "listings_added": 8,
                "start_time": "2025-01-31T10:10:00",
                "end_time": "2025-01-31T10:14:45",
            },
            {
                "scraper_name": "Agenz",
                "status": "error",
                "message": "Error during scraping: Connection timeout after 30 seconds.",
                "listings_added": 0,
                "start_time": "2025-01-31T10:20:00",
                "end_time": "2025-01-31T10:20:30",
            },
        ]

        for activity_data in sample_activities:
            activity_in = schemas.ScraperActivityCreate(**activity_data)
            crud.scraper_activity.create(db=db, obj_in=activity_in)

        logger.info(f"Added {len(sample_activities)} sample scraper activities")
    except Exception as e:
        logger.error(f"Error seeding database: {e}")
        traceback.print_exc()
        db.rollback()
        raise


def main() -> None:
    """
    Main entry point for database initialization.
    """
    from app.db.session import SessionLocal
    
    logger.info("Initializing database...")
    
    try:
        db = SessionLocal()
        init_db(db)
    except Exception as e:
        logger.error(f"An error occurred during database initialization: {e}")
        traceback.print_exc()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main() 